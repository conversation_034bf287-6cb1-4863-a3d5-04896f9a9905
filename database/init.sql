-- 游戏平台数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS games CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE games;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    agent_id VARCHAR(36),
    club_id VARCHAR(36),
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_agent_id (agent_id),
    INDEX idx_club_id (club_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- 代理表
CREATE TABLE IF NOT EXISTS agents (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    commission DECIMAL(5,4) DEFAULT 0.0500,
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- 俱乐部表
CREATE TABLE IF NOT EXISTS clubs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    agent_id VARCHAR(36) NOT NULL,
    description TEXT,
    max_members INT DEFAULT 1000,
    current_members INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- 管理员表
CREATE TABLE IF NOT EXISTS admins (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('superadmin', 'admin', 'operator') DEFAULT 'admin',
    permissions JSON,
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- 交易记录表
CREATE TABLE IF NOT EXISTS transactions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36),
    agent_id VARCHAR(36),
    type ENUM('deposit', 'withdraw', 'bet', 'win', 'commission', 'transfer') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    description TEXT,
    reference_id VARCHAR(36),
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB;

-- 游戏记录表
CREATE TABLE IF NOT EXISTS game_records (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    game_type ENUM('sangong', 'niuniu') NOT NULL,
    room_id VARCHAR(36) NOT NULL,
    round_number INT NOT NULL,
    players JSON NOT NULL,
    game_data JSON NOT NULL,
    result JSON NOT NULL,
    total_pot DECIMAL(15,2) NOT NULL,
    started_at TIMESTAMP NOT NULL,
    ended_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_game_type (game_type),
    INDEX idx_room_id (room_id),
    INDEX idx_started_at (started_at)
) ENGINE=InnoDB;

-- 游戏参与记录表
CREATE TABLE IF NOT EXISTS game_participations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    game_record_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    seat_position INT NOT NULL,
    bet_amount DECIMAL(15,2) NOT NULL,
    win_amount DECIMAL(15,2) DEFAULT 0.00,
    cards JSON,
    hand_result JSON,
    is_winner BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (game_record_id) REFERENCES game_records(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_game_record_id (game_record_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB;

-- 旁注记录表
CREATE TABLE IF NOT EXISTS side_wagers (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    game_record_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    target_seat INT NOT NULL,
    bet_amount DECIMAL(15,2) NOT NULL,
    win_amount DECIMAL(15,2) DEFAULT 0.00,
    is_winner BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (game_record_id) REFERENCES game_records(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_game_record_id (game_record_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB;

-- 插入默认管理员账户
INSERT INTO admins (username, email, password_hash, role, status) VALUES 
('superadmin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', 'superadmin', 'active')
ON DUPLICATE KEY UPDATE username=username;

-- 插入测试代理账户
INSERT INTO agents (username, email, password_hash, balance, status) VALUES 
('testagent', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', 10000.00, 'active')
ON DUPLICATE KEY UPDATE username=username;

-- 获取代理ID并插入测试俱乐部
SET @agent_id = (SELECT id FROM agents WHERE username = 'testagent');
INSERT INTO clubs (name, agent_id, description, max_members, current_members, status) VALUES 
('测试俱乐部', @agent_id, '这是一个测试俱乐部', 1000, 0, 'active')
ON DUPLICATE KEY UPDATE name=name;

-- 插入测试用户账户
SET @club_id = (SELECT id FROM clubs WHERE name = '测试俱乐部');
INSERT INTO users (username, email, password_hash, balance, agent_id, club_id, status) VALUES 
('testuser', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', 1000.00, @agent_id, @club_id, 'active')
ON DUPLICATE KEY UPDATE username=username;

-- 更新俱乐部成员数
UPDATE clubs SET current_members = (
    SELECT COUNT(*) FROM users WHERE club_id = clubs.id
) WHERE id = @club_id;
