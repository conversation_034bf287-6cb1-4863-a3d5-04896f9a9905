-- 创建游戏房间表
CREATE TABLE IF NOT EXISTS game_rooms (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    game_type ENUM('sangong', 'niuniu') NOT NULL UNIQUE,
    room_name VARCHAR(100) NOT NULL,
    status ENUM('waiting', 'playing', 'paused') DEFAULT 'waiting',
    current_round INT DEFAULT 0,
    max_players INT DEFAULT 10,
    min_bet DECIMAL(10,2) DEFAULT 10.00,
    max_bet DECIMAL(10,2) DEFAULT 1000.00,
    seats JSON DEFAULT '[]',
    spectators JSON DEFAULT '[]',
    game_state JSON DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_game_type (game_type),
    INDEX idx_status (status)
);

-- 插入默认游戏房间
INSERT INTO game_rooms (game_type, room_name, status, max_players, min_bet, max_bet) VALUES
('sangong', '三公房间', 'waiting', 10, 10.00, 1000.00),
('niuniu', '牛牛房间', 'waiting', 10, 10.00, 1000.00)
ON DUPLICATE KEY UPDATE
    room_name = VALUES(room_name),
    max_players = VALUES(max_players),
    min_bet = VALUES(min_bet),
    max_bet = VALUES(max_bet);
