const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: '127.0.0.1',
      user: 'root',
      password: 'root',
      database: 'games',
      multipleStatements: true
    });

    console.log('✅ 数据库连接成功');

    // 读取迁移脚本
    const migrationPath = path.join(__dirname, 'migrations', 'add-club-features.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('🔄 执行俱乐部功能迁移...');

    // 执行迁移脚本
    await connection.execute(migrationSQL);

    console.log('✅ 俱乐部功能迁移完成！');
    console.log('📊 已添加以下功能：');
    console.log('   - clubs表添加club_code字段（6位数字俱乐部ID）');
    console.log('   - clubs表添加auto_join字段（自动加入开关）');
    console.log('   - clubs表添加join_approval_required字段（是否需要审核）');
    console.log('   - 创建club_applications表（俱乐部申请审核表）');
    console.log('   - 创建user_clubs表（用户俱乐部关系表）');
    console.log('   - 添加自动更新成员数的触发器');
    console.log('   - 插入测试数据');

    // 验证迁移结果
    console.log('\n🔍 验证迁移结果：');
    
    const [clubs] = await connection.execute('SELECT id, name, club_code, auto_join, join_approval_required, current_members FROM clubs');
    console.log('俱乐部列表：');
    clubs.forEach(club => {
      console.log(`   - ${club.name} (代码: ${club.club_code}, 自动加入: ${club.auto_join ? '是' : '否'}, 需要审核: ${club.join_approval_required ? '是' : '否'}, 成员数: ${club.current_members})`);
    });

    const [applications] = await connection.execute('SELECT COUNT(*) as count FROM club_applications');
    console.log(`俱乐部申请表记录数: ${applications[0].count}`);

    const [userClubs] = await connection.execute('SELECT COUNT(*) as count FROM user_clubs');
    console.log(`用户俱乐部关系表记录数: ${userClubs[0].count}`);

  } catch (error) {
    console.error('❌ 迁移失败：', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

runMigration();
