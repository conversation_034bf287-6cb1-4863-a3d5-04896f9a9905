-- 俱乐部功能增强迁移脚本
-- 添加俱乐部ID、自动加入开关、申请审核表等功能

USE games;

-- 1. 修改clubs表，添加俱乐部ID和自动加入开关
ALTER TABLE clubs
ADD COLUMN club_code VARCHAR(6) UNIQUE COMMENT '俱乐部6位数字ID',
ADD COLUMN auto_join BOOLEAN DEFAULT FALSE COMMENT '是否自动加入俱乐部',
ADD COLUMN join_approval_required BOOLEAN DEFAULT TRUE COMMENT '是否需要审核加入申请';

ALTER TABLE clubs ADD INDEX idx_club_code (club_code);

-- 2. 创建俱乐部申请表
CREATE TABLE IF NOT EXISTS club_applications (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL COMMENT '申请用户ID',
    club_id VARCHAR(36) NOT NULL COMMENT '申请加入的俱乐部ID',
    club_code VARCHAR(6) NOT NULL COMMENT '俱乐部代码',
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT '申请状态',
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    processed_by VARCHAR(36) NULL COMMENT '处理人ID（代理ID）',
    rejection_reason TEXT NULL COMMENT '拒绝原因',
    notes TEXT NULL COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (club_id) REFERENCES clubs(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES agents(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_club_id (club_id),
    INDEX idx_club_code (club_code),
    INDEX idx_status (status),
    INDEX idx_applied_at (applied_at),
    INDEX idx_processed_at (processed_at),
    
    -- 确保用户不能重复申请同一个俱乐部
    UNIQUE KEY unique_user_club_application (user_id, club_id)
) ENGINE=InnoDB COMMENT='俱乐部申请表';

-- 3. 创建用户俱乐部关系表（用于支持用户可能属于多个俱乐部的未来扩展）
CREATE TABLE IF NOT EXISTS user_clubs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    club_id VARCHAR(36) NOT NULL COMMENT '俱乐部ID',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '在俱乐部中的状态',
    is_primary BOOLEAN DEFAULT TRUE COMMENT '是否为主要俱乐部',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (club_id) REFERENCES clubs(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_club_id (club_id),
    INDEX idx_joined_at (joined_at),
    INDEX idx_status (status),
    
    -- 确保用户在同一个俱乐部只有一条记录
    UNIQUE KEY unique_user_club (user_id, club_id)
) ENGINE=InnoDB COMMENT='用户俱乐部关系表';

-- 4. 为现有俱乐部生成随机的6位数字俱乐部代码
UPDATE clubs 
SET club_code = LPAD(FLOOR(RAND() * 1000000), 6, '0')
WHERE club_code IS NULL;

-- 5. 为现有用户创建俱乐部关系记录
INSERT INTO user_clubs (user_id, club_id, joined_at, status, is_primary)
SELECT id, club_id, created_at, 'active', TRUE
FROM users 
WHERE club_id IS NOT NULL
ON DUPLICATE KEY UPDATE user_id = user_id;

-- 6. 更新俱乐部成员数统计
UPDATE clubs 
SET current_members = (
    SELECT COUNT(*) 
    FROM user_clubs 
    WHERE club_id = clubs.id AND status = 'active'
);

-- 7. 添加触发器来自动更新俱乐部成员数
DELIMITER $$

CREATE TRIGGER update_club_members_after_insert
AFTER INSERT ON user_clubs
FOR EACH ROW
BEGIN
    UPDATE clubs 
    SET current_members = (
        SELECT COUNT(*) 
        FROM user_clubs 
        WHERE club_id = NEW.club_id AND status = 'active'
    )
    WHERE id = NEW.club_id;
END$$

CREATE TRIGGER update_club_members_after_update
AFTER UPDATE ON user_clubs
FOR EACH ROW
BEGIN
    -- 更新新俱乐部成员数
    UPDATE clubs 
    SET current_members = (
        SELECT COUNT(*) 
        FROM user_clubs 
        WHERE club_id = NEW.club_id AND status = 'active'
    )
    WHERE id = NEW.club_id;
    
    -- 如果俱乐部发生变化，也更新旧俱乐部成员数
    IF OLD.club_id != NEW.club_id THEN
        UPDATE clubs 
        SET current_members = (
            SELECT COUNT(*) 
            FROM user_clubs 
            WHERE club_id = OLD.club_id AND status = 'active'
        )
        WHERE id = OLD.club_id;
    END IF;
END$$

CREATE TRIGGER update_club_members_after_delete
AFTER DELETE ON user_clubs
FOR EACH ROW
BEGIN
    UPDATE clubs 
    SET current_members = (
        SELECT COUNT(*) 
        FROM user_clubs 
        WHERE club_id = OLD.club_id AND status = 'active'
    )
    WHERE id = OLD.club_id;
END$$

DELIMITER ;

-- 8. 插入一些测试数据
-- 为测试代理的俱乐部设置俱乐部代码和自动加入
UPDATE clubs 
SET club_code = '123456', 
    auto_join = TRUE, 
    join_approval_required = FALSE 
WHERE name = '测试俱乐部';

-- 创建另一个需要审核的测试俱乐部
INSERT INTO agents (id, username, email, password_hash, balance, status) VALUES 
('agent-test-002', 'testagent2', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', 5000.00, 'active')
ON DUPLICATE KEY UPDATE username=username;

INSERT INTO clubs (id, agent_id, name, description, club_code, auto_join, join_approval_required, max_members, status) VALUES 
('club-test-002', 'agent-test-002', '精英俱乐部', '需要审核的高端俱乐部', '654321', FALSE, TRUE, 500, 'active')
ON DUPLICATE KEY UPDATE name=name;

COMMIT;
