const mysql = require('mysql2/promise');

async function runMigration() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: '127.0.0.1',
      user: 'root',
      password: 'root',
      database: 'games'
    });

    console.log('✅ 数据库连接成功');

    console.log('🔄 执行俱乐部功能迁移...');

    // 1. 修改clubs表，添加俱乐部ID和自动加入开关
    try {
      await connection.execute(`
        ALTER TABLE clubs 
        ADD COLUMN club_code VARCHAR(6) UNIQUE,
        ADD COLUMN auto_join BOOLEAN DEFAULT FALSE,
        ADD COLUMN join_approval_required BOOLEAN DEFAULT TRUE
      `);
      console.log('✅ clubs表字段添加成功');
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('⚠️ clubs表字段已存在，跳过');
      } else {
        throw error;
      }
    }

    // 2. 添加索引
    try {
      await connection.execute('ALTER TABLE clubs ADD INDEX idx_club_code (club_code)');
      console.log('✅ clubs表索引添加成功');
    } catch (error) {
      if (error.message.includes('Duplicate key name')) {
        console.log('⚠️ clubs表索引已存在，跳过');
      } else {
        throw error;
      }
    }

    // 3. 创建俱乐部申请表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS club_applications (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        club_id VARCHAR(36) NOT NULL,
        club_code VARCHAR(6) NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed_at TIMESTAMP NULL,
        processed_by VARCHAR(36) NULL,
        rejection_reason TEXT NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_user_id (user_id),
        INDEX idx_club_id (club_id),
        INDEX idx_club_code (club_code),
        INDEX idx_status (status),
        INDEX idx_applied_at (applied_at),
        INDEX idx_processed_at (processed_at),
        
        UNIQUE KEY unique_user_club_application (user_id, club_id)
      ) ENGINE=InnoDB
    `);
    console.log('✅ club_applications表创建成功');

    // 4. 创建用户俱乐部关系表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_clubs (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        club_id VARCHAR(36) NOT NULL,
        joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
        is_primary BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_user_id (user_id),
        INDEX idx_club_id (club_id),
        INDEX idx_joined_at (joined_at),
        INDEX idx_status (status),
        
        UNIQUE KEY unique_user_club (user_id, club_id)
      ) ENGINE=InnoDB
    `);
    console.log('✅ user_clubs表创建成功');

    // 5. 为现有俱乐部生成随机的6位数字俱乐部代码
    await connection.execute(`
      UPDATE clubs 
      SET club_code = LPAD(FLOOR(RAND() * 1000000), 6, '0')
      WHERE club_code IS NULL
    `);
    console.log('✅ 俱乐部代码生成成功');

    // 6. 为现有用户创建俱乐部关系记录
    await connection.execute(`
      INSERT INTO user_clubs (user_id, club_id, joined_at, status, is_primary)
      SELECT id, club_id, created_at, 'active', TRUE
      FROM users 
      WHERE club_id IS NOT NULL
      ON DUPLICATE KEY UPDATE user_id = user_id
    `);
    console.log('✅ 用户俱乐部关系记录创建成功');

    // 7. 更新俱乐部成员数统计
    await connection.execute(`
      UPDATE clubs 
      SET current_members = (
        SELECT COUNT(*) 
        FROM user_clubs 
        WHERE club_id = clubs.id AND status = 'active'
      )
    `);
    console.log('✅ 俱乐部成员数统计更新成功');

    // 8. 设置测试数据
    await connection.execute(`
      UPDATE clubs 
      SET club_code = '123456', 
          auto_join = TRUE, 
          join_approval_required = FALSE 
      WHERE name = '测试俱乐部'
    `);
    console.log('✅ 测试俱乐部设置完成');

    console.log('\n✅ 俱乐部功能迁移完成！');

    // 验证迁移结果
    console.log('\n🔍 验证迁移结果：');
    
    const [clubs] = await connection.execute('SELECT id, name, club_code, auto_join, join_approval_required, current_members FROM clubs');
    console.log('俱乐部列表：');
    clubs.forEach(club => {
      console.log(`   - ${club.name} (代码: ${club.club_code}, 自动加入: ${club.auto_join ? '是' : '否'}, 需要审核: ${club.join_approval_required ? '是' : '否'}, 成员数: ${club.current_members})`);
    });

    const [applications] = await connection.execute('SELECT COUNT(*) as count FROM club_applications');
    console.log(`俱乐部申请表记录数: ${applications[0].count}`);

    const [userClubs] = await connection.execute('SELECT COUNT(*) as count FROM user_clubs');
    console.log(`用户俱乐部关系表记录数: ${userClubs[0].count}`);

  } catch (error) {
    console.error('❌ 迁移失败：', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

runMigration();
