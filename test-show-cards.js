const io = require('socket.io-client');

// 测试开牌功能的完整流程
async function testShowCardsFlow() {
  console.log('🧪 开始测试开牌功能...\n');

  // 创建两个测试用户
  const user1Socket = io('http://localhost:3002', {
    auth: {
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5NWM0ZjkxYy01MTFlLTExZjAtYjVhNS0wMjQyYWMxNDAwMDIiLCJ1c2VyVHlwZSI6InVzZXIiLCJ1c2VybmFtZSI6InRlc3R1c2VyIiwic2Vzc2lvbklkIjoiNzlmMjEyOTYtZWQ3My00MGZjLThhNmQtZGJiZGU2OGVkMThkIiwiaWF0IjoxNzUxODE3OTUyLCJleHAiOjE3NTI0MjI3NTIsImF1ZCI6ImdhbWVzLXVzZXJzIiwiaXNzIjoiZ2FtZXMtcGxhdGZvcm0ifQ.IJuazEguf_iPnHkMBgr0k75a2jnxyn7FjbdYA8FVo2k'
    }
  });

  const user2Socket = io('http://localhost:3002', {
    auth: {
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5NWM0ZjkxYy01MTFlLTExZjAtYjVhNS0wMjQyYWMxNDAwMDMiLCJ1c2VyVHlwZSI6InVzZXIiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInNlc3Npb25JZCI6IjY2MjYxM2ZmLThkYzktNGZmOC1hYjdlLTVkMzZlMzI0OTJlNyIsImlhdCI6MTc1MTgxNzk1OSwiZXhwIjoxNzUyNDIyNzU5LCJhdWQiOiJnYW1lcy11c2VycyIsImlzcyI6ImdhbWVzLXBsYXRmb3JtIn0.GgYcfC3nIXkRQsGMgnw2ZpVA8Pks9XPesv5txUx5Fwc'
    }
  });

  // 等待连接
  await Promise.all([
    new Promise(resolve => user1Socket.on('connect', resolve)),
    new Promise(resolve => user2Socket.on('connect', resolve))
  ]);

  console.log('✅ 两个用户已连接');

  // 设置事件监听
  user1Socket.on('room_update', (data) => {
    console.log('👤 User1 收到房间更新:', {
      status: data.status,
      seatedPlayers: data.seats.filter(s => s.userId).length
    });
  });

  user2Socket.on('room_update', (data) => {
    console.log('👤 User2 收到房间更新:', {
      status: data.status,
      seatedPlayers: data.seats.filter(s => s.userId).length
    });
  });

  user1Socket.on('deal_cards', (data) => {
    console.log('🃏 User1 收到发牌:', data.cards?.length || 0, '张牌');
  });

  user2Socket.on('deal_cards', (data) => {
    console.log('🃏 User2 收到发牌:', data.cards?.length || 0, '张牌');
  });

  user1Socket.on('show_cards', (data) => {
    console.log('🎴 User1 收到开牌事件:', {
      username: data.username,
      position: data.position,
      cards: data.cards.map(card => `${card.rank}${card.suit}`)
    });
  });

  user2Socket.on('show_cards', (data) => {
    console.log('🎴 User2 收到开牌事件:', {
      username: data.username,
      position: data.position,
      cards: data.cards.map(card => `${card.rank}${card.suit}`)
    });
  });

  user1Socket.on('notification', (data) => {
    console.log('📢 User1 通知:', data.title, '-', data.message);
  });

  user2Socket.on('notification', (data) => {
    console.log('📢 User2 通知:', data.title, '-', data.message);
  });

  user1Socket.on('error', (data) => {
    console.log('❌ User1 错误:', data);
  });

  user2Socket.on('error', (data) => {
    console.log('❌ User2 错误:', data);
  });

  try {
    // 步骤1: 加入房间
    console.log('\n📍 步骤1: 加入房间');
    user1Socket.emit('join_room', { gameType: 'sangong' });
    user2Socket.emit('join_room', { gameType: 'sangong' });
    await sleep(1000);

    // 步骤2: 坐下
    console.log('\n📍 步骤2: 用户坐下');
    user1Socket.emit('sit_down', { seatNumber: 1 });
    await sleep(500);
    user2Socket.emit('sit_down', { seatNumber: 2 });
    await sleep(2000);

    // 步骤3: 等待自动开始游戏
    console.log('\n📍 步骤3: 等待自动开始游戏 (5秒倒计时)');
    await sleep(6000);

    // 步骤4: 下注
    console.log('\n📍 步骤4: 用户下注');
    user1Socket.emit('place_bet', { amount: 50 });
    await sleep(500);
    user2Socket.emit('place_bet', { amount: 100 });
    await sleep(3000);

    // 步骤5: 等待发牌
    console.log('\n📍 步骤5: 等待发牌阶段');
    await sleep(5000);

    // 步骤6: 开牌测试
    console.log('\n📍 步骤6: 测试开牌功能');
    console.log('🎴 User1 开牌...');
    user1Socket.emit('show_cards');
    await sleep(2000);

    console.log('🎴 User2 开牌...');
    user2Socket.emit('show_cards');
    await sleep(3000);

    // 步骤7: 等待结算
    console.log('\n📍 步骤7: 等待游戏结算');
    await sleep(5000);

    console.log('\n✅ 开牌功能测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  } finally {
    // 清理连接
    user1Socket.disconnect();
    user2Socket.disconnect();
    console.log('\n🔌 连接已断开');
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行测试
testShowCardsFlow().catch(console.error);
