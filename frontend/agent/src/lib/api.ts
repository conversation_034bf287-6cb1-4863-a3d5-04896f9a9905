import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse, PaginationParams } from '@/types';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          this.removeToken();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('agent_token');
    }
    return null;
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('agent_token', token);
    }
  }

  private removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('agent_token');
    }
  }

  // 代理认证相关
  async login(username: string, password: string) {
    const response = await this.client.post<ApiResponse>('/api/agent/login', {
      username,
      password,
    });
    
    if (response.data.success && response.data.data?.tokens?.accessToken) {
      this.setToken(response.data.data.tokens.accessToken);
    }
    
    return response.data;
  }

  async register(data: any) {
    const response = await this.client.post<ApiResponse>('/agent/register', data);
    return response.data;
  }

  async logout() {
    try {
      await this.client.post('/agent/logout');
    } finally {
      this.removeToken();
    }
  }

  async getProfile() {
    const response = await this.client.get<ApiResponse>('/agent/profile');
    return response.data;
  }

  // 用户管理
  async getUsers(params: PaginationParams) {
    const response = await this.client.get<ApiResponse>('/agent/users', { params });
    return response.data;
  }

  async getUserById(id: string) {
    const response = await this.client.get<ApiResponse>(`/users/${id}`);
    return response.data;
  }

  // 充值提现
  async depositToUser(userId: string, amount: number, description?: string) {
    const response = await this.client.post<ApiResponse>('/agent/deposit', {
      userId,
      amount,
      description,
    });
    return response.data;
  }

  async withdrawFromUser(userId: string, amount: number, description?: string) {
    const response = await this.client.post<ApiResponse>('/agent/withdraw', {
      userId,
      amount,
      description,
    });
    return response.data;
  }

  // 交易记录
  async getTransactions(params: PaginationParams & { type?: string; userId?: string }) {
    const response = await this.client.get<ApiResponse>('/transactions', { params });
    return response.data;
  }

  async getTransaction(id: string) {
    const response = await this.client.get<ApiResponse>(`/transactions/${id}`);
    return response.data;
  }

  // 统计数据
  async getAgentStats() {
    const response = await this.client.get<ApiResponse>('/agent/stats');
    return response.data;
  }

  async getUserStats(params?: { startDate?: string; endDate?: string }) {
    const response = await this.client.get<ApiResponse>('/agent/user-stats', { params });
    return response.data;
  }

  async getRevenueStats(params?: { startDate?: string; endDate?: string; groupBy?: string }) {
    const response = await this.client.get<ApiResponse>('/agent/revenue-stats', { params });
    return response.data;
  }

  // 俱乐部管理
  async updateClub(data: any) {
    const response = await this.client.patch<ApiResponse>('/agent/club', data);
    return response.data;
  }

  async getClubMembers(params: PaginationParams) {
    const response = await this.client.get<ApiResponse>('/agent/club/members', { params });
    return response.data;
  }

  // 代理设置
  async updateSettings(data: any) {
    const response = await this.client.patch<ApiResponse>('/agent/settings', data);
    return response.data;
  }

  async getSettings() {
    const response = await this.client.get<ApiResponse>('/agent/settings');
    return response.data;
  }

  // 实时数据
  async getOnlineUsers() {
    const response = await this.client.get<ApiResponse>('/agent/online-users');
    return response.data;
  }

  async getActiveGames() {
    const response = await this.client.get<ApiResponse>('/agent/active-games');
    return response.data;
  }
}

export const apiClient = new ApiClient();
export default apiClient;
