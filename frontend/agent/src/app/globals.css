@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.card-header {
  @apply border-b border-gray-200 pb-4 mb-4;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
  @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-primary-500;
}

.btn-success {
  @apply text-white bg-success-600 hover:bg-success-700 focus:ring-success-500;
}

.btn-warning {
  @apply text-white bg-warning-600 hover:bg-warning-700 focus:ring-warning-500;
}

.btn-danger {
  @apply text-white bg-danger-600 hover:bg-danger-700 focus:ring-danger-500;
}

/* 表单样式 */
.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-error {
  @apply text-sm text-danger-600 mt-1;
}

/* 状态指示器 */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-active {
  @apply bg-success-100 text-success-800;
}

.status-inactive {
  @apply bg-gray-100 text-gray-800;
}

.status-banned {
  @apply bg-danger-100 text-danger-800;
}

.status-pending {
  @apply bg-warning-100 text-warning-800;
}

/* 表格样式 */
.table-container {
  @apply overflow-x-auto shadow-sm border border-gray-200 rounded-lg;
}

.table-container table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-container thead {
  @apply bg-gray-50;
}

.table-container th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-container td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table-container tbody tr:nth-child(even) {
  @apply bg-gray-50;
}

.table-container tbody tr:hover {
  @apply bg-gray-100;
}
