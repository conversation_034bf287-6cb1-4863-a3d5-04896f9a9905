'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import AgentLayout from '@/components/AgentLayout'

interface Transaction {
  id: string
  user_id: string
  username: string
  type: string
  amount: number
  balance_before: number
  balance_after: number
  status: string
  description: string
  created_at: string
}

interface DepositForm {
  userId: string
  username: string
  amount: number
  description: string
}

interface WithdrawForm {
  userId: string
  username: string
  amount: number
  description: string
}

export default function TransactionsPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [typeFilter, setTypeFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  // 充值提现相关状态
  const [showDepositModal, setShowDepositModal] = useState(false)
  const [showWithdrawModal, setShowWithdrawModal] = useState(false)
  const [depositForm, setDepositForm] = useState<DepositForm>({
    userId: '',
    username: '',
    amount: 0,
    description: ''
  })
  const [withdrawForm, setWithdrawForm] = useState<WithdrawForm>({
    userId: '',
    username: '',
    amount: 0,
    description: ''
  })
  const [actionLoading, setActionLoading] = useState(false)
  const [users, setUsers] = useState<any[]>([])

  const router = useRouter()

  useEffect(() => {
    const token = localStorage.getItem('agent_token')
    if (!token) {
      router.push('/login')
      return
    }

    fetchTransactions()
    fetchUsers()
  }, [router, pagination.page, typeFilter, statusFilter])

  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('agent_token')
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        type: typeFilter,
        status: statusFilter,
        start_date: dateRange.start,
        end_date: dateRange.end
      })

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/agent/transactions?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setTransactions(data.data.transactions)
          setPagination(prev => ({
            ...prev,
            total: data.data.pagination.total,
            totalPages: data.data.pagination.totalPages
          }))
        }
      }
    } catch (error) {
      console.error('获取交易记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getTransactionTypeName = (type: string) => {
    const typeMap = {
      deposit: '充值',
      withdraw: '提现',
      bet: '下注',
      win: '赢取',
      commission: '佣金',
      transfer: '转账',
      adjustment: '余额调整',
      bonus: '奖励',
      penalty: '扣除',
      refund: '退款'
    }
    return typeMap[type as keyof typeof typeMap] || type
  }

  const getTransactionTypeColor = (type: string) => {
    const colorMap = {
      deposit: 'text-green-600',
      withdraw: 'text-red-600',
      bet: 'text-blue-600',
      win: 'text-green-600',
      commission: 'text-purple-600',
      transfer: 'text-gray-600',
      adjustment: 'text-orange-600',
      bonus: 'text-green-600',
      penalty: 'text-red-600',
      refund: 'text-blue-600'
    }
    return colorMap[type as keyof typeof colorMap] || 'text-gray-600'
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      pending: { text: '待处理', class: 'bg-yellow-100 text-yellow-800' },
      completed: { text: '已完成', class: 'bg-green-100 text-green-800' },
      failed: { text: '失败', class: 'bg-red-100 text-red-800' },
      cancelled: { text: '已取消', class: 'bg-gray-100 text-gray-800' }
    }
    const statusInfo = statusMap[status as keyof typeof statusMap] || { text: status, class: 'bg-gray-100 text-gray-800' }
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.class}`}>
        {statusInfo.text}
      </span>
    )
  }

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('agent_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/agent/users?page=1&limit=100&search=&status=active`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setUsers(data.data.users)
        }
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
    }
  }

  // 处理充值
  const handleDeposit = async () => {
    if (!depositForm.userId || depositForm.amount <= 0) {
      alert('请选择用户并输入有效金额')
      return
    }

    setActionLoading(true)
    try {
      const token = localStorage.getItem('agent_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/agent/deposit`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: depositForm.userId,
          amount: depositForm.amount,
          description: depositForm.description || '代理充值'
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        alert('充值成功')
        setShowDepositModal(false)
        setDepositForm({ userId: '', username: '', amount: 0, description: '' })
        fetchTransactions()
      } else {
        alert(data.error || `充值失败 (${response.status})`)
      }
    } catch (error) {
      console.error('充值失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setActionLoading(false)
    }
  }

  // 处理提现
  const handleWithdraw = async () => {
    if (!withdrawForm.userId || withdrawForm.amount <= 0) {
      alert('请选择用户并输入有效金额')
      return
    }

    setActionLoading(true)
    try {
      const token = localStorage.getItem('agent_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/agent/withdraw`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: withdrawForm.userId,
          amount: withdrawForm.amount,
          description: withdrawForm.description || '用户提现'
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        alert('提现成功')
        setShowWithdrawModal(false)
        setWithdrawForm({ userId: '', username: '', amount: 0, description: '' })
        fetchTransactions()
      } else {
        alert(data.error || `提现失败 (${response.status})`)
      }
    } catch (error) {
      console.error('提现失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setActionLoading(false)
    }
  }

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }))
    fetchTransactions()
  }

  return (
    <AgentLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">充值提现</h1>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowDepositModal(true)}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              用户充值
            </button>
            <button
              onClick={() => setShowWithdrawModal(true)}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              用户提现
            </button>
            <div className="text-sm text-gray-500">
              共 {pagination.total} 条记录
            </div>
          </div>
        </div>

        {/* 筛选条件 */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">交易类型</label>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">全部类型</option>
                <option value="deposit">充值</option>
                <option value="withdraw">提现</option>
                <option value="bet">下注</option>
                <option value="win">赢取</option>
                <option value="commission">佣金</option>
                <option value="transfer">转账</option>
                <option value="adjustment">余额调整</option>
                <option value="bonus">奖励</option>
                <option value="penalty">扣除</option>
                <option value="refund">退款</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">全部状态</option>
                <option value="pending">待处理</option>
                <option value="completed">已完成</option>
                <option value="failed">失败</option>
                <option value="cancelled">已取消</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>

          <div className="mt-4 flex justify-end">
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              查询
            </button>
          </div>
        </div>

        {/* 交易记录列表 */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      交易ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用户
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      类型
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      金额
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      余额变化
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      说明
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transactions.map((transaction) => (
                    <tr key={transaction.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                        {transaction.id.slice(0, 8)}...
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.username}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-sm font-medium ${getTransactionTypeColor(transaction.type)}`}>
                          {getTransactionTypeName(transaction.type)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ¥{parseFloat(transaction.amount.toString()).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ¥{parseFloat(transaction.balance_before.toString()).toLocaleString()} → 
                        ¥{parseFloat(transaction.balance_after.toString()).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(transaction.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(transaction.created_at).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                        {transaction.description || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {transactions.length === 0 && !loading && (
                <div className="p-8 text-center text-gray-500">
                  暂无交易记录
                </div>
              )}
            </div>
          )}
        </div>

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between bg-white px-4 py-3 rounded-lg shadow">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                disabled={pagination.page === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                上一页
              </button>
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
                disabled={pagination.page === pagination.totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                下一页
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  显示第 <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> 到{' '}
                  <span className="font-medium">{Math.min(pagination.page * pagination.limit, pagination.total)}</span> 条，
                  共 <span className="font-medium">{pagination.total}</span> 条记录
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                    disabled={pagination.page === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                    {pagination.page} / {pagination.totalPages}
                  </span>
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
                    disabled={pagination.page === pagination.totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    下一页
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* 充值模态框 */}
        {showDepositModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">用户充值</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">选择用户</label>
                    <select
                      value={depositForm.userId}
                      onChange={(e) => {
                        const selectedUser = users.find(u => u.id === e.target.value)
                        setDepositForm(prev => ({
                          ...prev,
                          userId: e.target.value,
                          username: selectedUser?.username || ''
                        }))
                      }}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="">请选择用户</option>
                      {users.map(user => (
                        <option key={user.id} value={user.id}>
                          {user.username} (余额: ¥{parseFloat(user.balance).toLocaleString()})
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">充值金额</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={depositForm.amount}
                      onChange={(e) => setDepositForm(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="请输入充值金额"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">备注</label>
                    <textarea
                      value={depositForm.description}
                      onChange={(e) => setDepositForm(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="请输入充值备注..."
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => setShowDepositModal(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleDeposit}
                    disabled={actionLoading || !depositForm.userId || depositForm.amount <= 0}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                  >
                    {actionLoading ? '充值中...' : '确认充值'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 提现模态框 */}
        {showWithdrawModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">用户提现</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">选择用户</label>
                    <select
                      value={withdrawForm.userId}
                      onChange={(e) => {
                        const selectedUser = users.find(u => u.id === e.target.value)
                        setWithdrawForm(prev => ({
                          ...prev,
                          userId: e.target.value,
                          username: selectedUser?.username || ''
                        }))
                      }}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                    >
                      <option value="">请选择用户</option>
                      {users.map(user => (
                        <option key={user.id} value={user.id}>
                          {user.username} (余额: ¥{parseFloat(user.balance).toLocaleString()})
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">提现金额</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={withdrawForm.amount}
                      onChange={(e) => setWithdrawForm(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      placeholder="请输入提现金额"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">备注</label>
                    <textarea
                      value={withdrawForm.description}
                      onChange={(e) => setWithdrawForm(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      placeholder="请输入提现备注..."
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => setShowWithdrawModal(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleWithdraw}
                    disabled={actionLoading || !withdrawForm.userId || withdrawForm.amount <= 0}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                  >
                    {actionLoading ? '提现中...' : '确认提现'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AgentLayout>
  )
}
