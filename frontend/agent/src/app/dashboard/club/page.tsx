'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import AgentLayout from '@/components/AgentLayout'

interface ClubInfo {
  id: string
  name: string
  description: string
  clubCode: string
  autoJoin: boolean
  joinApprovalRequired: boolean
  maxMembers: number
  currentMembers: number
  status: string
}

interface ClubApplication {
  id: string
  userId: string
  username: string
  clubCode: string
  status: 'pending' | 'approved' | 'rejected'
  appliedAt: string
  processedAt?: string
  rejectionReason?: string
  notes?: string
}

export default function ClubManagePage() {
  const [clubInfo, setClubInfo] = useState<ClubInfo | null>(null)
  const [applications, setApplications] = useState<ClubApplication[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState<'settings' | 'applications'>('settings')
  const router = useRouter()

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    clubCode: '',
    autoJoin: false,
    joinApprovalRequired: true,
    maxMembers: 1000
  })

  useEffect(() => {
    const token = localStorage.getItem('agent_token')
    if (!token) {
      router.push('/login')
      return
    }

    fetchClubInfo()
    fetchApplications()
  }, [router])

  const fetchClubInfo = async () => {
    try {
      const token = localStorage.getItem('agent_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/agent/club`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('agent_token')
          router.push('/login')
          return
        }
        throw new Error('获取俱乐部信息失败')
      }

      const data = await response.json()
      if (data.success) {
        setClubInfo(data.data)
        setFormData({
          name: data.data.name || '',
          description: data.data.description || '',
          clubCode: data.data.clubCode || '',
          autoJoin: data.data.autoJoin || false,
          joinApprovalRequired: data.data.joinApprovalRequired !== false,
          maxMembers: data.data.maxMembers || 1000
        })
      }
    } catch (error) {
      console.error('获取俱乐部信息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchApplications = async () => {
    try {
      const token = localStorage.getItem('agent_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/agent/club/applications`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setApplications(data.data || [])
        }
      }
    } catch (error) {
      console.error('获取申请列表失败:', error)
    }
  }

  const handleSaveSettings = async () => {
    setSaving(true)
    try {
      const token = localStorage.getItem('agent_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/agent/club`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        throw new Error('保存设置失败')
      }

      const data = await response.json()
      if (data.success) {
        setClubInfo(data.data)
        alert('设置保存成功！')
      } else {
        alert(data.error || '保存失败')
      }
    } catch (error) {
      console.error('保存设置失败:', error)
      alert('保存设置失败')
    } finally {
      setSaving(false)
    }
  }

  const handleApplicationAction = async (applicationId: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      const token = localStorage.getItem('agent_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/agent/club/applications/${applicationId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action,
          rejectionReason: reason
        })
      })

      if (!response.ok) {
        throw new Error('处理申请失败')
      }

      const data = await response.json()
      if (data.success) {
        // 刷新申请列表
        fetchApplications()
        fetchClubInfo() // 更新成员数
        alert(action === 'approve' ? '申请已批准' : '申请已拒绝')
      } else {
        alert(data.error || '处理失败')
      }
    } catch (error) {
      console.error('处理申请失败:', error)
      alert('处理申请失败')
    }
  }

  const generateClubCode = () => {
    const code = Math.floor(100000 + Math.random() * 900000).toString()
    setFormData({ ...formData, clubCode: code })
  }

  if (loading) {
    return (
      <AgentLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">正在加载...</p>
          </div>
        </div>
      </AgentLayout>
    )
  }

  return (
    <AgentLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">俱乐部管理</h1>
          <p className="text-gray-600">管理您的俱乐部设置和成员申请</p>
        </div>
        {/* 标签页导航 */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('settings')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'settings'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                俱乐部设置
              </button>
              <button
                onClick={() => setActiveTab('applications')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'applications'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                加入申请
                {applications.filter(app => app.status === 'pending').length > 0 && (
                  <span className="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    {applications.filter(app => app.status === 'pending').length}
                  </span>
                )}
              </button>
            </nav>
          </div>

          <div className="p-6">

            {/* 俱乐部设置标签页 */}
            {activeTab === 'settings' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">俱乐部设置</h3>
                  <p className="text-sm text-gray-500 mb-6">
                    管理您的俱乐部基本信息和加入设置
                  </p>
                </div>
                {/* 基本信息 */}
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h4 className="text-md font-medium text-gray-900 mb-4">基本信息</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        俱乐部名称
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入俱乐部名称"
                      />
                    </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      俱乐部代码 (6位数字)
                    </label>
                    <div className="flex">
                      <input
                        type="text"
                        value={formData.clubCode}
                        onChange={(e) => setFormData({ ...formData, clubCode: e.target.value })}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="123456"
                        maxLength={6}
                      />
                      <button
                        type="button"
                        onClick={generateClubCode}
                        className="px-4 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 text-sm"
                      >
                        随机生成
                      </button>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    俱乐部描述
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入俱乐部描述"
                  />
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大成员数
                  </label>
                  <input
                    type="number"
                    value={formData.maxMembers}
                    onChange={(e) => setFormData({ ...formData, maxMembers: parseInt(e.target.value) || 1000 })}
                    className="w-full md:w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="1"
                    max="10000"
                  />
                </div>
              </div>

              {/* 加入设置 */}
              <div className="border-t pt-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">加入设置</h4>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="autoJoin"
                      checked={formData.autoJoin}
                      onChange={(e) => setFormData({ 
                        ...formData, 
                        autoJoin: e.target.checked,
                        joinApprovalRequired: !e.target.checked
                      })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="autoJoin" className="ml-2 block text-sm text-gray-900">
                      自动加入俱乐部
                    </label>
                  </div>
                  <p className="text-sm text-gray-500 ml-6">
                    开启后，用户输入正确的俱乐部代码即可自动加入，无需审核
                  </p>
                  
                  {!formData.autoJoin && (
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="joinApprovalRequired"
                        checked={formData.joinApprovalRequired}
                        onChange={(e) => setFormData({ ...formData, joinApprovalRequired: e.target.checked })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="joinApprovalRequired" className="ml-2 block text-sm text-gray-900">
                        需要审核加入申请
                      </label>
                    </div>
                  )}
                </div>
              </div>

              {/* 当前状态 */}
              {clubInfo && (
                <div className="border-t pt-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">当前状态</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">{clubInfo.currentMembers}</div>
                      <div className="text-sm text-gray-500">当前成员</div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">{clubInfo.maxMembers}</div>
                      <div className="text-sm text-gray-500">最大成员</div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">
                        {applications.filter(app => app.status === 'pending').length}
                      </div>
                      <div className="text-sm text-gray-500">待审核申请</div>
                    </div>
                  </div>
                </div>
              )}

                {/* 保存按钮 */}
                <div className="flex justify-end">
                  <button
                    onClick={handleSaveSettings}
                    disabled={saving}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {saving ? '保存中...' : '保存设置'}
                  </button>
                </div>
              </div>
            )}

            {/* 加入申请标签页 */}
            {activeTab === 'applications' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">加入申请</h3>
                  <p className="text-sm text-gray-500 mb-6">
                    审核用户的俱乐部加入申请
                  </p>
                </div>

                <div className="bg-white shadow rounded-lg overflow-hidden">
                  <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用户名
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      申请时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {applications.length === 0 ? (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                        暂无申请记录
                      </td>
                    </tr>
                  ) : (
                    applications.map((application) => (
                      <tr key={application.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {application.username}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(application.appliedAt).toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            application.status === 'pending' 
                              ? 'bg-yellow-100 text-yellow-800'
                              : application.status === 'approved'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {application.status === 'pending' ? '待审核' : 
                             application.status === 'approved' ? '已批准' : '已拒绝'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {application.status === 'pending' ? (
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleApplicationAction(application.id, 'approve')}
                                className="text-green-600 hover:text-green-900"
                              >
                                批准
                              </button>
                              <button
                                onClick={() => {
                                  const reason = prompt('请输入拒绝原因（可选）:')
                                  if (reason !== null) {
                                    handleApplicationAction(application.id, 'reject', reason)
                                  }
                                }}
                                className="text-red-600 hover:text-red-900"
                              >
                                拒绝
                              </button>
                            </div>
                          ) : (
                            <span className="text-gray-400">已处理</span>
                          )}
                        </td>
                      </tr>
                    ))
                  )}
                  </tbody>
                </table>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AgentLayout>
  )
}
