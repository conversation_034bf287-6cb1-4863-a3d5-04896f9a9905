'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { formatCurrency, formatNumber } from '@/lib/utils'

interface AgentStats {
  totalUsers: number
  activeUsers: number
  totalGames: number
  totalBet: number
  totalCommission: number
  agentBalance: number
  todayUsers: number
  todayGames: number
  todayCommission: number
}

export default function DashboardPage() {
  const [stats, setStats] = useState<AgentStats | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const token = localStorage.getItem('agent_token')
    if (!token) {
      router.push('/login')
      return
    }

    // 从API获取真实数据
    const fetchStats = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/agent/stats`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          if (response.status === 401) {
            localStorage.removeItem('agent_token')
            router.push('/login')
            return
          }
          throw new Error('获取统计数据失败')
        }

        const data = await response.json()
        if (data.success) {
          setStats(data.data)
        } else {
          console.error('获取统计数据失败:', data.error)
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        // 如果API失败，显示默认数据
        setStats({
          totalUsers: 0,
          activeUsers: 0,
          totalGames: 0,
          totalBet: 0,
          totalCommission: 0,
          agentBalance: 0,
          todayUsers: 0,
          todayGames: 0,
          todayCommission: 0
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('agent_token')
    router.push('/login')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-primary-600 rounded-md flex items-center justify-center mr-3">
                <span className="text-white text-sm font-bold">代</span>
              </div>
              <h1 className="text-xl font-semibold text-gray-900">代理管理平台</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">{stats?.clubInfo.name}</span>
              <button
                onClick={handleLogout}
                className="text-sm text-danger-600 hover:text-danger-800"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 代理概览 */}
          <div className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg p-6 text-white mb-6">
            <h2 className="text-2xl font-bold mb-2">代理中心</h2>
            <div className="flex items-center space-x-6">
              <div>
                <span className="text-primary-100">账户余额</span>
                <div className="text-xl font-semibold">
                  ¥{formatNumber(stats?.agentBalance || 0)}
                </div>
              </div>
              <div>
                <span className="text-primary-100">总用户数</span>
                <div className="text-xl font-semibold">
                  {formatNumber(stats?.totalUsers || 0)}
                </div>
              </div>
              <div>
                <span className="text-primary-100">活跃用户</span>
                <div className="text-xl font-semibold">
                  {formatNumber(stats?.activeUsers || 0)}
                </div>
              </div>
            </div>
          </div>
          
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">用</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatNumber(stats?.totalUsers || 0)}</dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-success-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">活</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">活跃用户</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatNumber(stats?.activeUsers || 0)}</dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-warning-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">游</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">总游戏局数</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatNumber(stats?.totalGames || 0)}</dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">佣</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">总佣金收入</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(stats?.totalCommission || 0)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* 今日数据 */}
          <div className="card mb-8">
            <div className="card-header">
              <h3 className="card-title">今日数据</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-success-600">{formatNumber(stats?.todayUsers || 0)}</div>
                <div className="text-sm text-gray-500">今日新增用户</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-warning-600">{formatNumber(stats?.todayGames || 0)}</div>
                <div className="text-sm text-gray-500">今日游戏局数</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">{formatCurrency(stats?.todayCommission || 0)}</div>
                <div className="text-sm text-gray-500">今日佣金收入</div>
              </div>
            </div>
          </div>

          {/* 快捷操作 */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">快捷操作</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="btn btn-primary">
                用户管理
              </button>
              <button className="btn btn-secondary">
                充值提现
              </button>
              <button className="btn btn-secondary">
                交易记录
              </button>
              <button
                onClick={() => router.push('/dashboard/club')}
                className="btn btn-secondary"
              >
                俱乐部设置
              </button>
              <button className="btn btn-secondary">
                数据统计
              </button>
              <button className="btn btn-secondary">
                账户设置
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
