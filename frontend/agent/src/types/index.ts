// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页类型
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 用户类型
export interface User {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  balance: number;
  agentId?: string;
  clubId?: string;
  status: 'active' | 'inactive' | 'banned';
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 代理类型
export interface Agent {
  id: string;
  username: string;
  email: string;
  phone?: string;
  balance: number;
  commission: number;
  status: 'active' | 'inactive' | 'banned';
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  club?: Club;
}

// 俱乐部类型
export interface Club {
  id: string;
  name: string;
  agentId: string;
  description?: string;
  maxMembers: number;
  currentMembers: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 交易记录类型
export interface Transaction {
  id: string;
  userId?: string;
  agentId?: string;
  type: 'deposit' | 'withdraw' | 'bet' | 'win' | 'commission' | 'transfer';
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  description?: string;
  referenceId?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
}

// 登录表单类型
export interface LoginForm {
  username?: string;
  email?: string;
  password: string;
}

// 注册表单类型
export interface RegisterForm {
  username: string;
  email: string;
  phone?: string;
  password: string;
  clubName: string;
  clubDescription?: string;
}

// 充值提现表单类型
export interface DepositForm {
  userId: string;
  amount: number;
  description?: string;
}

export interface WithdrawForm {
  userId: string;
  amount: number;
  description?: string;
}

// 代理统计数据类型
export interface AgentStats {
  totalUsers: number;
  activeUsers: number;
  totalBalance: number;
  todayDeposit: number;
  todayWithdraw: number;
  todayCommission: number;
  monthlyRevenue: number;
}

// 图表数据类型
export interface ChartData {
  name: string;
  value: number;
  date?: string;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

// 模态框属性
export interface ModalProps {
  open: boolean;
  title: string;
  children: React.ReactNode;
  onClose: () => void;
  onConfirm?: () => void;
  confirmText?: string;
  cancelText?: string;
  loading?: boolean;
}

// 表格列定义
export interface TableColumn {
  key: string;
  title: string;
  width?: string;
  sortable?: boolean;
  render?: (value: any, record: any) => React.ReactNode;
}

// 表格属性
export interface TableProps {
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
}

// 用户操作类型
export interface UserAction {
  type: 'deposit' | 'withdraw' | 'ban' | 'unban' | 'activate' | 'deactivate';
  userId: string;
  amount?: number;
  reason?: string;
}

// 俱乐部设置类型
export interface ClubSettings {
  name: string;
  description?: string;
  maxMembers: number;
  autoApprove: boolean;
  minDeposit: number;
  maxWithdraw: number;
}

// 代理设置类型
export interface AgentSettings {
  commission: number;
  autoWithdraw: boolean;
  withdrawLimit: number;
  notifications: {
    newUser: boolean;
    deposit: boolean;
    withdraw: boolean;
    lowBalance: boolean;
  };
}
