/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#fef7ee',
          100: '#fdedd3',
          200: '#fbd7a5',
          300: '#f8bb6d',
          400: '#f59332',
          500: '#f3761b',
          600: '#e45a0c',
          700: '#bd430c',
          800: '#973612',
          900: '#7a2e12',
        },
        game: {
          green: '#0f7b0f',
          red: '#dc2626',
          gold: '#fbbf24',
          blue: '#2563eb',
          purple: '#7c3aed',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'card-flip': 'cardFlip 0.6s ease-in-out',
        'card-deal': 'cardDeal 0.8s ease-out',
        'chip-move': 'chipMove 0.5s ease-in-out',
        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        cardFlip: {
          '0%': { transform: 'rotateY(0deg)' },
          '50%': { transform: 'rotateY(90deg)' },
          '100%': { transform: 'rotateY(0deg)' },
        },
        cardDeal: {
          '0%': { transform: 'translateY(-100px) rotate(0deg)', opacity: '0' },
          '100%': { transform: 'translateY(0) rotate(0deg)', opacity: '1' },
        },
        chipMove: {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.1)' },
          '100%': { transform: 'scale(1)' },
        },
      },
      backgroundImage: {
        'game-table': 'radial-gradient(ellipse at center, #0f7b0f 0%, #0a5d0a 100%)',
        'card-back': 'linear-gradient(45deg, #1e40af 0%, #3b82f6 50%, #1e40af 100%)',
      },
    },
  },
  plugins: [],
}
