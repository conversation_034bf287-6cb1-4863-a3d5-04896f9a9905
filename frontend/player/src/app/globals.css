@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
}

/* 游戏界面样式 */
.game-container {
  @apply min-h-screen bg-game-table relative overflow-hidden;
}

.game-table {
  @apply relative w-full h-full;
  background: radial-gradient(ellipse at center, #0f7b0f 0%, #0a5d0a 100%);
}

/* 扑克牌样式 */
.playing-card {
  @apply relative w-16 h-24 rounded-lg shadow-lg transition-all duration-300;
  background: white;
  border: 2px solid #e5e7eb;
}

.playing-card.face-down {
  @apply bg-card-back;
}

.playing-card:hover {
  @apply transform scale-105 shadow-xl;
}

/* 筹码样式 */
.chip {
  @apply relative w-12 h-12 rounded-full border-4 border-white shadow-lg cursor-pointer transition-all duration-200;
}

.chip:hover {
  @apply transform scale-110 shadow-xl;
}

.chip-10 {
  @apply bg-red-500;
}

.chip-50 {
  @apply bg-blue-500;
}

.chip-100 {
  @apply bg-green-500;
}

.chip-500 {
  @apply bg-purple-500;
}

.chip-1000 {
  @apply bg-yellow-500;
}

/* 座位样式 */
.game-seat {
  @apply relative p-4 rounded-lg border-2 border-transparent transition-all duration-300;
}

.game-seat.occupied {
  @apply border-yellow-400 bg-black bg-opacity-20;
}

.game-seat.current-player {
  @apply border-green-400 bg-green-500 bg-opacity-20;
}

.game-seat.empty {
  @apply border-gray-400 border-dashed bg-gray-500 bg-opacity-10;
}

/* 按钮样式 */
.game-btn {
  @apply px-6 py-3 rounded-lg font-semibold text-white shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95;
}

.game-btn-primary {
  @apply bg-primary-600 hover:bg-primary-700;
}

.game-btn-success {
  @apply bg-green-600 hover:bg-green-700;
}

.game-btn-danger {
  @apply bg-red-600 hover:bg-red-700;
}

.game-btn-warning {
  @apply bg-yellow-600 hover:bg-yellow-700;
}

.game-btn:disabled {
  @apply opacity-50 cursor-not-allowed transform-none;
}

/* 聊天样式 */
.chat-container {
  @apply absolute bottom-4 left-4 w-80 h-64 bg-black bg-opacity-70 rounded-lg p-4 text-white;
}

.chat-messages {
  @apply h-40 overflow-y-auto mb-2 space-y-1;
}

.chat-input {
  @apply w-full px-3 py-2 bg-gray-800 text-white rounded border border-gray-600 focus:border-primary-500 focus:outline-none;
}

/* 用户信息面板 */
.user-panel {
  @apply absolute top-4 right-4 bg-black bg-opacity-70 rounded-lg p-4 text-white min-w-48;
}

/* 游戏状态指示器 */
.game-status {
  @apply absolute top-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 rounded-lg px-6 py-3 text-white font-semibold;
}

/* 下注区域 */
.betting-area {
  @apply absolute bottom-20 left-1/2 transform -translate-x-1/2 flex space-x-4;
}

/* 动画效果 */
.card-enter {
  animation: cardDeal 0.8s ease-out;
}

.chip-animate {
  animation: chipMove 0.5s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .playing-card {
    @apply w-12;
    height: 4.5rem; /* 72px, equivalent to h-18 */
  }

  .chip {
    @apply w-10 h-10;
  }

  .chat-container {
    @apply w-64 h-48;
  }

  .user-panel {
    min-width: 10rem; /* 160px, equivalent to min-w-40 */
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin rounded-full border-4 border-gray-300 border-t-primary-600;
}

/* 通知样式 */
.notification {
  @apply fixed top-4 right-4 z-50 max-w-sm bg-white rounded-lg shadow-lg border p-4;
}

.notification-success {
  @apply border-green-500 bg-green-50;
}

.notification-error {
  @apply border-red-500 bg-red-50;
}

.notification-warning {
  @apply border-yellow-500 bg-yellow-50;
}

.notification-info {
  @apply border-blue-500 bg-blue-50;
}
