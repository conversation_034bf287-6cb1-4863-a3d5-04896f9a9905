'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { formatCurrency } from '@/lib/utils'

interface User {
  id: string
  username: string
  balance: number
  clubId?: string
  clubName?: string
}

export default function LobbyPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const token = localStorage.getItem('user_token')
    if (!token) {
      router.push('/login')
      return
    }

    // 检查用户俱乐部状态并获取用户信息
    checkClubMembershipAndLoadUser(token)
  }, [router])

  const checkClubMembershipAndLoadUser = async (token: string) => {
    try {
      // 检查俱乐部状态
      const clubResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/club-status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const clubData = await clubResponse.json()
      if (clubData.success) {
        if (clubData.data.status !== 'member') {
          // 用户不是俱乐部成员，跳转到俱乐部申请页面
          router.push('/club-join')
          return
        }

        // 获取用户信息
        const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/profile`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        const userData = await userResponse.json()
        if (userData.success) {
          setUser({
            id: userData.data.id,
            username: userData.data.username,
            balance: userData.data.balance,
            clubId: clubData.data.clubInfo?.id || '',
            clubName: clubData.data.clubInfo?.name || '未知俱乐部'
          })
        }
      }
    } catch (error) {
      console.error('Failed to load user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user_token')
    router.push('/login')
  }

  const handleJoinGame = (gameType: 'sangong' | 'niuniu') => {
    router.push(`/game/${gameType}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-orange-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-orange-50 to-yellow-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-gradient-to-br from-primary-500 to-orange-500 rounded-md flex items-center justify-center mr-3">
                <span className="text-white text-sm font-bold">游</span>
              </div>
              <h1 className="text-xl font-semibold text-gray-900">游戏大厅</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-700">
                <span className="font-medium">{user?.username}</span>
                {user?.clubName && (
                  <span className="ml-2 text-primary-600">({user.clubName})</span>
                )}
              </div>
              <div className="text-sm font-semibold text-green-600">
                {formatCurrency(user?.balance || 0)}
              </div>
              <button
                onClick={handleLogout}
                className="text-sm text-red-600 hover:text-red-800"
              >
                退出
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 欢迎信息 */}
          <div className="bg-gradient-to-r from-primary-500 to-orange-500 rounded-xl p-6 text-white mb-8">
            <h2 className="text-2xl font-bold mb-2">欢迎回来，{user?.username}！</h2>
            <p className="text-primary-100">选择您喜欢的游戏开始娱乐</p>
          </div>

          {/* 游戏选择 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* 三公游戏 */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <div className="h-48 bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="text-6xl font-bold mb-2">三公</div>
                  <div className="text-lg">经典扑克游戏</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">三公游戏</h3>
                <p className="text-gray-600 mb-4">
                  比拼牌型大小，大三公、小三公、混三公，刺激好玩！
                </p>
                <div className="flex justify-between items-center mb-4">
                  <div className="text-sm text-gray-500">
                    <div>最小下注: ¥10</div>
                    <div>最大下注: ¥1000</div>
                  </div>
                  <div className="text-sm text-green-600 font-semibold">
                    在线: 23人
                  </div>
                </div>
                <button
                  onClick={() => handleJoinGame('sangong')}
                  className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-200 transform hover:scale-105"
                >
                  进入游戏
                </button>
              </div>
            </div>

            {/* 牛牛游戏 */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <div className="h-48 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="text-6xl font-bold mb-2">牛牛</div>
                  <div className="text-lg">策略扑克游戏</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">牛牛游戏</h3>
                <p className="text-gray-600 mb-4">
                  五张牌组合，牛牛最大，五小牛、四炸更刺激！
                </p>
                <div className="flex justify-between items-center mb-4">
                  <div className="text-sm text-gray-500">
                    <div>最小下注: ¥10</div>
                    <div>最大下注: ¥1000</div>
                  </div>
                  <div className="text-sm text-green-600 font-semibold">
                    在线: 18人
                  </div>
                </div>
                <button
                  onClick={() => handleJoinGame('niuniu')}
                  className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105"
                >
                  进入游戏
                </button>
              </div>
            </div>
          </div>

          {/* 快捷功能 */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">快捷功能</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button className="p-4 text-center rounded-lg border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200">
                <div className="text-2xl mb-2">📊</div>
                <div className="text-sm font-medium">游戏记录</div>
              </button>
              <button className="p-4 text-center rounded-lg border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200">
                <div className="text-2xl mb-2">💰</div>
                <div className="text-sm font-medium">交易记录</div>
              </button>
              <button className="p-4 text-center rounded-lg border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200">
                <div className="text-2xl mb-2">⚙️</div>
                <div className="text-sm font-medium">设置</div>
              </button>
              <button className="p-4 text-center rounded-lg border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200">
                <div className="text-2xl mb-2">❓</div>
                <div className="text-sm font-medium">帮助</div>
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
