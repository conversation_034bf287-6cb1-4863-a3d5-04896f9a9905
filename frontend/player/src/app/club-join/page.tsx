'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface ClubApplication {
  id: string
  clubCode: string
  status: 'pending' | 'approved' | 'rejected'
  appliedAt: string
  processedAt?: string
  rejectionReason?: string
}

export default function ClubJoinPage() {
  const [clubCode, setClubCode] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [applications, setApplications] = useState<ClubApplication[]>([])
  const [userClubStatus, setUserClubStatus] = useState<'none' | 'pending' | 'member'>('none')
  const router = useRouter()

  useEffect(() => {
    const token = localStorage.getItem('user_token')
    if (!token) {
      router.push('/login')
      return
    }

    // 检查用户俱乐部状态和申请记录
    checkUserClubStatus()
    fetchApplications()
  }, [router])

  const checkUserClubStatus = async () => {
    try {
      const token = localStorage.getItem('user_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/club-status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()
      if (data.success) {
        setUserClubStatus(data.data.status)
        if (data.data.status === 'member') {
          // 如果已经是俱乐部成员，跳转到大厅
          router.push('/lobby')
        }
      }
    } catch (err) {
      console.error('Failed to check club status:', err)
    }
  }

  const fetchApplications = async () => {
    try {
      const token = localStorage.getItem('user_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/club-applications`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()
      if (data.success) {
        setApplications(data.data)
      }
    } catch (err) {
      console.error('Failed to fetch applications:', err)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setSuccess('')

    if (!clubCode || clubCode.length !== 6 || !/^\d{6}$/.test(clubCode)) {
      setError('请输入6位数字的俱乐部代码')
      setLoading(false)
      return
    }

    try {
      const token = localStorage.getItem('user_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/join-club`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ clubCode }),
      })

      const data = await response.json()

      if (data.success) {
        if (data.data.autoJoined) {
          setSuccess('成功加入俱乐部！正在跳转到游戏大厅...')
          setTimeout(() => {
            router.push('/lobby')
          }, 2000)
        } else {
          setSuccess('申请已提交，请等待俱乐部管理员审核')
          setClubCode('')
          fetchApplications()
        }
      } else {
        setError(data.error || '申请失败')
      }
    } catch (err) {
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '等待审核'
      case 'approved': return '已批准'
      case 'rejected': return '已拒绝'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100'
      case 'approved': return 'text-green-600 bg-green-100'
      case 'rejected': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-orange-50 to-yellow-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-gradient-to-br from-primary-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
            <span className="text-white text-2xl font-bold">俱</span>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            加入俱乐部
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            您需要加入俱乐部才能开始游戏
          </p>
        </div>

        {/* 申请表单 */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="clubCode" className="block text-sm font-medium text-gray-700 mb-1">
                俱乐部代码
              </label>
              <input
                id="clubCode"
                name="clubCode"
                type="text"
                required
                maxLength={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="请输入6位数字代码"
                value={clubCode}
                onChange={(e) => setClubCode(e.target.value)}
              />
              <p className="mt-1 text-xs text-gray-500">
                请向俱乐部管理员获取6位数字代码
              </p>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="text-sm text-red-600">{error}</div>
              </div>
            )}

            {success && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="text-sm text-green-600">{success}</div>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-600 to-orange-600 hover:from-primary-700 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  申请中...
                </div>
              ) : (
                '申请加入'
              )}
            </button>
          </form>
        </div>

        {/* 申请历史 */}
        {applications.length > 0 && (
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">申请记录</h3>
            <div className="space-y-3">
              {applications.map((app) => (
                <div key={app.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">俱乐部代码: {app.clubCode}</div>
                    <div className="text-sm text-gray-500">
                      申请时间: {new Date(app.appliedAt).toLocaleString()}
                    </div>
                    {app.rejectionReason && (
                      <div className="text-sm text-red-600 mt-1">
                        拒绝原因: {app.rejectionReason}
                      </div>
                    )}
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(app.status)}`}>
                    {getStatusText(app.status)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
