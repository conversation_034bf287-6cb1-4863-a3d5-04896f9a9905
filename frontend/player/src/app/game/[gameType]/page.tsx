'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSocket } from '@/hooks/useSocket'
import { formatCurrency, getGameTypeText, getGameStatusText, getSeatPosition } from '@/lib/utils'
import GameResultModal from '@/components/GameResultModal'
import { GameResult } from '@/types'

export default function GamePage() {
  const router = useRouter()
  const params = useParams()
  const gameType = params.gameType as 'sangong' | 'niuniu'
  
  const {
    connected,
    gameRoom,
    messages,
    notifications,
    gameResult,
    showResultModal,
    joinRoom,
    leaveRoom,
    sitDown,
    standUp,
    ready,
    placeBet,
    showCards,
    sendMessage,
    closeResultModal,
  } = useSocket()

  const [chatMessage, setChatMessage] = useState('')
  const [selectedBet, setSelectedBet] = useState(10)
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)

  useEffect(() => {
    const token = localStorage.getItem('user_token')
    if (!token) {
      router.push('/login')
      return
    }

    // 解析JWT token获取用户ID
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      setCurrentUserId(payload.userId)
    } catch (error) {
      console.error('Failed to parse token:', error)
      router.push('/login')
      return
    }

    // 检查用户俱乐部状态
    checkClubMembership(token)

    if (connected) {
      joinRoom(gameType)
    }

    return () => {
      leaveRoom()
    }
  }, [connected, gameType, joinRoom, leaveRoom, router])

  const checkClubMembership = async (token: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/club-status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()
      if (data.success) {
        if (data.data.status !== 'member') {
          // 用户不是俱乐部成员，跳转到俱乐部申请页面
          router.push('/club-join')
          return
        }
      } else {
        console.error('Failed to check club status:', data.error)
      }
    } catch (error) {
      console.error('Failed to check club membership:', error)
    }
  }

  const handleSitDown = (seatNumber: number) => {
    sitDown(seatNumber)
  }

  const handleStandUp = () => {
    standUp()
  }

  const handleReady = () => {
    ready()
  }

  const handlePlaceBet = () => {
    placeBet(selectedBet)
  }

  const handleShowCards = () => {
    showCards()
  }

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (chatMessage.trim()) {
      sendMessage(chatMessage)
      setChatMessage('')
    }
  }

  const handleLeaveGame = () => {
    leaveRoom()
    router.push('/lobby')
  }

  const betOptions = [10, 50, 100, 500, 1000]

  // 花色符号映射
  const getSuitSymbol = (suit: string) => {
    const suitMap: { [key: string]: string } = {
      'hearts': '♥',
      'diamonds': '♦',
      'clubs': '♣',
      'spades': '♠'
    }
    return suitMap[suit] || suit
  }

  // 获取花色颜色
  const getSuitColor = (suit: string) => {
    return suit === 'hearts' || suit === 'diamonds' ? 'text-red-500' : 'text-black'
  }

  // 获取当前用户的座位
  const currentUserSeat = gameRoom?.seats.find(seat => seat.userId === currentUserId)

  // 如果还没有获取到用户ID，显示加载状态
  if (!currentUserId) {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>
  }

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      {/* 简化的顶部状态栏 */}
      <div className="bg-white rounded-lg shadow p-4 mb-4">
        <div className="flex justify-between items-center">
          <button
            onClick={handleLeaveGame}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            离开游戏
          </button>

          <div className="text-center">
            <div className="font-bold">{getGameTypeText(gameType)}</div>
            <div className="text-sm text-gray-600">
              {gameRoom ? getGameStatusText(gameRoom.status) : '连接中...'}
            </div>
          </div>

          <div className="text-sm">
            连接: {connected ? '✅' : '❌'}
          </div>
        </div>
      </div>

      {/* 简化的游戏座位 */}
      {gameRoom && (
        <div className="bg-white rounded-lg shadow p-4 mb-4">
          <h3 className="font-bold mb-4">游戏座位</h3>
          <div className="grid grid-cols-5 gap-4">
            {gameRoom.seats.map((seat) => {
              const isCurrentUser = seat.userId === currentUserId
              const isEmpty = !seat.userId

              return (
                <div
                  key={seat.position}
                  className={`p-4 rounded border-2 text-center ${
                    isEmpty
                      ? 'border-dashed border-gray-300 bg-gray-50'
                      : isCurrentUser
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-400 bg-gray-100'
                  }`}
                >
                  <div className="text-sm font-bold mb-2">座位 {seat.position}</div>

                  {isEmpty ? (
                    <button
                      onClick={() => handleSitDown(seat.position)}
                      className="w-full py-2 bg-green-500 hover:bg-green-600 text-white rounded text-sm"
                    >
                      坐下
                    </button>
                  ) : (
                    <div>
                      <div className="font-semibold text-sm">{seat.username}</div>
                      <div className="text-xs text-gray-600">余额: {formatCurrency(seat.balance)}</div>
                      {seat.bet > 0 && (
                        <div className="text-xs text-orange-600 font-bold">
                          下注: {formatCurrency(seat.bet)}
                        </div>
                      )}
                      {seat.isReady && (
                        <div className="text-xs text-green-600">已准备</div>
                      )}

                      {/* 简化的扑克牌显示 */}
                      {seat.cards.length > 0 && (
                        <div className="mt-2">
                          <div className="text-xs text-gray-500">牌数: {seat.cards.length}</div>
                          <div className="flex justify-center space-x-1 mt-1">
                            {seat.cards.map((card, index) => (
                              <div
                                key={index}
                                className={`w-8 h-10 rounded border text-xs flex flex-col items-center justify-center ${
                                  card.hidden || !seat.cardsShown
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-white border-gray-400'
                                }`}
                              >
                                {card.hidden || !seat.cardsShown ? (
                                  '?'
                                ) : (
                                  <>
                                    <div className={`font-bold ${getSuitColor(card.suit)}`}>
                                      {card.rank}
                                    </div>
                                    <div className={`text-xs ${getSuitColor(card.suit)}`}>
                                      {getSuitSymbol(card.suit)}
                                    </div>
                                  </>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* 简化的用户信息面板 */}
      <div className="bg-white rounded-lg shadow p-4 mb-4">
        <h3 className="font-bold mb-2">我的信息</h3>
        <div className="text-sm space-y-1">
          <div>用户ID: {currentUserId}</div>
          <div>余额: {formatCurrency(currentUserSeat?.balance || 1000)}</div>
          {currentUserSeat && (
            <>
              <div>座位: {currentUserSeat.position}号</div>
              <div>状态: {currentUserSeat.isReady ? '已准备' : '未准备'}</div>
              {currentUserSeat.bet > 0 && (
                <div>下注: {formatCurrency(currentUserSeat.bet)}</div>
              )}
            </>
          )}
        </div>
      </div>

      {/* 简化的操作按钮区域 */}
      {currentUserSeat && (
        <div className="bg-white rounded-lg shadow p-4 mb-4">
          <h3 className="font-bold mb-4">游戏操作</h3>

          <div className="space-y-4">
            {/* 游戏状态显示 */}
            <div className="text-sm text-gray-600">
              当前状态: {gameRoom ? getGameStatusText(gameRoom.status) : '未知'}
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-wrap gap-2">
              {!currentUserSeat.isReady && gameRoom?.status === 'waiting' && (
                <button
                  onClick={handleReady}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
                >
                  准备
                </button>
              )}

              {gameRoom?.status === 'betting' && !currentUserSeat.bet && (
                <button
                  onClick={handlePlaceBet}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                >
                  下注 {formatCurrency(selectedBet)}
                </button>
              )}

              {gameRoom?.status === 'playing' && currentUserSeat.cards && currentUserSeat.cards.length > 0 && !currentUserSeat.cardsShown && (
                <button
                  onClick={handleShowCards}
                  className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded"
                >
                  开牌
                </button>
              )}

              <button
                onClick={handleStandUp}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
              >
                站起
              </button>
            </div>

            {/* 下注金额选择 */}
            {gameRoom?.status === 'betting' && !currentUserSeat.bet && (
              <div>
                <div className="text-sm font-semibold mb-2">选择下注金额:</div>
                <div className="flex flex-wrap gap-2">
                  {betOptions.map((amount) => (
                    <button
                      key={amount}
                      onClick={() => setSelectedBet(amount)}
                      className={`px-3 py-2 rounded text-sm ${
                        selectedBet === amount
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      {formatCurrency(amount)}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 简化的聊天和通知区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 聊天窗口 */}
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="font-bold mb-2">聊天</h3>
          <div className="h-32 overflow-y-auto bg-gray-50 rounded p-2 mb-2">
            {messages.map((msg, index) => (
              <div key={index} className="text-xs mb-1">
                <span className="font-semibold text-blue-600">{msg.username}:</span>
                <span className="ml-1">{msg.message}</span>
              </div>
            ))}
          </div>
          <form onSubmit={handleSendMessage}>
            <input
              type="text"
              value={chatMessage}
              onChange={(e) => setChatMessage(e.target.value)}
              placeholder="输入聊天内容..."
              className="w-full px-3 py-2 border rounded text-sm"
              maxLength={100}
            />
          </form>
        </div>

        {/* 通知区域 */}
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="font-bold mb-2">系统通知</h3>
          <div className="h-32 overflow-y-auto space-y-2">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-2 rounded text-sm ${
                  notification.type === 'success'
                    ? 'bg-green-100 text-green-800'
                    : notification.type === 'error'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-blue-100 text-blue-800'
                }`}
              >
                <div className="font-semibold">{notification.title}</div>
                <div className="text-xs">{notification.message}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 结算弹窗 */}
      <GameResultModal
        isOpen={showResultModal}
        gameResult={gameResult}
        onClose={closeResultModal}
      />
    </div>
  )
}
