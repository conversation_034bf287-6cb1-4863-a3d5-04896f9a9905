import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          this.removeToken();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('user_token');
    }
    return null;
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user_token', token);
    }
  }

  private removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('user_token');
    }
  }

  // 用户认证相关
  async login(username: string, email: string, password: string) {
    const response = await this.client.post<ApiResponse>('/auth/login', {
      username,
      email,
      password,
    });
    
    if (response.data.success && response.data.data?.tokens?.accessToken) {
      this.setToken(response.data.data.tokens.accessToken);
    }
    
    return response.data;
  }

  async register(data: any) {
    const response = await this.client.post<ApiResponse>('/auth/register', data);
    return response.data;
  }

  async logout() {
    try {
      await this.client.post('/auth/logout');
    } finally {
      this.removeToken();
    }
  }

  async getProfile() {
    const response = await this.client.get<ApiResponse>('/auth/profile');
    return response.data;
  }

  // 俱乐部相关
  async getAvailableClubs(page: number = 1, limit: number = 20) {
    const response = await this.client.get<ApiResponse>('/users/clubs', {
      params: { page, limit }
    });
    return response.data;
  }

  async joinClub(agentId: string) {
    const response = await this.client.post<ApiResponse>('/users/join-club', {
      agentId
    });
    return response.data;
  }

  async leaveClub() {
    const response = await this.client.post<ApiResponse>('/users/leave-club');
    return response.data;
  }

  // 游戏相关
  async getGameHistory(params?: { page?: number; limit?: number; gameType?: string }) {
    const response = await this.client.get<ApiResponse>('/users/game-history', { params });
    return response.data;
  }

  async getGameStats() {
    const response = await this.client.get<ApiResponse>('/users/game-stats');
    return response.data;
  }

  // 交易相关
  async getTransactions(params?: { page?: number; limit?: number; type?: string }) {
    const response = await this.client.get<ApiResponse>('/users/transactions', { params });
    return response.data;
  }

  // 用户设置
  async updateSettings(settings: any) {
    const response = await this.client.patch<ApiResponse>('/users/settings', settings);
    return response.data;
  }

  async getSettings() {
    const response = await this.client.get<ApiResponse>('/users/settings');
    return response.data;
  }

  // 实时数据
  async getOnlineUsers() {
    const response = await this.client.get<ApiResponse>('/realtime/online-users');
    return response.data;
  }

  async getActiveGames() {
    const response = await this.client.get<ApiResponse>('/realtime/active-games');
    return response.data;
  }
}

export const apiClient = new ApiClient();
export default apiClient;
