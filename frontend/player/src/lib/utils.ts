import { Card } from '@/types';

/**
 * 格式化货币显示
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
  }).format(amount);
}

/**
 * 格式化数字显示
 */
export function formatNumber(num: number): string {
  return new Intl.NumberFormat('zh-CN').format(num);
}

/**
 * 格式化时间显示
 */
export function formatTime(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(d);
}

/**
 * 获取扑克牌显示文本
 */
export function getCardText(card: Card): string {
  const suitMap = {
    hearts: '♥',
    diamonds: '♦',
    clubs: '♣',
    spades: '♠'
  };
  
  return `${suitMap[card.suit]}${card.rank}`;
}

/**
 * 获取扑克牌颜色
 */
export function getCardColor(card: Card): 'red' | 'black' {
  return card.suit === 'hearts' || card.suit === 'diamonds' ? 'red' : 'black';
}

/**
 * 获取游戏类型显示文本
 */
export function getGameTypeText(gameType: string): string {
  const gameTypeMap: { [key: string]: string } = {
    sangong: '三公',
    niuniu: '牛牛',
  };
  
  return gameTypeMap[gameType] || gameType;
}

/**
 * 获取游戏状态显示文本
 */
export function getGameStatusText(status: string): string {
  const statusMap: { [key: string]: string } = {
    waiting: '等待中',
    betting: '下注中',
    dealing: '发牌中',
    playing: '游戏中',
    settling: '结算中',
    finished: '已结束',
  };
  
  return statusMap[status] || status;
}

/**
 * 生成随机ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * 播放音效
 */
export function playSound(soundName: string): void {
  if (typeof window !== 'undefined') {
    const audio = new Audio(`/sounds/${soundName}.mp3`);
    audio.volume = 0.5;
    audio.play().catch(() => {
      // 忽略播放失败
    });
  }
}

/**
 * 获取筹码颜色类
 */
export function getChipColorClass(amount: number): string {
  if (amount >= 1000) return 'chip-1000';
  if (amount >= 500) return 'chip-500';
  if (amount >= 100) return 'chip-100';
  if (amount >= 50) return 'chip-50';
  return 'chip-10';
}

/**
 * 计算座位位置 - 左右各5个座位
 */
export function getSeatPosition(seatNumber: number, totalSeats: number = 10): { x: number; y: number } {
  // 左边5个座位 (1-5)，右边5个座位 (6-10)
  if (seatNumber >= 1 && seatNumber <= 5) {
    // 左边座位
    const leftIndex = seatNumber - 1; // 0-4
    return {
      x: 10, // 左边固定位置
      y: 20 + (leftIndex * 15) // 从上到下排列，间隔15%
    };
  } else if (seatNumber >= 6 && seatNumber <= 10) {
    // 右边座位
    const rightIndex = seatNumber - 6; // 0-4
    return {
      x: 90, // 右边固定位置
      y: 20 + (rightIndex * 15) // 从上到下排列，间隔15%
    };
  }

  // 默认位置（不应该到达这里）
  return { x: 50, y: 50 };
}

/**
 * 检查是否为移动设备
 */
export function isMobile(): boolean {
  if (typeof window === 'undefined') return false;
  return window.innerWidth <= 768;
}

/**
 * 复制文本到剪贴板
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch {
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    const success = document.execCommand('copy');
    document.body.removeChild(textArea);
    return success;
  }
}

/**
 * 格式化游戏时长
 */
export function formatGameDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * 获取胜率颜色
 */
export function getWinRateColor(winRate: number): string {
  if (winRate >= 70) return 'text-green-600';
  if (winRate >= 50) return 'text-yellow-600';
  return 'text-red-600';
}

/**
 * 合并CSS类名
 */
export function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

/**
 * 验证邮箱格式
 */
export function validateEmail(email: string): boolean {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

/**
 * 验证用户名格式
 */
export function validateUsername(username: string): boolean {
  const regex = /^[a-zA-Z0-9_]{3,20}$/;
  return regex.test(username);
}
