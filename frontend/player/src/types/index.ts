// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 用户类型
export interface User {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  balance: number;
  agentId?: string;
  clubId?: string;
  status: 'active' | 'inactive' | 'banned';
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 扑克牌类型
export interface Card {
  suit: 'hearts' | 'diamonds' | 'clubs' | 'spades';
  rank: 'A' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | 'J' | 'Q' | 'K';
  value: number;
  hidden?: boolean;
}

// 游戏座位类型
export interface GameSeat {
  position: number;
  userId: string | null;
  username: string | null;
  balance: number;
  bet: number;
  cards: Card[];
  isReady: boolean;
  cardsShown?: boolean;
  sideWagers: SideWager[];
}

// 旁注类型
export interface SideWager {
  userId: string;
  username: string;
  amount: number;
  targetSeat: number;
}

// 游戏房间状态
export interface GameRoom {
  roomId: string;
  gameType: 'sangong' | 'niuniu';
  status: 'waiting' | 'betting' | 'dealing' | 'playing' | 'settling' | 'finished';
  seats: GameSeat[];
  spectators: string[];
  currentRound: number;
  minBet: string | number;
  maxBet: string | number;
}

// WebSocket事件类型
export enum SocketEvents {
  CONNECTION = 'connection',
  DISCONNECT = 'disconnect',
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  SIT_DOWN = 'sit_down',
  STAND_UP = 'stand_up',
  READY = 'ready',
  PLACE_BET = 'place_bet',
  PLACE_SIDE_WAGER = 'place_side_wager',
  GAME_START = 'game_start',
  DEAL_CARDS = 'deal_cards',
  SHOW_CARDS = 'show_cards',
  GAME_END = 'game_end',
  ROOM_UPDATE = 'room_update',
  CHAT_MESSAGE = 'chat_message',
  NOTIFICATION = 'notification',
  ERROR = 'error',
  BALANCE_UPDATE = 'balance_update',
}

// WebSocket消息类型
export interface SocketMessage<T = any> {
  event: SocketEvents;
  data: T;
  timestamp: Date;
  userId?: string;
}

// 聊天消息类型
export interface ChatMessage {
  userId: string;
  username: string;
  userType?: 'user' | 'agent' | 'admin' | 'system';
  message: string;
  type: 'text' | 'emoji';
  timestamp: Date;
  isSystem?: boolean;
  messageType?: 'info' | 'warning' | 'error';
}

// 游戏结果类型
export interface GameResult {
  players: {
    userId: string;
    hand: any;
    bet: number;
    winAmount: number;
    isWinner: boolean;
  }[];
  winners: string[];
  totalPot: number;
  roundNumber: number;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

// 登录表单类型
export interface LoginForm {
  username?: string;
  email?: string;
  password: string;
}

// 注册表单类型
export interface RegisterForm {
  username: string;
  email: string;
  phone?: string;
  password: string;
  agentCode?: string;
}

// 游戏操作类型
export interface GameAction {
  type: 'join_room' | 'sit_down' | 'stand_up' | 'ready' | 'place_bet' | 'place_side_wager';
  data?: any;
}

// 下注选项
export interface BetOption {
  amount: number;
  color: string;
  label: string;
}

// 俱乐部信息
export interface Club {
  id: string;
  name: string;
  agentId: string;
  description?: string;
  maxMembers: number;
  currentMembers: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 游戏统计
export interface GameStats {
  totalGames: number;
  totalWins: number;
  totalLosses: number;
  totalWinnings: number;
  winRate: number;
  favoriteGame: string;
}

// 用户设置
export interface UserSettings {
  soundEnabled: boolean;
  musicEnabled: boolean;
  autoReady: boolean;
  quickBet: boolean;
  chatEnabled: boolean;
  notifications: {
    gameStart: boolean;
    gameEnd: boolean;
    newMessage: boolean;
  };
}

// 游戏配置
export interface GameConfig {
  minBet: number;
  maxBet: number;
  maxSeats: number;
  bettingTime: number;
  dealingTime: number;
  settlingTime: number;
}

// 错误响应
export interface ErrorResponse {
  code: string;
  message: string;
  details?: any;
}
