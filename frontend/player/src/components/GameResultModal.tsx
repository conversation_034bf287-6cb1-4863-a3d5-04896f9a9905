'use client'

import { useEffect, useState } from 'react'
import { GameResult } from '@/types'

interface GameResultModalProps {
  isOpen: boolean
  gameResult: GameResult | null
  onClose: () => void
}

export default function GameResultModal({ isOpen, gameResult, onClose }: GameResultModalProps) {
  const [autoCloseTimer, setAutoCloseTimer] = useState<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (isOpen && gameResult) {
      // 15秒后自动关闭
      const timer = setTimeout(() => {
        onClose()
      }, 15000)
      
      setAutoCloseTimer(timer)
      
      return () => {
        if (timer) {
          clearTimeout(timer)
        }
      }
    }
  }, [isOpen, gameResult, onClose])

  useEffect(() => {
    return () => {
      if (autoCloseTimer) {
        clearTimeout(autoCloseTimer)
      }
    }
  }, [autoCloseTimer])

  if (!isOpen || !gameResult) {
    return null
  }

  // 花色符号映射
  const getSuitSymbol = (suit: string) => {
    const suitMap: { [key: string]: string } = {
      'hearts': '♥',
      'diamonds': '♦',
      'clubs': '♣',
      'spades': '♠'
    }
    return suitMap[suit] || suit
  }

  // 获取花色颜色
  const getSuitColor = (suit: string) => {
    return suit === 'hearts' || suit === 'diamonds' ? 'text-red-500' : 'text-black'
  }

  // 格式化卡牌显示
  const formatCard = (card: any) => {
    if (typeof card === 'string') {
      // 处理字符串格式的卡牌 (如 "Aspades")
      const rank = card.slice(0, -6) || card.slice(0, -7)
      const suit = card.slice(-6) || card.slice(-7)
      return {
        rank: rank,
        suit: suit,
        display: `${rank}${getSuitSymbol(suit)}`,
        color: getSuitColor(suit)
      }
    } else {
      // 处理对象格式的卡牌
      return {
        rank: card.rank,
        suit: card.suit,
        display: `${card.rank}${getSuitSymbol(card.suit)}`,
        color: getSuitColor(card.suit)
      }
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* 标题栏 */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-t-lg">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">🎯 游戏结算</h2>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-2xl font-bold"
            >
              ×
            </button>
          </div>
          <div className="text-sm opacity-90 mt-1">
            第 {gameResult.roundNumber} 轮 | 总奖池: ¥{gameResult.totalPot}
          </div>
        </div>

        {/* 结算内容 */}
        <div className="p-6">
          {/* 玩家结果列表 */}
          <div className="space-y-4">
            {gameResult.players.map((player, index) => (
              <div
                key={player.userId}
                className={`border rounded-lg p-4 ${
                  player.isWinner 
                    ? 'border-green-500 bg-green-50' 
                    : 'border-red-500 bg-red-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  {/* 玩家信息 */}
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                      player.isWinner ? 'bg-green-500' : 'bg-red-500'
                    }`}>
                      {player.isWinner ? '🏆' : '💸'}
                    </div>
                    <div>
                      <div className="font-semibold">
                        玩家 {index + 1}
                      </div>
                      <div className="text-sm text-gray-600">
                        下注: ¥{player.bet}
                      </div>
                    </div>
                  </div>

                  {/* 输赢金额 */}
                  <div className={`text-right ${
                    player.isWinner ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <div className="text-lg font-bold">
                      {player.winAmount > 0 ? '+' : ''}¥{player.winAmount}
                    </div>
                    <div className="text-sm">
                      {player.isWinner ? '获胜' : '失败'}
                    </div>
                  </div>
                </div>

                {/* 手牌信息 */}
                {player.hand && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between">
                      {/* 手牌 */}
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">手牌:</span>
                        <div className="flex space-x-1">
                          {player.hand.cards && player.hand.cards.map((card: any, cardIndex: number) => {
                            const formattedCard = formatCard(card)
                            return (
                              <span
                                key={cardIndex}
                                className={`inline-block px-2 py-1 bg-white border rounded text-sm font-mono ${formattedCard.color}`}
                              >
                                {formattedCard.display}
                              </span>
                            )
                          })}
                        </div>
                      </div>

                      {/* 牌型 */}
                      <div className="text-right">
                        <div className="font-semibold text-blue-600">
                          {player.hand.description}
                        </div>
                        {player.hand.points !== undefined && (
                          <div className="text-sm text-gray-600">
                            {player.hand.points}点
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* 获胜者总结 */}
          {gameResult.winners && gameResult.winners.length > 0 && (
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-800 mb-2">
                  🎉 恭喜获胜！
                </div>
                <div className="text-yellow-700">
                  获胜者: {gameResult.winners.length} 人
                </div>
              </div>
            </div>
          )}

          {/* 关闭按钮 */}
          <div className="mt-6 text-center">
            <button
              onClick={onClose}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold"
            >
              确定
            </button>
            <div className="text-xs text-gray-500 mt-2">
              15秒后自动关闭
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
