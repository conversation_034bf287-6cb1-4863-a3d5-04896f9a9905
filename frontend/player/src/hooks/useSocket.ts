import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { SocketEvents, SocketMessage, GameRoom, ChatMessage, Notification, GameResult } from '@/types';

interface UseSocketReturn {
  socket: Socket | null;
  connected: boolean;
  gameRoom: GameRoom | null;
  messages: ChatMessage[];
  notifications: Notification[];
  userBalance: number | null;
  gameResult: GameResult | null;
  showResultModal: boolean;
  joinRoom: (gameType: 'sangong' | 'niuniu') => void;
  leaveRoom: () => void;
  sitDown: (seatNumber: number) => void;
  standUp: () => void;
  ready: () => void;
  placeBet: (amount: number) => void;
  placeSideWager: (amount: number, targetSeat: number) => void;
  showCards: () => void;
  sendMessage: (message: string) => void;
  clearNotifications: () => void;
  closeResultModal: () => void;
}

export function useSocket(): UseSocketReturn {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [gameRoom, setGameRoom] = useState<GameRoom | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [userBalance, setUserBalance] = useState<number | null>(null);
  const [gameResult, setGameResult] = useState<GameResult | null>(null);
  const [showResultModal, setShowResultModal] = useState(false);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  useEffect(() => {
    const token = localStorage.getItem('user_token');
    if (!token) return;

    const newSocket = io(process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:3002', {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
    });

    // 连接事件
    newSocket.on('connect', () => {
      console.log('WebSocket连接成功');
      setConnected(true);
      reconnectAttempts.current = 0;
    });

    newSocket.on('disconnect', (reason) => {
      console.log('WebSocket连接断开:', reason);
      setConnected(false);
      
      // 自动重连
      if (reason === 'io server disconnect') {
        // 服务器主动断开，不重连
        return;
      }
      
      if (reconnectAttempts.current < maxReconnectAttempts) {
        setTimeout(() => {
          reconnectAttempts.current++;
          newSocket.connect();
        }, 1000 * Math.pow(2, reconnectAttempts.current));
      }
    });

    // 游戏事件
    newSocket.on(SocketEvents.ROOM_UPDATE, (data: GameRoom) => {
      setGameRoom(data);
    });

    newSocket.on(SocketEvents.GAME_START, (data: any) => {
      // 新游戏开始时关闭结算弹窗
      setShowResultModal(false);
      setGameResult(null);

      addNotification({
        id: Date.now().toString(),
        type: 'info',
        title: '游戏开始',
        message: '新一轮游戏开始了！',
        duration: 3000,
      });
    });

    newSocket.on(SocketEvents.DEAL_CARDS, (data: any) => {
      // 处理发牌事件
      console.log('收到发牌:', data);
    });

    newSocket.on(SocketEvents.SHOW_CARDS, (data: any) => {
      // 处理开牌事件
      console.log('收到开牌:', data);
      addNotification({
        id: Date.now().toString(),
        type: 'info',
        title: '开牌',
        message: `${data.username} 开牌了！`,
        duration: 3000,
      });
    });

    newSocket.on(SocketEvents.GAME_END, (data: any) => {
      console.log('收到游戏结束事件:', data);

      // 设置游戏结果并显示弹窗
      if (data.result) {
        setGameResult(data.result);
        setShowResultModal(true);
      }

      addNotification({
        id: Date.now().toString(),
        type: 'success',
        title: '游戏结束',
        message: '本轮游戏已结束',
        duration: 5000,
      });
    });

    // 聊天事件
    newSocket.on(SocketEvents.CHAT_MESSAGE, (data: ChatMessage) => {
      setMessages(prev => [...prev.slice(-49), data]); // 保留最近50条消息
    });

    // 通知事件
    newSocket.on(SocketEvents.NOTIFICATION, (data: Notification) => {
      addNotification({
        ...data,
        id: Date.now().toString(),
      });
    });

    // 余额更新事件
    newSocket.on(SocketEvents.BALANCE_UPDATE, (data: { newBalance: number }) => {
      console.log('收到余额更新:', data);
      setUserBalance(data.newBalance);
      addNotification({
        id: Date.now().toString(),
        type: 'success',
        title: '余额更新',
        message: `您的余额已更新为 ¥${data.newBalance.toFixed(2)}`,
        duration: 3000,
      });
    });

    // 错误事件
    newSocket.on(SocketEvents.ERROR, (data: any) => {
      addNotification({
        id: Date.now().toString(),
        type: 'error',
        title: '错误',
        message: data.message || '发生了未知错误',
        duration: 5000,
      });
    });

    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
    };
  }, []);

  const addNotification = (notification: Notification) => {
    setNotifications(prev => [...prev, notification]);
    
    // 自动移除通知
    if (notification.duration) {
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, notification.duration);
    }
  };

  const joinRoom = useCallback((gameType: 'sangong' | 'niuniu') => {
    if (socket) {
      socket.emit(SocketEvents.JOIN_ROOM, { gameType });
    }
  }, [socket]);

  const leaveRoom = useCallback(() => {
    if (socket) {
      socket.emit(SocketEvents.LEAVE_ROOM);
      setGameRoom(null);
    }
  }, [socket]);

  const sitDown = useCallback((seatNumber: number) => {
    if (socket) {
      socket.emit(SocketEvents.SIT_DOWN, { seatNumber });
    }
  }, [socket]);

  const standUp = useCallback(() => {
    if (socket) {
      socket.emit(SocketEvents.STAND_UP);
    }
  }, [socket]);

  const ready = useCallback(() => {
    if (socket) {
      socket.emit(SocketEvents.READY);
    }
  }, [socket]);

  const placeBet = useCallback((amount: number) => {
    if (socket) {
      socket.emit(SocketEvents.PLACE_BET, { amount });
    }
  }, [socket]);

  const placeSideWager = useCallback((amount: number, targetSeat: number) => {
    if (socket) {
      socket.emit(SocketEvents.PLACE_SIDE_WAGER, { amount, targetSeat });
    }
  }, [socket]);

  const showCards = useCallback(() => {
    if (socket) {
      socket.emit(SocketEvents.SHOW_CARDS);
    }
  }, [socket]);

  const sendMessage = useCallback((message: string) => {
    if (socket && message.trim()) {
      socket.emit(SocketEvents.CHAT_MESSAGE, { message, type: 'text' });
    }
  }, [socket]);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const closeResultModal = useCallback(() => {
    setShowResultModal(false);
    setGameResult(null);
  }, []);

  return {
    socket,
    connected,
    gameRoom,
    messages,
    notifications,
    userBalance,
    gameResult,
    showResultModal,
    joinRoom,
    leaveRoom,
    sitDown,
    standUp,
    ready,
    placeBet,
    placeSideWager,
    showCards,
    sendMessage,
    clearNotifications,
    closeResultModal,
  };
}
