'use client'

import { useState, useEffect } from 'react'

interface Agent {
  id: string
  username: string
  email: string
  phone: string
  balance: number
  commission: number
  status: string
  last_login_at: string
  created_at: string
  user_count: number
  club_count: number
}

interface User {
  id: string
  username: string
  email: string
  balance: number
  status: string
  last_login_at: string
  created_at: string
  club_name: string
}

interface Club {
  id: string
  name: string
  description: string
  current_members: number
  max_members: number
  status: string
}

interface Stats {
  total_transactions: number
  total_income: number
  total_expense: number
}

interface AgentDetailModalProps {
  agentId: string | null
  isOpen: boolean
  onClose: () => void
  onRefresh: () => void
}

export default function AgentDetailModal({ agentId, isOpen, onClose, onRefresh }: AgentDetailModalProps) {
  const [loading, setLoading] = useState(false)
  const [agent, setAgent] = useState<Agent | null>(null)
  const [users, setUsers] = useState<User[]>([])
  const [clubs, setClubs] = useState<Club[]>([])
  const [stats, setStats] = useState<Stats | null>(null)
  const [activeTab, setActiveTab] = useState('info')

  const fetchAgentDetail = async () => {
    if (!agentId) return

    setLoading(true)
    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/admin/agents/${agentId}/detail`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('获取代理详情失败')
      }

      const data = await response.json()
      if (data.success) {
        setAgent(data.data.agent)
        setUsers(data.data.users || [])
        setClubs(data.data.clubs || [])
        setStats(data.data.stats)
      } else {
        console.error('获取代理详情失败:', data.error)
      }
    } catch (error) {
      console.error('获取代理详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isOpen && agentId) {
      fetchAgentDetail()
    }
  }, [isOpen, agentId])

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: { text: '正常', class: 'bg-green-100 text-green-800' },
      inactive: { text: '未激活', class: 'bg-yellow-100 text-yellow-800' },
      banned: { text: '已封禁', class: 'bg-red-100 text-red-800' }
    }
    const statusInfo = statusMap[status as keyof typeof statusMap] || { text: status, class: 'bg-gray-100 text-gray-800' }
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.class}`}>
        {statusInfo.text}
      </span>
    )
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                代理详情
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-4 text-gray-600">加载中...</p>
                </div>
              </div>
            ) : agent ? (
              <div>
                {/* 标签页导航 */}
                <div className="border-b border-gray-200 mb-6">
                  <nav className="-mb-px flex space-x-8">
                    <button
                      onClick={() => setActiveTab('info')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'info'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      基本信息
                    </button>
                    <button
                      onClick={() => setActiveTab('users')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'users'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      下属用户 ({users.length})
                    </button>
                    <button
                      onClick={() => setActiveTab('clubs')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'clubs'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      俱乐部 ({clubs.length})
                    </button>
                    <button
                      onClick={() => setActiveTab('stats')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'stats'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      统计数据
                    </button>
                  </nav>
                </div>

                {/* 标签页内容 */}
                {activeTab === 'info' && (
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">基本信息</h4>
                      <dl className="space-y-2">
                        <div>
                          <dt className="text-sm text-gray-500">用户名</dt>
                          <dd className="text-sm font-medium text-gray-900">{agent.username}</dd>
                        </div>
                        <div>
                          <dt className="text-sm text-gray-500">邮箱</dt>
                          <dd className="text-sm font-medium text-gray-900">{agent.email || '-'}</dd>
                        </div>
                        <div>
                          <dt className="text-sm text-gray-500">手机号</dt>
                          <dd className="text-sm font-medium text-gray-900">{agent.phone || '-'}</dd>
                        </div>
                        <div>
                          <dt className="text-sm text-gray-500">状态</dt>
                          <dd className="text-sm font-medium text-gray-900">{getStatusBadge(agent.status)}</dd>
                        </div>
                      </dl>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">财务信息</h4>
                      <dl className="space-y-2">
                        <div>
                          <dt className="text-sm text-gray-500">余额</dt>
                          <dd className="text-sm font-medium text-green-600">¥{agent.balance.toLocaleString()}</dd>
                        </div>
                        <div>
                          <dt className="text-sm text-gray-500">佣金比例</dt>
                          <dd className="text-sm font-medium text-gray-900">{(agent.commission * 100).toFixed(2)}%</dd>
                        </div>
                        <div>
                          <dt className="text-sm text-gray-500">注册时间</dt>
                          <dd className="text-sm font-medium text-gray-900">{new Date(agent.created_at).toLocaleString()}</dd>
                        </div>
                        <div>
                          <dt className="text-sm text-gray-500">最后登录</dt>
                          <dd className="text-sm font-medium text-gray-900">
                            {agent.last_login_at ? new Date(agent.last_login_at).toLocaleString() : '从未登录'}
                          </dd>
                        </div>
                      </dl>
                    </div>
                  </div>
                )}

                {activeTab === 'users' && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">下属用户列表</h4>
                    {users.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">用户名</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">余额</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">俱乐部</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">状态</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">注册时间</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {users.map((user) => (
                              <tr key={user.id}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {user.username}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                  ¥{user.balance.toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {user.club_name || '-'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  {getStatusBadge(user.status)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {new Date(user.created_at).toLocaleDateString()}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-500 text-center py-8">暂无下属用户</p>
                    )}
                  </div>
                )}

                {activeTab === 'clubs' && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">俱乐部列表</h4>
                    {clubs.length > 0 ? (
                      <div className="space-y-4">
                        {clubs.map((club) => (
                          <div key={club.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <h5 className="text-sm font-medium text-gray-900">{club.name}</h5>
                                <p className="text-sm text-gray-500">{club.description || '暂无描述'}</p>
                              </div>
                              <div className="text-right">
                                <div className="text-sm text-gray-900">
                                  {club.current_members}/{club.max_members} 成员
                                </div>
                                <div className="mt-1">
                                  {getStatusBadge(club.status)}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-center py-8">暂无俱乐部</p>
                    )}
                  </div>
                )}

                {activeTab === 'stats' && stats && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">统计数据</h4>
                    <div className="grid grid-cols-3 gap-6">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{stats.total_transactions}</div>
                        <div className="text-sm text-blue-600">总交易数</div>
                      </div>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">¥{stats.total_income.toLocaleString()}</div>
                        <div className="text-sm text-green-600">总收入</div>
                      </div>
                      <div className="bg-red-50 p-4 rounded-lg">
                        <div className="text-2xl font-bold text-red-600">¥{stats.total_expense.toLocaleString()}</div>
                        <div className="text-sm text-red-600">总支出</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">未找到代理信息</p>
              </div>
            )}
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
