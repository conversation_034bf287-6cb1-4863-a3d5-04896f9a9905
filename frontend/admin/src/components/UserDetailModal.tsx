'use client'

import { useState, useEffect } from 'react'

interface UserDetail {
  user: {
    id: string
    username: string
    email: string
    phone: string
    balance: number
    status: string
    last_login_at: string
    created_at: string
    agent_username: string
    club_name: string
  }
  transactions: Array<{
    id: string
    type: string
    amount: number
    balance_before: number
    balance_after: number
    description: string
    status: string
    created_at: string
  }>
  gameParticipations: Array<{
    id: string
    game_type: string
    bet_amount: number
    win_amount: number
    is_winner: boolean
    started_at: string
    total_pot: number
  }>
  stats: {
    totalGames: number
    totalBet: number
    totalWin: number
    winCount: number
    winRate: string
  }
}

interface UserDetailModalProps {
  userId: string | null
  isOpen: boolean
  onClose: () => void
}

export default function UserDetailModal({ userId, isOpen, onClose }: UserDetailModalProps) {
  const [userDetail, setUserDetail] = useState<UserDetail | null>(null)
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('info')

  useEffect(() => {
    if (isOpen && userId) {
      fetchUserDetail()
    }
  }, [isOpen, userId])

  const fetchUserDetail = async () => {
    if (!userId) return

    setLoading(true)
    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch(`/api/admin/users/${userId}/detail`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setUserDetail(data.data)
        }
      }
    } catch (error) {
      console.error('获取用户详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getTransactionTypeName = (type: string) => {
    const typeMap = {
      deposit: '充值',
      withdraw: '提现',
      bet: '下注',
      win: '赢取',
      commission: '佣金',
      transfer: '转账'
    }
    return typeMap[type as keyof typeof typeMap] || type
  }

  const getGameTypeName = (gameType: string) => {
    const gameTypeMap = {
      sangong: '三公',
      niuniu: '牛牛'
    }
    return gameTypeMap[gameType as keyof typeof gameTypeMap] || gameType
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: { text: '正常', class: 'bg-green-100 text-green-800' },
      inactive: { text: '未激活', class: 'bg-yellow-100 text-yellow-800' },
      banned: { text: '已封禁', class: 'bg-red-100 text-red-800' }
    }
    const statusInfo = statusMap[status as keyof typeof statusMap] || { text: status, class: 'bg-gray-100 text-gray-800' }
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.class}`}>
        {statusInfo.text}
      </span>
    )
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                用户详情
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            ) : userDetail ? (
              <div>
                {/* 标签页导航 */}
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8">
                    <button
                      onClick={() => setActiveTab('info')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'info'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      基本信息
                    </button>
                    <button
                      onClick={() => setActiveTab('transactions')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'transactions'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      交易记录
                    </button>
                    <button
                      onClick={() => setActiveTab('games')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'games'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      游戏记录
                    </button>
                    <button
                      onClick={() => setActiveTab('stats')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'stats'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      统计数据
                    </button>
                  </nav>
                </div>

                {/* 标签页内容 */}
                <div className="mt-6">
                  {activeTab === 'info' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-3">用户信息</h4>
                        <dl className="space-y-2">
                          <div>
                            <dt className="text-sm text-gray-500">用户名</dt>
                            <dd className="text-sm text-gray-900">{userDetail.user.username}</dd>
                          </div>
                          <div>
                            <dt className="text-sm text-gray-500">邮箱</dt>
                            <dd className="text-sm text-gray-900">{userDetail.user.email}</dd>
                          </div>
                          <div>
                            <dt className="text-sm text-gray-500">手机号</dt>
                            <dd className="text-sm text-gray-900">{userDetail.user.phone || '-'}</dd>
                          </div>
                          <div>
                            <dt className="text-sm text-gray-500">余额</dt>
                            <dd className="text-sm text-gray-900">¥{userDetail.user.balance.toLocaleString()}</dd>
                          </div>
                          <div>
                            <dt className="text-sm text-gray-500">状态</dt>
                            <dd className="text-sm text-gray-900">{getStatusBadge(userDetail.user.status)}</dd>
                          </div>
                        </dl>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-3">关联信息</h4>
                        <dl className="space-y-2">
                          <div>
                            <dt className="text-sm text-gray-500">所属代理</dt>
                            <dd className="text-sm text-gray-900">{userDetail.user.agent_username || '-'}</dd>
                          </div>
                          <div>
                            <dt className="text-sm text-gray-500">所属俱乐部</dt>
                            <dd className="text-sm text-gray-900">{userDetail.user.club_name || '-'}</dd>
                          </div>
                          <div>
                            <dt className="text-sm text-gray-500">注册时间</dt>
                            <dd className="text-sm text-gray-900">{new Date(userDetail.user.created_at).toLocaleString()}</dd>
                          </div>
                          <div>
                            <dt className="text-sm text-gray-500">最后登录</dt>
                            <dd className="text-sm text-gray-900">
                              {userDetail.user.last_login_at ? new Date(userDetail.user.last_login_at).toLocaleString() : '从未登录'}
                            </dd>
                          </div>
                        </dl>
                      </div>
                    </div>
                  )}

                  {activeTab === 'transactions' && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">最近交易记录</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">类型</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">金额</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">余额变化</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">时间</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {userDetail.transactions.map((transaction) => (
                              <tr key={transaction.id}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {getTransactionTypeName(transaction.type)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  <span className={transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'}>
                                    {transaction.amount >= 0 ? '+' : ''}¥{transaction.amount.toLocaleString()}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ¥{transaction.balance_before.toLocaleString()} → ¥{transaction.balance_after.toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {new Date(transaction.created_at).toLocaleString()}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {activeTab === 'games' && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">最近游戏记录</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">游戏类型</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">下注金额</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">赢取金额</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">结果</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">时间</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {userDetail.gameParticipations.map((game) => (
                              <tr key={game.id}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {getGameTypeName(game.game_type)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ¥{game.bet_amount.toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ¥{game.win_amount.toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                    game.is_winner ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {game.is_winner ? '胜利' : '失败'}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {new Date(game.started_at).toLocaleString()}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {activeTab === 'stats' && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">统计数据</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">{userDetail.stats.totalGames}</div>
                          <div className="text-sm text-gray-600">总游戏局数</div>
                        </div>
                        <div className="bg-green-50 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-green-600">¥{userDetail.stats.totalBet.toLocaleString()}</div>
                          <div className="text-sm text-gray-600">总下注金额</div>
                        </div>
                        <div className="bg-purple-50 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-purple-600">¥{userDetail.stats.totalWin.toLocaleString()}</div>
                          <div className="text-sm text-gray-600">总赢取金额</div>
                        </div>
                        <div className="bg-yellow-50 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-yellow-600">{userDetail.stats.winRate}%</div>
                          <div className="text-sm text-gray-600">胜率</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">无法加载用户详情</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
