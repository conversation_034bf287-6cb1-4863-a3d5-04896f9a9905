// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页类型
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 用户类型
export interface User {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  balance: number;
  agentId?: string;
  clubId?: string;
  status: 'active' | 'inactive' | 'banned';
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 代理类型
export interface Agent {
  id: string;
  username: string;
  email: string;
  phone?: string;
  balance: number;
  commission: number;
  status: 'active' | 'inactive' | 'banned';
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  club?: Club;
}

// 俱乐部类型
export interface Club {
  id: string;
  name: string;
  agentId: string;
  description?: string;
  maxMembers: number;
  currentMembers: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 游戏记录类型
export interface GameRecord {
  id: string;
  roomId: string;
  gameType: 'sangong' | 'niuniu';
  roundNumber: number;
  players: any[];
  gameResult: any;
  totalPot: number;
  createdAt: string;
}

// 交易记录类型
export interface Transaction {
  id: string;
  userId?: string;
  agentId?: string;
  type: 'deposit' | 'withdraw' | 'bet' | 'win' | 'commission' | 'transfer';
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  description?: string;
  referenceId?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
}

// 系统配置类型
export interface SystemConfig {
  id: number;
  configKey: string;
  configValue: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// 管理员类型
export interface Admin {
  id: string;
  username: string;
  email: string;
  role: 'super_admin' | 'admin' | 'operator';
  permissions: string[];
  status: 'active' | 'inactive';
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 登录表单类型
export interface LoginForm {
  username: string;
  password: string;
}

// 统计数据类型
export interface DashboardStats {
  totalUsers: number;
  totalAgents: number;
  totalGames: number;
  totalRevenue: number;
  todayUsers: number;
  todayGames: number;
  todayRevenue: number;
  onlineUsers: number;
}

// 图表数据类型
export interface ChartData {
  name: string;
  value: number;
  date?: string;
}

// 游戏统计类型
export interface GameStats {
  gameType: 'sangong' | 'niuniu';
  totalGames: number;
  totalPlayers: number;
  totalRevenue: number;
  averageGameDuration: number;
}

// 表格列定义
export interface TableColumn {
  key: string;
  title: string;
  width?: string;
  sortable?: boolean;
  render?: (value: any, record: any) => React.ReactNode;
}

// 表格属性
export interface TableProps {
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
}

// 模态框属性
export interface ModalProps {
  open: boolean;
  title: string;
  children: React.ReactNode;
  onClose: () => void;
  onConfirm?: () => void;
  confirmText?: string;
  cancelText?: string;
  loading?: boolean;
}

// 表单字段类型
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: { label: string; value: string | number }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: RegExp;
    message?: string;
  };
}

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}
