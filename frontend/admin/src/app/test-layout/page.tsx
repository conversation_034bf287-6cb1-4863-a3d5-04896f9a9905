'use client'

import AdminLayout from '../../components/AdminLayout'

export default function TestLayoutPage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">布局测试页面</h1>
          <p className="text-gray-600">
            这是一个测试页面，用来验证后台管理系统的布局是否正常工作。
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-blue-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">测试卡片 1</h3>
            <p className="text-blue-700">这是第一个测试卡片的内容。</p>
          </div>
          
          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-green-900 mb-2">测试卡片 2</h3>
            <p className="text-green-700">这是第二个测试卡片的内容。</p>
          </div>
          
          <div className="bg-purple-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-purple-900 mb-2">测试卡片 3</h3>
            <p className="text-purple-700">这是第三个测试卡片的内容。</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">布局检查清单</h2>
          <ul className="space-y-2">
            <li className="flex items-center">
              <span className="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
              侧边栏在大屏幕上应该固定显示
            </li>
            <li className="flex items-center">
              <span className="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
              主内容区域应该有正确的左边距（lg:pl-64）
            </li>
            <li className="flex items-center">
              <span className="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
              顶部导航栏应该正常显示
            </li>
            <li className="flex items-center">
              <span className="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
              移动端侧边栏应该可以正常切换
            </li>
            <li className="flex items-center">
              <span className="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
              内容不应该被导航栏遮挡
            </li>
          </ul>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 p-6 rounded-lg">
          <h2 className="text-xl font-semibold text-yellow-900 mb-4">注意事项</h2>
          <p className="text-yellow-800">
            如果你看到这个页面的布局有问题，请检查：
          </p>
          <ol className="list-decimal list-inside mt-2 space-y-1 text-yellow-800">
            <li>侧边栏是否正确定位</li>
            <li>主内容区域是否有正确的边距</li>
            <li>CSS类是否正确应用</li>
            <li>是否有样式冲突</li>
          </ol>
        </div>

        {/* 添加一些长内容来测试滚动 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">长内容测试</h2>
          {Array.from({ length: 10 }, (_, i) => (
            <p key={i} className="mb-4 text-gray-600">
              这是第 {i + 1} 段测试内容。Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
              Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, 
              quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. 
              Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
            </p>
          ))}
        </div>
      </div>
    </AdminLayout>
  )
}
