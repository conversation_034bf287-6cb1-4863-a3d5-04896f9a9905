'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import AdminLayout from '../../../components/AdminLayout'

interface Agent {
  id: string
  username: string
  email: string
  phone: string
  balance: number
  commission: number
  status: string
  last_login_at: string
  created_at: string
  user_count: number
  club_count: number
}

interface AgentsResponse {
  agents: Agent[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export default function AgentsPage() {
  const [agents, setAgents] = useState<Agent[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [selectedAgents, setSelectedAgents] = useState<string[]>([])
  const [selectAll, setSelectAll] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [createFormData, setCreateFormData] = useState({
    username: '',
    password: '',
    commission: '0.1',
    clubName: '',
    clubDescription: ''
  })
  const [createLoading, setCreateLoading] = useState(false)
  const router = useRouter()

  const fetchAgents = async (page: number = 1, searchTerm: string = '', status: string = '') => {
    try {
      const token = localStorage.getItem('admin_token')
      if (!token) {
        router.push('/login')
        return
      }

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString()
      })

      if (searchTerm) params.append('search', searchTerm)
      if (status) params.append('status', status)

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/admin/agents?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('admin_token')
          router.push('/login')
          return
        }
        throw new Error('获取代理列表失败')
      }

      const data = await response.json()
      if (data.success) {
        setAgents(data.data.agents)
        setPagination(data.data.pagination)
      } else {
        console.error('获取代理列表失败:', data.error)
      }
    } catch (error) {
      console.error('获取代理列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAgents()
  }, [])

  const handleSearch = () => {
    setLoading(true)
    fetchAgents(1, search, statusFilter)
  }

  const handleStatusChange = async (agentId: string, newStatus: string) => {
    try {
      const token = localStorage.getItem('admin_token')
      if (!token) return

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/admin/agents/${agentId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        fetchAgents(pagination.page, search, statusFilter)
      } else {
        alert('更新代理状态失败')
      }
    } catch (error) {
      console.error('更新代理状态失败:', error)
      alert('更新代理状态失败')
    }
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: { text: '正常', class: 'bg-green-100 text-green-800' },
      inactive: { text: '未激活', class: 'bg-yellow-100 text-yellow-800' },
      banned: { text: '已封禁', class: 'bg-red-100 text-red-800' }
    }
    const statusInfo = statusMap[status as keyof typeof statusMap] || { text: status, class: 'bg-gray-100 text-gray-800' }
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.class}`}>
        {statusInfo.text}
      </span>
    )
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  const handleSelectAgent = (agentId: string) => {
    setSelectedAgents(prev =>
      prev.includes(agentId)
        ? prev.filter(id => id !== agentId)
        : [...prev, agentId]
    )
  }

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedAgents([])
    } else {
      setSelectedAgents(agents.map(agent => agent.id))
    }
    setSelectAll(!selectAll)
  }

  const handleBatchStatusChange = async (newStatus: string) => {
    if (selectedAgents.length === 0) {
      alert('请先选择代理')
      return
    }

    if (!confirm(`确定要将选中的 ${selectedAgents.length} 个代理状态改为 ${newStatus} 吗？`)) {
      return
    }

    try {
      const token = localStorage.getItem('admin_token')
      if (!token) return

      const promises = selectedAgents.map(agentId =>
        fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/admin/agents/${agentId}/status`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ status: newStatus })
        })
      )

      await Promise.all(promises)
      fetchAgents(pagination.page, search, statusFilter)
      setSelectedAgents([])
      setSelectAll(false)
      alert('批量操作完成')
    } catch (error) {
      console.error('批量操作失败:', error)
      alert('批量操作失败')
    }
  }

  const handleBatchExport = () => {
    if (selectedAgents.length === 0) {
      alert('请先选择代理')
      return
    }

    const selectedAgentData = agents.filter(agent => selectedAgents.includes(agent.id))
    const csvContent = [
      ['用户名', '邮箱', '手机号', '余额', '佣金比例', '状态', '用户数', '俱乐部数', '注册时间'].join(','),
      ...selectedAgentData.map(agent => [
        agent.username,
        agent.email,
        agent.phone || '',
        agent.balance,
        (agent.commission * 100).toFixed(2) + '%',
        agent.status,
        agent.user_count,
        agent.club_count,
        new Date(agent.created_at).toLocaleString()
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `agents_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleExportAll = async () => {
    try {
      const token = localStorage.getItem('admin_token')
      if (!token) return

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/admin/agents?limit=10000`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          const csvContent = [
            ['用户名', '邮箱', '手机号', '余额', '佣金比例', '状态', '用户数', '俱乐部数', '最后登录', '注册时间'].join(','),
            ...data.data.agents.map((agent: any) => [
              agent.username,
              agent.email,
              agent.phone || '',
              agent.balance,
              (agent.commission * 100).toFixed(2) + '%',
              agent.status,
              agent.user_count,
              agent.club_count,
              agent.last_login_at ? new Date(agent.last_login_at).toLocaleString() : '从未登录',
              new Date(agent.created_at).toLocaleString()
            ].join(','))
          ].join('\n')

          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
          const link = document.createElement('a')
          const url = URL.createObjectURL(blob)
          link.setAttribute('href', url)
          link.setAttribute('download', `all_agents_${new Date().toISOString().split('T')[0]}.csv`)
          link.style.visibility = 'hidden'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }
      }
    } catch (error) {
      console.error('导出失败:', error)
      alert('导出失败')
    }
  }

  const handleCreateAgent = async (e: React.FormEvent) => {
    e.preventDefault()
    setCreateLoading(true)

    try {
      const token = localStorage.getItem('admin_token')
      if (!token) return

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/admin/agents`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...createFormData,
          commission: parseFloat(createFormData.commission)
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          alert('代理创建成功')
          setShowCreateModal(false)
          setCreateFormData({
            username: '',
            password: '',
            commission: '0.1',
            clubName: '',
            clubDescription: ''
          })
          fetchAgents(pagination.page, search, statusFilter)
        } else {
          alert(data.error || '创建代理失败')
        }
      } else {
        alert('创建代理失败')
      }
    } catch (error) {
      console.error('创建代理失败:', error)
      alert('创建代理失败')
    } finally {
      setCreateLoading(false)
    }
  }

  const handleCreateFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setCreateFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <AdminLayout>
      <div>
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">代理管理</h1>
            <p className="mt-2 text-sm text-gray-700">
              管理平台上的所有代理账户，包括查看代理信息、修改代理状态等。
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <div className="flex space-x-3">
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center justify-center rounded-md border border-transparent bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                创建代理
              </button>
              <button
                onClick={handleExportAll}
                className="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                导出全部
              </button>
            </div>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div className="mt-6 bg-white shadow rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">搜索代理</label>
              <input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="用户名、邮箱或手机号"
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">状态筛选</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">全部状态</option>
                <option value="active">正常</option>
                <option value="inactive">未激活</option>
                <option value="banned">已封禁</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={handleSearch}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                搜索
              </button>
            </div>
          </div>
        </div>

        {/* 批量操作 */}
        {selectedAgents.length > 0 && (
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-blue-700">
                已选择 {selectedAgents.length} 个代理
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleBatchStatusChange('active')}
                  className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                >
                  批量启用
                </button>
                <button
                  onClick={() => handleBatchStatusChange('banned')}
                  className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                >
                  批量禁用
                </button>
                <button
                  onClick={handleBatchExport}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  导出选中
                </button>
                <button
                  onClick={() => {
                    setSelectedAgents([])
                    setSelectAll(false)
                  }}
                  className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  取消选择
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 代理列表 */}
        <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:p-6">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAll}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      代理信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      余额
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      佣金比例
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用户/俱乐部
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      最后登录
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {agents.map((agent) => (
                    <tr key={agent.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedAgents.includes(agent.id)}
                          onChange={() => handleSelectAgent(agent.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{agent.username}</div>
                          <div className="text-sm text-gray-500">{agent.email}</div>
                          {agent.phone && <div className="text-sm text-gray-500">{agent.phone}</div>}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ¥{agent.balance.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {(agent.commission * 100).toFixed(2)}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(agent.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{agent.user_count} 用户</div>
                        <div className="text-gray-500">{agent.club_count} 俱乐部</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {agent.last_login_at ? new Date(agent.last_login_at).toLocaleString() : '从未登录'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <select
                          value={agent.status}
                          onChange={(e) => handleStatusChange(agent.id, e.target.value)}
                          className="text-sm border-gray-300 rounded-md"
                        >
                          <option value="active">正常</option>
                          <option value="inactive">未激活</option>
                          <option value="banned">已封禁</option>
                        </select>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分页 */}
            {pagination.totalPages > 1 && (
              <div className="mt-6 flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  显示 {(pagination.page - 1) * pagination.limit + 1} 到{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} 条，共 {pagination.total} 条
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => fetchAgents(pagination.page - 1, search, statusFilter)}
                    disabled={pagination.page <= 1}
                    className="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <button
                    onClick={() => fetchAgents(pagination.page + 1, search, statusFilter)}
                    disabled={pagination.page >= pagination.totalPages}
                    className="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md disabled:opacity-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 创建代理弹窗 */}
      {showCreateModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowCreateModal(false)}></div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleCreateAgent}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      创建代理
                    </h3>
                    <button
                      type="button"
                      onClick={() => setShowCreateModal(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">用户名</label>
                      <input
                        type="text"
                        name="username"
                        required
                        value={createFormData.username}
                        onChange={handleCreateFormChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="请输入用户名"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">密码</label>
                      <input
                        type="password"
                        name="password"
                        required
                        value={createFormData.password}
                        onChange={handleCreateFormChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="请输入密码"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">佣金比例</label>
                      <input
                        type="number"
                        name="commission"
                        step="0.01"
                        min="0"
                        max="1"
                        required
                        value={createFormData.commission}
                        onChange={handleCreateFormChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="0.1 (10%)"
                      />
                      <p className="mt-1 text-sm text-gray-500">输入0.1表示10%佣金</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">俱乐部名称</label>
                      <input
                        type="text"
                        name="clubName"
                        required
                        value={createFormData.clubName}
                        onChange={handleCreateFormChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="请输入俱乐部名称"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">俱乐部描述</label>
                      <textarea
                        name="clubDescription"
                        rows={3}
                        value={createFormData.clubDescription}
                        onChange={handleCreateFormChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="请输入俱乐部描述（可选）"
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="submit"
                    disabled={createLoading}
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
                  >
                    {createLoading ? '创建中...' : '创建代理'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    取消
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}
