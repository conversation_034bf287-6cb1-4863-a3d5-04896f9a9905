import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse, PaginationParams } from '@/types';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          this.removeToken();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('admin_token');
    }
    return null;
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('admin_token', token);
    }
  }

  private removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('admin_token');
    }
  }

  // 认证相关
  async login(username: string, password: string) {
    const response = await this.client.post<ApiResponse>('/admin/login', {
      username,
      password,
    });
    
    if (response.data.success && response.data.data?.tokens?.accessToken) {
      this.setToken(response.data.data.tokens.accessToken);
    }
    
    return response.data;
  }

  async logout() {
    try {
      await this.client.post('/admin/logout');
    } finally {
      this.removeToken();
    }
  }

  async getProfile() {
    const response = await this.client.get<ApiResponse>('/admin/profile');
    return response.data;
  }

  // 用户管理
  async getUsers(params: PaginationParams) {
    const response = await this.client.get<ApiResponse>('/admin/users', { params });
    return response.data;
  }

  async getUserById(id: string) {
    const response = await this.client.get<ApiResponse>(`/admin/users/${id}`);
    return response.data;
  }

  async updateUserStatus(id: string, status: string) {
    const response = await this.client.put<ApiResponse>(`/admin/users/${id}/status`, {
      status,
    });
    return response.data;
  }

  async updateUserBalance(id: string, amount: number, description?: string) {
    const response = await this.client.put<ApiResponse>(`/admin/users/${id}/balance`, {
      amount,
      description,
    });
    return response.data;
  }

  // 代理管理
  async getAgents(params: PaginationParams) {
    const response = await this.client.get<ApiResponse>('/admin/agents', { params });
    return response.data;
  }

  async getAgentById(id: string) {
    const response = await this.client.get<ApiResponse>(`/admin/agents/${id}`);
    return response.data;
  }

  async createAgent(data: any) {
    const response = await this.client.post<ApiResponse>('/admin/agents', data);
    return response.data;
  }

  async updateAgent(id: string, data: any) {
    const response = await this.client.patch<ApiResponse>(`/admin/agents/${id}`, data);
    return response.data;
  }

  async updateAgentStatus(id: string, status: string) {
    const response = await this.client.put<ApiResponse>(`/admin/agents/${id}/status`, {
      status,
    });
    return response.data;
  }

  async updateAgentCommission(id: string, commission: number) {
    const response = await this.client.patch<ApiResponse>(`/admin/agents/${id}/commission`, {
      commission,
    });
    return response.data;
  }

  // 游戏记录
  async getGameRecords(params: PaginationParams & { gameType?: string }) {
    const response = await this.client.get<ApiResponse>('/admin/games', { params });
    return response.data;
  }

  async getGameRecord(id: string) {
    const response = await this.client.get<ApiResponse>(`/admin/games/${id}`);
    return response.data;
  }

  // 交易记录
  async getTransactions(params: PaginationParams & { type?: string; userId?: string }) {
    const response = await this.client.get<ApiResponse>('/admin/transactions', { params });
    return response.data;
  }

  async getTransaction(id: string) {
    const response = await this.client.get<ApiResponse>(`/admin/transactions/${id}`);
    return response.data;
  }

  // 系统配置
  async getSystemConfigs() {
    const response = await this.client.get<ApiResponse>('/system/configs');
    return response.data;
  }

  async updateSystemConfig(key: string, value: string) {
    const response = await this.client.patch<ApiResponse>(`/system/configs/${key}`, {
      value,
    });
    return response.data;
  }

  // 统计数据
  async getDashboardStats() {
    const response = await this.client.get<ApiResponse>('/admin/dashboard/stats');
    return response.data;
  }

  async getGameStats(params?: { startDate?: string; endDate?: string }) {
    const response = await this.client.get<ApiResponse>('/stats/games', { params });
    return response.data;
  }

  async getUserStats(params?: { startDate?: string; endDate?: string }) {
    const response = await this.client.get<ApiResponse>('/stats/users', { params });
    return response.data;
  }

  async getRevenueStats(params?: { startDate?: string; endDate?: string; groupBy?: string }) {
    const response = await this.client.get<ApiResponse>('/stats/revenue', { params });
    return response.data;
  }

  // 实时数据
  async getOnlineUsers() {
    const response = await this.client.get<ApiResponse>('/realtime/online-users');
    return response.data;
  }

  async getActiveGames() {
    const response = await this.client.get<ApiResponse>('/realtime/active-games');
    return response.data;
  }

  // 系统操作
  async restartGameRoom(gameType: string) {
    const response = await this.client.post<ApiResponse>(`/system/restart-room/${gameType}`);
    return response.data;
  }

  async broadcastMessage(message: string, type: string = 'info') {
    const response = await this.client.post<ApiResponse>('/system/broadcast', {
      message,
      type,
    });
    return response.data;
  }
}

export const apiClient = new ApiClient();
export default apiClient;
