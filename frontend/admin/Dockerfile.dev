# 开发环境Dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装开发工具
RUN apk add --no-cache curl

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装所有依赖（包括开发依赖）
RUN npm ci

# 复制源代码
COPY . .

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# 设置权限
RUN chown -R nextjs:nodejs /app

# 切换到非root用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 环境变量
ENV NODE_ENV development
ENV HOSTNAME "0.0.0.0"

# 开发环境启动命令（支持热重载）
CMD ["npm", "run", "dev"]
