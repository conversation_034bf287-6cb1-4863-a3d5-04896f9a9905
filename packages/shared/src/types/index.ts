// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  balance: number;
  status: 'active' | 'inactive' | 'banned';
  createdAt: Date;
  updatedAt: Date;
}

export interface Agent {
  id: string;
  userId: string;
  agentCode: string;
  commissionRate: number; // 返点率
  balance: number;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}

export interface Club {
  id: string;
  agentId: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}

export interface ClubMember {
  id: string;
  clubId: string;
  userId: string;
  joinedAt: Date;
  status: 'active' | 'inactive';
}

// 游戏相关类型
export type GameType = 'sangong' | 'niuniu';

export type CardSuit = 'hearts' | 'diamonds' | 'clubs' | 'spades';
export type CardRank = 'A' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | 'J' | 'Q' | 'K';

export interface Card {
  suit: CardSuit;
  rank: CardRank;
  value: number; // 用于计算的数值
}

export interface GameRoom {
  id: string;
  gameType: GameType;
  maxPlayers: number;
  minPlayers: number;
  currentPlayers: number;
  status: 'waiting' | 'playing' | 'finished';
  seats: (GameSeat | null)[];
  spectators: Spectator[];
  createdAt: Date;
  updatedAt: Date;
}

export interface GameSeat {
  seatIndex: number;
  playerId: string;
  playerName: string;
  balance: number;
  betAmount: number;
  cards: Card[];
  status: 'waiting' | 'playing' | 'folded' | 'finished';
}

export interface Spectator {
  id: string;
  userId: string;
  userName: string;
  betOnSeat?: number; // 旁注的座位号
  betAmount?: number; // 旁注金额
}

// 游戏记录
export interface GameRecord {
  id: string;
  roomId: string;
  gameType: GameType;
  players: GamePlayer[];
  result: GameResult;
  createdAt: Date;
}

export interface GamePlayer {
  userId: string;
  userName: string;
  seatIndex: number;
  cards: Card[];
  betAmount: number;
  winAmount: number;
  handType?: string; // 牌型描述
}

export interface GameResult {
  winners: string[]; // 获胜玩家ID列表
  totalPot: number;
  payouts: Record<string, number>; // 玩家ID -> 赢得金额
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

export interface JoinRoomMessage extends WebSocketMessage {
  type: 'join_room';
  data: {
    roomId: string;
    userId: string;
    seatIndex?: number; // 如果指定座位
  };
}

export interface BetMessage extends WebSocketMessage {
  type: 'bet';
  data: {
    roomId: string;
    userId: string;
    amount: number;
    seatIndex?: number; // 旁注时指定座位
  };
}

export interface GameStateMessage extends WebSocketMessage {
  type: 'game_state';
  data: {
    room: GameRoom;
    currentTurn?: string;
    timeLeft?: number;
  };
}
