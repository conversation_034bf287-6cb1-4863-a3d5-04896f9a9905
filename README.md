# 🎮 游戏平台

一个完整的在线游戏平台，支持三公和牛牛游戏，包含管理后台、代理端和玩家端。

## 🏗️ 项目架构

```
games/
├── backend/                 # 后端服务
│   ├── api/                # RESTful API服务 (端口: 3001)
│   └── websocket/          # WebSocket游戏服务 (端口: 3002)
├── frontend/               # 前端应用
│   ├── admin/             # 管理后台 (端口: 3000)
│   ├── agent/             # 代理端 (端口: 3003)
│   └── player/            # 玩家端 (端口: 3004)
├── database/              # 数据库脚本
├── nginx/                 # Nginx配置
├── scripts/               # 部署脚本
└── docker-compose.yml     # Docker编排
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 7+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd games
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，设置数据库密码等配置
```

3. **启动开发环境**

**方式一：快速启动（推荐）**
```bash
# 给脚本执行权限
chmod +x scripts/dev-start.sh scripts/dev-stop.sh

# 启动开发环境
./scripts/dev-start.sh

# 停止开发环境
./scripts/dev-stop.sh
```

**方式二：Docker启动**
```bash
# 给部署脚本执行权限
chmod +x scripts/deploy.sh

# 构建并启动开发环境
./scripts/deploy.sh dev build
./scripts/deploy.sh dev start
```

4. **访问应用**
- 玩家端: http://localhost:3004
- 代理端: http://localhost:3003
- 管理后台: http://localhost:3000
- API服务: http://localhost:3001

## 🎯 功能特性

### 核心功能
- ✅ 用户注册登录系统
- ✅ 代理管理系统（代理即俱乐部）
- ✅ 三公游戏逻辑
- ✅ 牛牛游戏逻辑
- ✅ 实时游戏通信
- ✅ 充值提现系统
- ✅ 交易记录管理

### 技术特性
- 🔐 JWT身份认证 + Redis会话管理
- 📡 WebSocket实时通信
- 💾 Redis缓存优化
- 🐳 Docker容器化部署
- 🔄 自动化部署脚本
- 📊 完整的日志系统

## 🎮 游戏规则

### 三公游戏
- 每人3张牌
- 牌型：大三公 > 小三公 > 混三公 > 点数牌
- 支持10座位 + 无限旁注
- 大吃小机制

### 牛牛游戏
- 每人5张牌
- 牌型：五小牛 > 四炸 > 五花牛 > 牛牛 > 牛9-1 > 无牛
- 支持10座位 + 无限旁注
- 倍数计算机制

## 🛠️ 开发指南

### 开发命令

**快速开发命令**
```bash
# 启动开发环境
./scripts/dev-start.sh

# 停止开发环境
./scripts/dev-stop.sh

# 查看日志
tail -f logs/api.log          # API服务日志
tail -f logs/websocket.log    # WebSocket服务日志
tail -f logs/admin.log        # 管理后台日志
tail -f logs/agent.log        # 代理端日志
tail -f logs/player.log       # 玩家端日志
```

**Docker开发命令**
```bash
# 启动开发环境
./scripts/deploy.sh dev start

# 查看日志
./scripts/deploy.sh dev logs [服务名]

# 重启服务
./scripts/deploy.sh dev restart

# 停止服务
./scripts/deploy.sh dev stop

# 健康检查
./scripts/deploy.sh dev health

# 数据库迁移
./scripts/deploy.sh dev migrate

# 数据备份
./scripts/deploy.sh dev backup
```

### 项目结构详解

#### 后端服务
```
backend/
├── api/                    # RESTful API (Express.js)
│   ├── src/
│   │   ├── controllers/   # 控制器
│   │   ├── middleware/    # 中间件
│   │   ├── routes/        # 路由
│   │   ├── services/      # 业务逻辑
│   │   └── config/        # 配置
│   └── tests/             # 测试
└── websocket/             # WebSocket服务 (Socket.IO)
    ├── src/
    │   ├── handlers/      # 事件处理器
    │   ├── game/          # 游戏逻辑
    │   └── utils/         # 工具函数
    └── tests/             # 测试
```

#### 前端应用
```
frontend/
├── admin/                 # 管理后台 (Next.js)
├── agent/                 # 代理端 (Next.js)
└── player/                # 玩家端 (Next.js)
    ├── src/
    │   ├── app/          # 页面组件
    │   ├── components/   # 通用组件
    │   ├── hooks/        # 自定义Hook
    │   ├── lib/          # 工具库
    │   └── types/        # 类型定义
    └── public/           # 静态资源
```

### 测试

```bash
# 运行API测试
cd backend/api
npm test

# 运行WebSocket测试
cd backend/websocket
npm test

# 运行前端测试
cd frontend/player
npm test
```

## 🚀 部署指南

### 开发环境
```bash
./scripts/deploy.sh dev build
./scripts/deploy.sh dev start
```

### 生产环境
```bash
# 设置生产环境变量
cp .env.example .env.production
# 编辑生产环境配置

# 构建并部署
./scripts/deploy.sh production build
./scripts/deploy.sh production start
```

### 监控和维护
```bash
# 查看服务状态
./scripts/deploy.sh production health

# 查看日志
./scripts/deploy.sh production logs

# 备份数据
./scripts/deploy.sh production backup

# 更新服务
./scripts/deploy.sh production restart
```

## 📊 API文档

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息

### 代理接口
- `POST /api/agent/register` - 代理注册
- `POST /api/agent/login` - 代理登录
- `POST /api/agent/deposit` - 给用户充值
- `POST /api/agent/withdraw` - 用户提现
- `GET /api/agent/users` - 获取用户列表

### WebSocket事件
- `join_room` - 加入游戏房间
- `sit_down` - 坐下
- `ready` - 准备
- `place_bet` - 下注
- `place_side_wager` - 旁注

## 🔧 配置说明

### 环境变量
详见 `.env.example` 文件中的配置说明。

### 数据库配置
- 默认连接: `mysql://root:root@localhost:3306/games`
- 支持连接池配置
- 自动重连机制

### Redis配置
- 默认连接: `redis://localhost:6379`
- 用于会话管理和游戏状态缓存
- 支持集群模式

## 🎯 核心架构特点

### 代理即俱乐部模式
- 每个代理拥有一个俱乐部
- 用户加入代理的俱乐部
- 代理负责用户的充值提现
- 代理余额限制用户操作

### 游戏房间设计
- 每种游戏类型只有一个房间
- 10个固定座位
- 支持无限旁注
- 实时状态同步

### 三端分离架构
- **管理端**: 系统管理、代理管理、数据统计
- **代理端**: 俱乐部管理、用户管理、充值提现
- **玩家端**: 游戏体验、俱乐部加入、个人中心

## 🆘 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库配置信息
   - 确认网络连接

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis配置
   - 检查防火墙设置

3. **WebSocket连接问题**
   - 检查端口是否被占用
   - 验证CORS配置
   - 查看浏览器控制台错误

4. **Docker构建失败**
   - 清理Docker缓存: `docker system prune`
   - 检查Dockerfile语法
   - 验证依赖版本

### 获取帮助

- 查看日志: `./scripts/deploy.sh dev logs`
- 健康检查: `./scripts/deploy.sh dev health`

## 🎯 项目完成状态

### ✅ 已完成功能

**后端服务**
- [x] RESTful API服务 (Express.js + TypeScript)
- [x] WebSocket游戏服务 (Socket.IO)
- [x] MySQL数据库设计和初始化
- [x] Redis缓存集成
- [x] JWT身份认证系统
- [x] 用户/代理管理
- [x] 充值提现系统
- [x] 交易记录管理

**游戏逻辑**
- [x] 三公游戏完整实现
- [x] 牛牛游戏完整实现
- [x] 实时游戏通信
- [x] 游戏房间管理
- [x] 旁注系统

**前端应用**
- [x] 管理后台 (Next.js + TailwindCSS)
- [x] 代理端界面
- [x] 玩家游戏端
- [x] 响应式设计
- [x] 实时数据更新

**部署配置**
- [x] Docker容器化
- [x] Nginx反向代理
- [x] 自动化部署脚本
- [x] 环境配置管理
- [x] 测试框架集成

### 🚀 技术栈总览

**后端技术**
- Node.js 18+ + TypeScript
- Express.js (RESTful API)
- Socket.IO (WebSocket)
- MySQL 8.0 (主数据库)
- Redis 7 (缓存/会话)
- JWT (身份认证)
- Jest (测试框架)

**前端技术**
- Next.js 14 + TypeScript
- TailwindCSS (样式框架)
- Socket.IO Client (实时通信)
- Axios (HTTP客户端)
- React Hooks (状态管理)

**部署技术**
- Docker + Docker Compose
- Nginx (反向代理)
- 自动化部署脚本
- 多环境配置

### 📋 默认账户信息

**管理员账户**
- 用户名: `superadmin`
- 密码: `admin123`
- 访问地址: http://localhost:3000/admin

**测试代理账户**
- 用户名: `testagent`
- 密码: `agent123`
- 访问地址: http://localhost:3003

**测试用户账户**
- 用户名: `testuser`
- 密码: `password123`
- 访问地址: http://localhost:3004

### 🔄 开发工作流

1. **启动开发环境**
```bash
./scripts/deploy.sh dev start
```

2. **实时开发**
- 后端代码修改会自动重启服务
- 前端代码修改会热重载
- 数据库和Redis持久化

3. **测试验证**
```bash
# 运行所有测试
npm run test:all

# 健康检查
./scripts/deploy.sh dev health
```

4. **部署生产**
```bash
./scripts/deploy.sh production build
./scripts/deploy.sh production start
```

### 🎮 游戏体验流程

1. **用户注册** → 玩家端注册账户
2. **加入俱乐部** → 选择代理的俱乐部加入
3. **充值** → 代理为用户充值游戏币
4. **进入游戏** → 选择三公或牛牛游戏
5. **游戏对局** → 坐下、准备、下注、游戏
6. **提现** → 向代理申请提现

### 📊 性能特性

- **并发支持**: 支持数百用户同时在线
- **实时通信**: WebSocket确保游戏实时性
- **缓存优化**: Redis缓存提升响应速度
- **负载均衡**: Nginx支持多实例部署
- **数据持久化**: MySQL确保数据安全

## 📞 技术支持

如有技术问题，请：
1. 查看日志文件排查问题
2. 检查服务健康状态
3. 参考故障排除指南
4. 提交详细的问题报告

## 📝 许可证

本项目采用 MIT 许可证。
