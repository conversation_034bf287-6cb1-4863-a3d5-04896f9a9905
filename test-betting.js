const io = require('socket.io-client');

// 测试下注功能
async function testBetting() {
  console.log('🎮 Testing betting functionality...\n');

  // 创建两个用户连接
  const socket1 = io('http://localhost:3002', {
    auth: {
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5NWM0ZjkxYy01MTFlLTExZjAtYjVhNS0wMjQyYWMxNDAwMDIiLCJ1c2VyVHlwZSI6InVzZXIiLCJ1c2VybmFtZSI6InRlc3R1c2VyIiwic2Vzc2lvbklkIjoiNzlmMjEyOTYtZWQ3My00MGZjLThhNmQtZGJiZGU2OGVkMThkIiwiaWF0IjoxNzUxODE3OTUyLCJleHAiOjE3NTI0MjI3NTIsImF1ZCI6ImdhbWVzLXVzZXJzIiwiaXNzIjoiZ2FtZXMtcGxhdGZvcm0ifQ.IJuazEguf_iPnHkMBgr0k75a2jnxyn7FjbdYA8FVo2k'
    }
  });

  const socket2 = io('http://localhost:3002', {
    auth: {
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5NWM0ZjkxYy01MTFlLTExZjAtYjVhNS0wMjQyYWMxNDAwMDMiLCJ1c2VyVHlwZSI6InVzZXIiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInNlc3Npb25JZCI6IjY2MjYxM2ZmLThkYzktNGZmOC1hYjdlLTVkMzZlMzI0OTJlNyIsImlhdCI6MTc1MTgxNzk1OSwiZXhwIjoxNzUyNDIyNzU5LCJhdWQiOiJnYW1lcy11c2VycyIsImlzcyI6ImdhbWVzLXBsYXRmb3JtIn0.GgYcfC3nIXkRQsGMgnw2ZpVA8Pks9XPesv5txUx5Fwc'
    }
  });

  // 等待连接
  await new Promise((resolve) => {
    let connected = 0;
    socket1.on('connect', () => {
      console.log('✅ testuser connected');
      connected++;
      if (connected === 2) resolve();
    });
    socket2.on('connect', () => {
      console.log('✅ testuser2 connected');
      connected++;
      if (connected === 2) resolve();
    });
  });

  // 监听事件
  socket1.on('notification', (data) => {
    console.log(`📢 testuser: ${data.title} - ${data.message}`);
  });

  socket2.on('notification', (data) => {
    console.log(`📢 testuser2: ${data.title} - ${data.message}`);
  });

  socket1.on('room_update', (data) => {
    console.log(`📊 testuser room update - Status: ${data.status}, Players: ${data.playerCount}`);
  });

  socket2.on('room_update', (data) => {
    console.log(`📊 testuser2 room update - Status: ${data.status}, Players: ${data.playerCount}`);
  });

  socket1.on('game_start', (data) => {
    console.log(`🚀 testuser received GAME_START!`);
    console.log(`   Phase: ${data.phase}, Time: ${data.timeLimit}ms`);
  });

  socket2.on('game_start', (data) => {
    console.log(`🚀 testuser2 received GAME_START!`);
    console.log(`   Phase: ${data.phase}, Time: ${data.timeLimit}ms`);
  });

  socket1.on('deal_cards', (data) => {
    console.log(`🃏 testuser received cards:`, data.cards);
  });

  socket2.on('deal_cards', (data) => {
    console.log(`🃏 testuser2 received cards:`, data.cards);
  });

  socket1.on('error', (data) => {
    console.log(`❌ testuser error: ${data.code} - ${data.message}`);
  });

  socket2.on('error', (data) => {
    console.log(`❌ testuser2 error: ${data.code} - ${data.message}`);
  });

  // 加入房间
  socket1.emit('join_room', { gameType: 'sangong' });
  socket2.emit('join_room', { gameType: 'sangong' });

  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log('\n🪑 Users sitting down...');
  // 坐下
  socket1.emit('sit_down', { seatNumber: 1 });
  await new Promise(resolve => setTimeout(resolve, 1000));

  socket2.emit('sit_down', { seatNumber: 2 });
  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log('\n⏰ Waiting for auto-start (should start in 5 seconds)...');
  await new Promise(resolve => setTimeout(resolve, 6000));

  console.log('\n💰 Testing betting...');

  // 测试下注
  console.log('💰 testuser placing bet: 100');
  socket1.emit('place_bet', { amount: 100 });

  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('💰 testuser2 placing bet: 200');
  socket2.emit('place_bet', { amount: 200 });

  // 等待游戏完成
  await new Promise(resolve => setTimeout(resolve, 15000));

  console.log('\n📊 Test completed');
  socket1.disconnect();
  socket2.disconnect();
  process.exit(0);
}

testBetting().catch(console.error);