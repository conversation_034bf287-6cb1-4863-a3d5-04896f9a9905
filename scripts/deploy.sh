#!/bin/bash

# 游戏平台部署脚本
# 使用方法: ./scripts/deploy.sh [环境] [操作]
# 环境: dev, staging, production
# 操作: build, start, stop, restart, logs, clean

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 设置环境变量
setup_environment() {
    local env=$1
    
    log_info "设置${env}环境..."
    
    case $env in
        "dev")
            export COMPOSE_FILE="docker-compose.yml:docker-compose.dev.yml"
            export NODE_ENV="development"
            ;;
        "staging")
            export COMPOSE_FILE="docker-compose.yml:docker-compose.staging.yml"
            export NODE_ENV="staging"
            ;;
        "production")
            export COMPOSE_FILE="docker-compose.yml:docker-compose.prod.yml"
            export NODE_ENV="production"
            ;;
        *)
            log_error "未知环境: $env"
            exit 1
            ;;
    esac
    
    log_success "环境设置完成: $env"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    docker-compose build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 先启动基础服务
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 启动应用服务
    docker-compose up -d api websocket
    
    # 等待API服务启动
    log_info "等待API服务启动..."
    sleep 5
    
    # 启动前端服务
    docker-compose up -d admin agent player
    
    # 启动Nginx
    docker-compose up -d nginx
    
    log_success "所有服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    docker-compose down
    
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    stop_services
    start_services
    
    log_success "服务重启完成"
}

# 查看日志
view_logs() {
    local service=$1
    
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$service"
    fi
}

# 清理资源
clean_resources() {
    log_warning "这将删除所有容器、镜像和数据卷，确定要继续吗？(y/N)"
    read -r response
    
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理Docker资源..."
        
        # 停止并删除容器
        docker-compose down -v --remove-orphans
        
        # 删除镜像
        docker-compose down --rmi all
        
        # 清理未使用的资源
        docker system prune -f
        
        log_success "资源清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local services=("mysql" "redis" "api" "websocket" "admin" "agent" "player" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose ps "$service" | grep -q "Up"; then
            log_success "$service: 运行中"
        else
            log_error "$service: 未运行"
        fi
    done
}

# 数据库迁移
run_migrations() {
    log_info "执行数据库迁移..."
    
    # 等待数据库启动
    docker-compose exec mysql mysqladmin ping -h localhost -u root -proot --silent
    
    # 执行迁移脚本
    docker-compose exec api npm run migrate
    
    log_success "数据库迁移完成"
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    
    log_info "备份数据到 $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # 备份MySQL数据
    docker-compose exec mysql mysqldump -u root -proot games > "$backup_dir/mysql_backup.sql"
    
    # 备份Redis数据
    docker-compose exec redis redis-cli BGSAVE
    docker cp "$(docker-compose ps -q redis):/data/dump.rdb" "$backup_dir/redis_backup.rdb"
    
    log_success "数据备份完成: $backup_dir"
}

# 显示帮助信息
show_help() {
    echo "游戏平台部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [操作] [参数]"
    echo ""
    echo "环境:"
    echo "  dev         开发环境"
    echo "  staging     测试环境"
    echo "  production  生产环境"
    echo ""
    echo "操作:"
    echo "  build       构建Docker镜像"
    echo "  start       启动所有服务"
    echo "  stop        停止所有服务"
    echo "  restart     重启所有服务"
    echo "  logs        查看日志 [服务名]"
    echo "  health      健康检查"
    echo "  migrate     执行数据库迁移"
    echo "  backup      备份数据"
    echo "  clean       清理所有资源"
    echo ""
    echo "示例:"
    echo "  $0 dev build"
    echo "  $0 production start"
    echo "  $0 dev logs api"
}

# 主函数
main() {
    local env=$1
    local action=$2
    local param=$3
    
    if [ $# -lt 2 ]; then
        show_help
        exit 1
    fi
    
    check_dependencies
    setup_environment "$env"
    
    case $action in
        "build")
            build_images
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            view_logs "$param"
            ;;
        "health")
            health_check
            ;;
        "migrate")
            run_migrations
            ;;
        "backup")
            backup_data
            ;;
        "clean")
            clean_resources
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
