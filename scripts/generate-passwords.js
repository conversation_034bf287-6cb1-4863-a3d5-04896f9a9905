const bcrypt = require('bcryptjs');

async function generatePasswordHashes() {
  const passwords = {
    'admin123': null,
    'agent123': null,
    'user123': null
  };

  for (const [password, _] of Object.entries(passwords)) {
    const hash = await bcrypt.hash(password, 12);
    passwords[password] = hash;
    console.log(`Password: ${password}`);
    console.log(`Hash: ${hash}`);
    console.log('---');
  }

  // 生成SQL更新语句
  console.log('\nSQL更新语句:');
  console.log(`UPDATE admins SET password_hash = '${passwords['admin123']}' WHERE username = 'superadmin';`);
  console.log(`UPDATE agents SET password_hash = '${passwords['agent123']}' WHERE username = 'testagent';`);
  console.log(`UPDATE users SET password_hash = '${passwords['user123']}' WHERE username = 'testuser';`);
}

generatePasswordHashes().catch(console.error);
