#!/bin/bash

# 停止开发环境服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_services() {
    log_info "停止开发服务..."
    
    # 检查PID文件是否存在
    if [ ! -d "logs" ]; then
        log_warning "未找到日志目录，可能服务未启动"
        return
    fi
    
    # 停止各个服务
    services=("api" "websocket" "admin" "agent" "player")
    
    for service in "${services[@]}"; do
        pid_file="logs/${service}.pid"
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                log_info "停止${service}服务 (PID: $pid)..."
                kill "$pid"
                rm "$pid_file"
                log_success "${service}服务已停止"
            else
                log_warning "${service}服务进程不存在 (PID: $pid)"
                rm "$pid_file"
            fi
        else
            log_warning "未找到${service}服务的PID文件"
        fi
    done
    
    # 停止Docker服务
    log_info "停止Docker服务..."
    docker-compose down
    
    log_success "所有服务已停止"
}

# 清理进程
cleanup_processes() {
    log_info "清理相关进程..."
    
    # 查找并杀死相关的Node.js进程
    pkill -f "next dev" || true
    pkill -f "nodemon" || true
    
    log_success "进程清理完成"
}

# 主函数
main() {
    log_info "停止游戏平台开发环境..."
    
    stop_services
    cleanup_processes
    
    echo ""
    log_success "🎮 游戏平台开发环境已停止"
    echo ""
    echo "💡 提示:"
    echo "  重新启动: ./scripts/dev-start.sh"
    echo "  查看日志: ls -la logs/"
}

# 执行主函数
main "$@"
