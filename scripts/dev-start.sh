#!/bin/bash

# 开发环境快速启动脚本
# 使用本地Node.js而不是Docker来避免构建问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本过低，需要18+，当前版本: $(node -v)"
        exit 1
    fi
    
    log_success "Node.js版本检查通过: $(node -v)"
}

# 检查MySQL和Redis
check_services() {
    log_info "检查MySQL和Redis服务..."
    
    # 启动MySQL和Redis（仅Docker）
    docker-compose up -d mysql redis
    
    # 等待服务启动
    log_info "等待数据库服务启动..."
    sleep 10
    
    log_success "数据库服务启动完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 后端API依赖
    log_info "安装API服务依赖..."
    cd backend/api && npm install && cd ../..
    
    # 后端WebSocket依赖
    log_info "安装WebSocket服务依赖..."
    cd backend/websocket && npm install && cd ../..
    
    # 前端依赖
    log_info "安装管理后台依赖..."
    cd frontend/admin && npm install && cd ../..
    
    log_info "安装代理端依赖..."
    cd frontend/agent && npm install && cd ../..
    
    log_info "安装玩家端依赖..."
    cd frontend/player && npm install && cd ../..
    
    log_success "所有依赖安装完成"
}

# 启动服务
start_services() {
    log_info "启动开发服务..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动API服务
    log_info "启动API服务 (端口: 4000)..."
    cd backend/api
    npm run dev > ../../logs/api.log 2>&1 &
    API_PID=$!
    cd ../..
    
    # 启动WebSocket服务
    log_info "启动WebSocket服务 (端口: 3002)..."
    cd backend/websocket
    npm run dev > ../../logs/websocket.log 2>&1 &
    WS_PID=$!
    cd ../..
    
    # 等待后端服务启动
    sleep 5
    
    # 启动前端服务
    log_info "启动管理后台 (端口: 3000)..."
    cd frontend/admin
    npm run dev > ../../logs/admin.log 2>&1 &
    ADMIN_PID=$!
    cd ../..
    
    log_info "启动代理端 (端口: 3005)..."
    cd frontend/agent
    PORT=3005 npm run dev > ../../logs/agent.log 2>&1 &
    AGENT_PID=$!
    cd ../..

    log_info "启动玩家端 (端口: 3006)..."
    cd frontend/player
    PORT=3006 npm run dev > ../../logs/player.log 2>&1 &
    PLAYER_PID=$!
    cd ../..
    
    # 保存PID到文件
    echo "$API_PID" > logs/api.pid
    echo "$WS_PID" > logs/websocket.pid
    echo "$ADMIN_PID" > logs/admin.pid
    echo "$AGENT_PID" > logs/agent.pid
    echo "$PLAYER_PID" > logs/player.pid
    
    log_success "所有服务启动完成！"
    
    echo ""
    echo "🎮 游戏平台开发环境已启动"
    echo ""
    echo "📱 访问地址:"
    echo "  玩家端:   http://localhost:3006"
    echo "  代理端:   http://localhost:3005"
    echo "  管理后台: http://localhost:3000"
    echo "  API服务:  http://localhost:4000"
    echo "  WS服务:   http://localhost:3002"
    echo ""
    echo "📋 日志文件:"
    echo "  API:      logs/api.log"
    echo "  WebSocket: logs/websocket.log"
    echo "  管理后台: logs/admin.log"
    echo "  代理端:   logs/agent.log"
    echo "  玩家端:   logs/player.log"
    echo ""
    echo "🛑 停止服务: ./scripts/dev-stop.sh"
    echo "📊 查看日志: tail -f logs/[服务名].log"
}

# 主函数
main() {
    log_info "启动游戏平台开发环境..."
    
    check_node
    check_services
    
    # 询问是否需要安装依赖
    read -p "是否需要安装/更新依赖？(y/N): " install_deps
    if [[ "$install_deps" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        install_dependencies
    fi
    
    start_services
}

# 执行主函数
main "$@"
