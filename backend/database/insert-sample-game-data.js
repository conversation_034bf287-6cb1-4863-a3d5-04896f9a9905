const mysql = require('mysql2/promise');

// 简单的UUID生成函数
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'games'
};

async function insertSampleGameData() {
  let connection;
  
  try {
    console.log('🎮 开始插入示例游戏数据...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 获取现有用户
    const [users] = await connection.execute('SELECT id, username FROM users LIMIT 3');
    if (users.length === 0) {
      console.log('❌ 没有找到用户数据，请先创建用户');
      return;
    }
    
    console.log(`📊 找到 ${users.length} 个用户`);
    users.forEach(user => console.log(`   - ${user.username} (${user.id})`));
    
    // 创建示例游戏房间
    const roomId = uuidv4();
    await connection.execute(`
      INSERT IGNORE INTO game_rooms (id, game_type, status, current_round, min_bet, max_bet, seats, spectators)
      VALUES (?, 'sangong', 'waiting', 0, 10.00, 1000.00, '[]', '[]')
    `, [roomId]);
    
    // 创建示例游戏记录
    const gameRecords = [];
    const gameParticipations = [];
    const sideWagers = [];
    
    for (let i = 0; i < 5; i++) {
      const gameId = uuidv4();
      const startedAt = new Date(Date.now() - (i + 1) * 24 * 60 * 60 * 1000); // 过去几天
      const endedAt = new Date(startedAt.getTime() + 5 * 60 * 1000); // 5分钟后结束
      
      // 游戏记录
      gameRecords.push([
        gameId,
        roomId,
        'sangong',
        i + 1,
        JSON.stringify({
          players: users.slice(0, 2).map((user, index) => ({
            userId: user.id,
            username: user.username,
            seat: index + 1
          }))
        }),
        JSON.stringify({
          winner: users[i % 2].id,
          winnerSeat: (i % 2) + 1,
          cards: {
            [users[0].id]: [{ suit: 'hearts', rank: 'K' }, { suit: 'spades', rank: 'Q' }, { suit: 'diamonds', rank: 'J' }],
            [users[1].id]: [{ suit: 'clubs', rank: '10' }, { suit: 'hearts', rank: '9' }, { suit: 'spades', rank: '8' }]
          }
        }),
        100.00 + i * 50, // 总奖池
        startedAt,
        endedAt
      ]);
      
      // 游戏参与记录
      users.slice(0, 2).forEach((user, index) => {
        const isWinner = (i % 2) === index;
        const betAmount = 50.00;
        const winAmount = isWinner ? 100.00 + i * 50 : 0.00;
        
        gameParticipations.push([
          uuidv4(),
          gameId,
          user.id,
          index + 1, // seat_position
          betAmount,
          winAmount,
          JSON.stringify([
            { suit: index === 0 ? 'hearts' : 'clubs', rank: index === 0 ? 'K' : '10' },
            { suit: index === 0 ? 'spades' : 'hearts', rank: index === 0 ? 'Q' : '9' },
            { suit: index === 0 ? 'diamonds' : 'spades', rank: index === 0 ? 'J' : '8' }
          ]),
          JSON.stringify({
            type: index === 0 ? 'sangong' : 'point',
            value: index === 0 ? 30 : 7
          }),
          isWinner
        ]);
      });
      
      // 旁注记录（如果有第三个用户）
      if (users.length > 2) {
        const betOnSeat = (i % 2) + 1;
        const isWinner = (i % 2) === 0; // 押注第一个座位
        
        sideWagers.push([
          uuidv4(),
          gameId,
          users[2].id,
          betOnSeat,
          20.00, // 旁注金额
          isWinner ? 40.00 : 0.00,
          isWinner
        ]);
      }
    }
    
    // 插入游戏记录
    console.log('🔄 插入游戏记录...');
    for (const record of gameRecords) {
      await connection.execute(`
        INSERT INTO game_records (id, room_id, game_type, round_number, players, game_result, total_pot, started_at, ended_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, record);
    }
    
    // 插入游戏参与记录
    console.log('🔄 插入游戏参与记录...');
    for (const participation of gameParticipations) {
      await connection.execute(`
        INSERT INTO game_participations (id, game_record_id, user_id, seat_position, bet_amount, win_amount, cards, hand_result, is_winner)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, participation);
    }
    
    // 插入旁注记录
    if (sideWagers.length > 0) {
      console.log('🔄 插入旁注记录...');
      for (const wager of sideWagers) {
        await connection.execute(`
          INSERT INTO side_wagers (id, game_record_id, user_id, target_seat, bet_amount, win_amount, is_winner)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, wager);
      }
    }
    
    console.log('✅ 示例游戏数据插入完成！');
    console.log(`📊 插入了 ${gameRecords.length} 条游戏记录`);
    console.log(`📊 插入了 ${gameParticipations.length} 条参与记录`);
    console.log(`📊 插入了 ${sideWagers.length} 条旁注记录`);
    
  } catch (error) {
    console.error('❌ 插入示例数据失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
insertSampleGameData();
