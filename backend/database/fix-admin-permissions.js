const mysql = require('mysql2/promise');

async function fixAdminPermissions() {
  let connection;
  
  try {
    console.log('🔧 修复管理员权限字段...');
    
    connection = await mysql.createConnection({
      host: '127.0.0.1',
      user: 'root',
      password: 'root',
      database: 'games'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 检查当前permissions字段
    const [currentAdmins] = await connection.execute(
      'SELECT username, permissions FROM admins WHERE username = ?',
      ['superadmin']
    );
    
    if (currentAdmins.length > 0) {
      console.log('当前permissions字段:', currentAdmins[0].permissions);
    }
    
    // 修复permissions字段为正确的JSON格式
    const correctPermissions = JSON.stringify(['all']);
    console.log('正确的permissions格式:', correctPermissions);
    
    await connection.execute(
      'UPDATE admins SET permissions = ? WHERE username = ?',
      [correctPermissions, 'superadmin']
    );
    
    console.log('✅ 管理员权限字段已修复');
    
    // 验证修复结果
    const [updatedAdmins] = await connection.execute(
      'SELECT username, permissions FROM admins WHERE username = ?',
      ['superadmin']
    );
    
    if (updatedAdmins.length > 0) {
      console.log('修复后permissions字段:', updatedAdmins[0].permissions);
      try {
        const parsed = JSON.parse(updatedAdmins[0].permissions);
        console.log('JSON解析成功:', parsed);
      } catch (e) {
        console.log('JSON解析失败:', e.message);
      }
    }
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

fixAdminPermissions();
