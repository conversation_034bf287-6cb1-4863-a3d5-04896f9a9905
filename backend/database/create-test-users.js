const mysql = require('mysql2/promise');

// 简单的UUID生成函数
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'games'
};

async function createTestUsers() {
  let connection;
  
  try {
    console.log('👥 开始创建测试用户...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 创建测试代理
    const agentId = uuidv4();
    const agentPasswordHash = '$2a$12$HbxCo67x2c/uq0alQZWhTu7FxT35vviqu1qyI3vLsJ9BaF5aZWK8i'; // 预生成的hash (密码: agent123)

    await connection.execute(`
      INSERT INTO agents (id, username, email, password_hash, balance, commission, status)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [agentId, 'testagent', '<EMAIL>', agentPasswordHash, 10000.00, 0.05, 'active']);
    
    console.log('✅ 创建测试代理: testagent');
    
    // 创建测试俱乐部
    const clubId = uuidv4();
    await connection.execute(`
      INSERT INTO clubs (id, name, agent_id, description, max_members, current_members, status)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [clubId, '测试俱乐部', agentId, '这是一个测试俱乐部', 1000, 0, 'active']);
    
    console.log('✅ 创建测试俱乐部: 测试俱乐部');
    
    // 创建测试用户
    const users = [
      { username: 'player1', email: '<EMAIL>', balance: 1000.00 },
      { username: 'player2', email: '<EMAIL>', balance: 800.00 },
      { username: 'player3', email: '<EMAIL>', balance: 1200.00 }
    ];
    
    for (const userData of users) {
      const userId = uuidv4();
      const passwordHash = '$2a$12$HbxCo67x2c/uq0alQZWhTu7FxT35vviqu1qyI3vLsJ9BaF5aZWK8i'; // 预生成的hash (密码: player123)

      await connection.execute(`
        INSERT INTO users (id, username, email, password_hash, balance, agent_id, club_id, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [userId, userData.username, userData.email, passwordHash, userData.balance, agentId, clubId, 'active']);

      console.log(`✅ 创建测试用户: ${userData.username} (余额: ¥${userData.balance})`);
    }
    
    // 更新俱乐部成员数
    await connection.execute(`
      UPDATE clubs SET current_members = ? WHERE id = ?
    `, [users.length, clubId]);
    
    console.log('✅ 测试用户创建完成！');
    console.log('📊 创建了:');
    console.log('   - 1 个代理账户');
    console.log('   - 1 个俱乐部');
    console.log(`   - ${users.length} 个用户账户`);
    
  } catch (error) {
    console.error('❌ 创建测试用户失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
createTestUsers();
