-- 游戏平台数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS games CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE games;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    agent_id VARCHAR(36),
    club_id VARCHAR(36),
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_agent_id (agent_id),
    INDEX idx_club_id (club_id),
    INDEX idx_status (status)
);

-- 代理表
CREATE TABLE IF NOT EXISTS agents (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    commission DECIMAL(5,4) DEFAULT 0.0000 COMMENT '返点比例，如0.0500表示5%',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- 俱乐部表
CREATE TABLE IF NOT EXISTS clubs (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    agent_id VARCHAR(36) NOT NULL,
    description TEXT,
    max_members INT DEFAULT 1000,
    current_members INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status),
    INDEX idx_name (name)
);

-- 游戏房间表
CREATE TABLE IF NOT EXISTS game_rooms (
    id VARCHAR(36) PRIMARY KEY,
    game_type ENUM('sangong', 'niuniu') NOT NULL,
    status ENUM('waiting', 'betting', 'dealing', 'playing', 'settling', 'finished') DEFAULT 'waiting',
    current_round INT DEFAULT 0,
    min_bet DECIMAL(10,2) DEFAULT 10.00,
    max_bet DECIMAL(10,2) DEFAULT 1000.00,
    seats JSON COMMENT '座位信息，存储JSON格式数据',
    spectators JSON COMMENT '旁观者列表',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_game_type (game_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 游戏记录表
CREATE TABLE IF NOT EXISTS game_records (
    id VARCHAR(36) PRIMARY KEY,
    room_id VARCHAR(36) NOT NULL,
    game_type ENUM('sangong', 'niuniu') NOT NULL,
    round_number INT NOT NULL,
    players JSON NOT NULL COMMENT '参与玩家信息',
    game_result JSON NOT NULL COMMENT '游戏结果',
    total_pot DECIMAL(15,2) NOT NULL COMMENT '总奖池',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES game_rooms(id) ON DELETE CASCADE,
    INDEX idx_room_id (room_id),
    INDEX idx_game_type (game_type),
    INDEX idx_created_at (created_at)
);

-- 交易记录表
CREATE TABLE IF NOT EXISTS transactions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36),
    agent_id VARCHAR(36),
    type ENUM('deposit', 'withdraw', 'bet', 'win', 'commission', 'transfer') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    description TEXT,
    reference_id VARCHAR(36) COMMENT '关联ID，如游戏记录ID',
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    user_type ENUM('user', 'agent', 'admin') NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token_hash (token_hash),
    INDEX idx_expires_at (expires_at)
);

-- 系统管理员表
CREATE TABLE IF NOT EXISTS admins (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('super_admin', 'admin', 'operator') DEFAULT 'operator',
    permissions JSON COMMENT '权限列表',
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
);

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('game.min_players', '2', '游戏最少玩家数'),
('game.max_seats', '10', '游戏最大座位数'),
('game.default_min_bet', '10', '默认最小下注金额'),
('game.default_max_bet', '1000', '默认最大下注金额'),
('game.betting_time', '30', '下注时间（秒）'),
('game.sangong_enabled', 'true', '三公游戏是否启用'),
('game.niuniu_enabled', 'true', '牛牛游戏是否启用'),
('system.maintenance_mode', 'false', '系统维护模式'),
('system.registration_enabled', 'true', '是否允许用户注册');

-- 创建默认超级管理员账户 (密码: admin123)
INSERT INTO admins (id, username, email, password_hash, role, permissions) VALUES
('admin-super-001', 'superadmin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'super_admin', '["all"]');

-- 添加外键约束
ALTER TABLE users ADD CONSTRAINT fk_users_agent FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL;
ALTER TABLE users ADD CONSTRAINT fk_users_club FOREIGN KEY (club_id) REFERENCES clubs(id) ON DELETE SET NULL;
