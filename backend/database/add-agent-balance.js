const mysql = require('mysql2/promise');

async function addBalance() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      user: 'root',
      password: 'root',
      database: 'games'
    });
    
    // 直接更新代理余额
    await connection.execute(
      'UPDATE agents SET balance = balance + 50000 WHERE username = ?',
      ['testagent']
    );
    
    console.log('✅ 代理余额已增加50000元');
    
    // 验证结果
    const [agents] = await connection.execute(
      'SELECT username, balance FROM agents WHERE username = ?',
      ['testagent']
    );
    
    if (agents.length > 0) {
      console.log(`代理 ${agents[0].username} 当前余额: ¥${agents[0].balance}`);
    }
    
    await connection.end();
  } catch (error) {
    console.error('错误:', error.message);
  }
}

addBalance();
