const mysql = require('mysql2/promise');
const readline = require('readline');
require('dotenv').config({ path: '../api/.env' });

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function resetDatabase() {
  let connection;
  
  try {
    console.log('⚠️  数据库重置工具');
    console.log('================================');
    console.log('此操作将删除所有数据并重新创建数据库！');
    console.log('');
    
    const confirm1 = await askQuestion('确定要继续吗？(输入 yes 继续): ');
    if (confirm1.toLowerCase() !== 'yes') {
      console.log('❌ 操作已取消');
      return;
    }
    
    const confirm2 = await askQuestion('再次确认：这将删除所有游戏数据！(输入 RESET 继续): ');
    if (confirm2 !== 'RESET') {
      console.log('❌ 操作已取消');
      return;
    }
    
    console.log('🔄 开始重置数据库...');
    
    // 连接到MySQL服务器
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root'
    });
    
    console.log('✅ 成功连接到MySQL服务器');
    
    // 删除数据库
    console.log('🔄 删除现有数据库...');
    await connection.execute('DROP DATABASE IF EXISTS games');
    
    console.log('✅ 数据库删除完成');
    
    // 重新初始化数据库
    const { initDatabase } = require('./init.js');
    await connection.end();
    
    console.log('🔄 重新初始化数据库...');
    await initDatabase();
    
    console.log('✅ 数据库重置完成！');
    
  } catch (error) {
    console.error('❌ 数据库重置失败：', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
    rl.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  resetDatabase().catch(console.error);
}

module.exports = { resetDatabase };
