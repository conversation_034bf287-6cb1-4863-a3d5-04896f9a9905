const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'games'
};

async function fixAdminPassword() {
  let connection;
  
  try {
    console.log('🔧 修复管理员密码...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查现有管理员
    const [existingAdmins] = await connection.execute('SELECT id, username, email, password_hash FROM admins');
    console.log('现有管理员账户:');
    existingAdmins.forEach(admin => {
      console.log(`  - ${admin.username} (${admin.email})`);
      console.log(`    密码hash: ${admin.password_hash.substring(0, 20)}...`);
    });
    
    // 生成正确的密码hash (使用bcryptjs)
    const password = 'admin123';
    const saltRounds = 12;
    const correctPasswordHash = await bcrypt.hash(password, saltRounds);
    
    console.log('\n🔄 生成新的密码hash...');
    console.log(`新密码hash: ${correctPasswordHash.substring(0, 20)}...`);
    
    // 验证新hash是否正确
    const isValid = await bcrypt.compare(password, correctPasswordHash);
    console.log(`密码验证: ${isValid ? '✅ 成功' : '❌ 失败'}`);
    
    if (isValid) {
      // 更新管理员密码
      await connection.execute(
        'UPDATE admins SET password_hash = ? WHERE username = ?',
        [correctPasswordHash, 'superadmin']
      );
      
      console.log('✅ 管理员密码已更新');
      
      // 验证更新后的密码
      const [updatedAdmins] = await connection.execute(
        'SELECT password_hash FROM admins WHERE username = ?',
        ['superadmin']
      );
      
      if (updatedAdmins.length > 0) {
        const testValid = await bcrypt.compare(password, updatedAdmins[0].password_hash);
        console.log(`更新后密码验证: ${testValid ? '✅ 成功' : '❌ 失败'}`);
      }
    }
    
    console.log('\n🎯 管理员登录信息:');
    console.log('   用户名: superadmin');
    console.log('   密码: admin123');
    console.log('   邮箱: <EMAIL>');
    
  } catch (error) {
    console.error('❌ 修复管理员密码失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
fixAdminPassword();
