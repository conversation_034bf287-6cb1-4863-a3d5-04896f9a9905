const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'games'
};

async function updateTransactionTypes() {
  let connection;
  
  try {
    console.log('🔄 更新交易记录表的类型枚举值...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查当前表结构
    console.log('📊 检查当前表结构...');
    const [columns] = await connection.execute('DESCRIBE transactions');
    const typeColumn = columns.find(col => col.Field === 'type');
    
    if (typeColumn) {
      console.log('当前type字段定义:', typeColumn.Type);
    }
    
    // 更新type字段的枚举值
    console.log('🔄 更新type字段枚举值...');
    await connection.execute(`
      ALTER TABLE transactions 
      MODIFY COLUMN type ENUM(
        'deposit', 'withdraw', 'bet', 'win', 'commission', 'transfer', 
        'adjustment', 'bonus', 'penalty', 'refund'
      ) NOT NULL
    `);
    
    console.log('✅ 交易记录表类型枚举值更新成功！');
    
    // 验证更新结果
    console.log('📊 验证更新结果...');
    const [updatedColumns] = await connection.execute('DESCRIBE transactions');
    const updatedTypeColumn = updatedColumns.find(col => col.Field === 'type');
    
    if (updatedTypeColumn) {
      console.log('更新后type字段定义:', updatedTypeColumn.Type);
    }
    
    console.log('🎯 现在支持的交易类型:');
    console.log('   - deposit: 充值');
    console.log('   - withdraw: 提现');
    console.log('   - bet: 下注');
    console.log('   - win: 赢取');
    console.log('   - commission: 佣金');
    console.log('   - transfer: 转账');
    console.log('   - adjustment: 余额调整');
    console.log('   - bonus: 奖励');
    console.log('   - penalty: 扣除');
    console.log('   - refund: 退款');
    
  } catch (error) {
    console.error('❌ 更新失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
updateTransactionTypes();
