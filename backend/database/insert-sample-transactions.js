const mysql = require('mysql2/promise');

// 简单的UUID生成函数
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'games'
};

async function insertSampleTransactions() {
  let connection;
  
  try {
    console.log('💰 开始插入示例交易数据...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 获取现有用户和代理
    const [users] = await connection.execute('SELECT id, username, balance FROM users');
    const [agents] = await connection.execute('SELECT id, username, balance FROM agents');
    const [gameRecords] = await connection.execute('SELECT id, total_pot FROM game_records ORDER BY started_at DESC LIMIT 10');
    
    if (users.length === 0 || agents.length === 0) {
      console.log('❌ 没有找到用户或代理数据');
      return;
    }
    
    console.log(`📊 找到 ${users.length} 个用户, ${agents.length} 个代理, ${gameRecords.length} 个游戏记录`);
    
    const transactions = [];
    let transactionCount = 0;
    
    // 为每个用户创建交易记录
    for (const user of users) {
      let currentBalance = parseFloat(user.balance);
      const agent = agents[0]; // 使用第一个代理
      
      // 1. 代理给用户充值（上分）
      const depositAmount = 500 + Math.random() * 1000; // 500-1500
      const depositTransaction = {
        id: uuidv4(),
        user_id: user.id,
        agent_id: agent.id,
        type: 'deposit',
        amount: depositAmount,
        balance_before: currentBalance,
        balance_after: currentBalance + depositAmount,
        description: `代理 ${agent.username} 给用户 ${user.username} 充值`,
        reference_id: null,
        status: 'completed',
        created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // 过去7天内随机时间
      };
      transactions.push(depositTransaction);
      currentBalance += depositAmount;
      transactionCount++;
      
      // 2. 游戏下注和输赢记录
      for (let i = 0; i < 3; i++) {
        const betAmount = 50 + Math.random() * 200; // 50-250
        const isWin = Math.random() > 0.4; // 60%胜率
        const gameRecord = gameRecords[i % gameRecords.length];
        
        // 下注记录
        const betTransaction = {
          id: uuidv4(),
          user_id: user.id,
          agent_id: null,
          type: 'bet',
          amount: -betAmount, // 负数表示支出
          balance_before: currentBalance,
          balance_after: currentBalance - betAmount,
          description: `游戏下注 - ${user.username}`,
          reference_id: gameRecord.id,
          status: 'completed',
          created_at: new Date(Date.now() - Math.random() * 5 * 24 * 60 * 60 * 1000) // 过去5天内
        };
        transactions.push(betTransaction);
        currentBalance -= betAmount;
        transactionCount++;
        
        // 如果赢了，添加赢取记录
        if (isWin) {
          const winAmount = betAmount * (1.5 + Math.random() * 1.5); // 1.5-3倍赔率
          const winTransaction = {
            id: uuidv4(),
            user_id: user.id,
            agent_id: null,
            type: 'win',
            amount: winAmount,
            balance_before: currentBalance,
            balance_after: currentBalance + winAmount,
            description: `游戏赢取 - ${user.username}`,
            reference_id: gameRecord.id,
            status: 'completed',
            created_at: new Date(betTransaction.created_at.getTime() + 5 * 60 * 1000) // 下注5分钟后
          };
          transactions.push(winTransaction);
          currentBalance += winAmount;
          transactionCount++;
        }
      }
      
      // 3. 如果余额过高，代理给用户提现（下分）
      if (currentBalance > 2000) {
        const withdrawAmount = 500 + Math.random() * 1000; // 500-1500
        const withdrawTransaction = {
          id: uuidv4(),
          user_id: user.id,
          agent_id: agent.id,
          type: 'withdraw',
          amount: -withdrawAmount, // 负数表示支出
          balance_before: currentBalance,
          balance_after: currentBalance - withdrawAmount,
          description: `代理 ${agent.username} 给用户 ${user.username} 提现`,
          reference_id: null,
          status: 'completed',
          created_at: new Date(Date.now() - Math.random() * 2 * 24 * 60 * 60 * 1000) // 过去2天内
        };
        transactions.push(withdrawTransaction);
        currentBalance -= withdrawAmount;
        transactionCount++;
      }
      
      // 4. 代理佣金记录
      const commissionAmount = 10 + Math.random() * 50; // 10-60
      const commissionTransaction = {
        id: uuidv4(),
        user_id: null,
        agent_id: agent.id,
        type: 'commission',
        amount: commissionAmount,
        balance_before: parseFloat(agent.balance),
        balance_after: parseFloat(agent.balance) + commissionAmount,
        description: `代理佣金 - 来自用户 ${user.username} 的游戏`,
        reference_id: null,
        status: 'completed',
        created_at: new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000) // 过去3天内
      };
      transactions.push(commissionTransaction);
      transactionCount++;
      
      // 更新用户余额
      await connection.execute(
        'UPDATE users SET balance = ? WHERE id = ?',
        [currentBalance.toFixed(2), user.id]
      );
    }
    
    // 添加一些用户间转账记录
    if (users.length >= 2) {
      const transferAmount = 100 + Math.random() * 300; // 100-400
      const fromUser = users[0];
      const toUser = users[1];
      
      // 转出记录
      const transferOutTransaction = {
        id: uuidv4(),
        user_id: fromUser.id,
        agent_id: null,
        type: 'transfer',
        amount: -transferAmount,
        balance_before: parseFloat(fromUser.balance),
        balance_after: parseFloat(fromUser.balance) - transferAmount,
        description: `转账给用户 ${toUser.username}`,
        reference_id: null,
        status: 'completed',
        created_at: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000) // 过去1天内
      };
      transactions.push(transferOutTransaction);
      transactionCount++;
      
      // 转入记录
      const transferInTransaction = {
        id: uuidv4(),
        user_id: toUser.id,
        agent_id: null,
        type: 'transfer',
        amount: transferAmount,
        balance_before: parseFloat(toUser.balance),
        balance_after: parseFloat(toUser.balance) + transferAmount,
        description: `收到来自用户 ${fromUser.username} 的转账`,
        reference_id: null,
        status: 'completed',
        created_at: transferOutTransaction.created_at
      };
      transactions.push(transferInTransaction);
      transactionCount++;
    }
    
    // 批量插入交易记录
    console.log('🔄 插入交易记录...');
    for (const transaction of transactions) {
      await connection.execute(`
        INSERT INTO transactions (id, user_id, agent_id, type, amount, balance_before, balance_after, description, reference_id, status, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transaction.id,
        transaction.user_id,
        transaction.agent_id,
        transaction.type,
        transaction.amount,
        transaction.balance_before,
        transaction.balance_after,
        transaction.description,
        transaction.reference_id,
        transaction.status,
        transaction.created_at
      ]);
    }
    
    console.log('✅ 示例交易数据插入完成！');
    console.log(`📊 插入了 ${transactionCount} 条交易记录`);
    console.log('📋 交易类型统计:');
    
    const typeStats = {};
    transactions.forEach(t => {
      typeStats[t.type] = (typeStats[t.type] || 0) + 1;
    });
    
    Object.entries(typeStats).forEach(([type, count]) => {
      const typeNames = {
        deposit: '充值',
        withdraw: '提现',
        bet: '下注',
        win: '赢取',
        commission: '佣金',
        transfer: '转账'
      };
      console.log(`   - ${typeNames[type] || type}: ${count} 条`);
    });
    
  } catch (error) {
    console.error('❌ 插入示例交易数据失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
insertSampleTransactions();
