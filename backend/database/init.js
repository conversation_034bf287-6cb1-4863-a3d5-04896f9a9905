const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '../api/.env' });

async function initDatabase() {
  let connection;
  
  try {
    console.log('🔄 开始初始化数据库...');
    
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      multipleStatements: true
    });
    
    console.log('✅ 成功连接到MySQL服务器');
    
    // 创建数据库
    console.log('🔄 创建数据库...');
    await connection.execute('CREATE DATABASE IF NOT EXISTS games CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');

    // 关闭当前连接，重新连接到games数据库
    await connection.end();

    connection = await mysql.createConnection({
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: 'games',
      multipleStatements: true
    });

    // 执行表创建脚本
    console.log('🔄 创建数据表...');
    await createTables(connection);
    
    console.log('✅ 数据库初始化完成！');
    console.log('📊 已创建以下表：');
    console.log('   - users (用户表)');
    console.log('   - agents (代理表)');
    console.log('   - clubs (俱乐部表)');
    console.log('   - game_rooms (游戏房间表)');
    console.log('   - game_records (游戏记录表)');
    console.log('   - game_participations (游戏参与记录表)');
    console.log('   - side_wagers (旁注记录表)');
    console.log('   - transactions (交易记录表)');
    console.log('   - user_sessions (用户会话表)');
    console.log('   - admins (管理员表)');
    console.log('   - system_configs (系统配置表)');
    console.log('');
    console.log('🔑 默认管理员账户：');
    console.log('   用户名: superadmin');
    console.log('   密码: admin123');
    console.log('   邮箱: <EMAIL>');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败：', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 检查数据库连接
async function checkConnection() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root'
    });
    
    await connection.execute('SELECT 1');
    console.log('✅ 数据库连接测试成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败：', error.message);
    console.error('请检查以下配置：');
    console.error(`   主机: ${process.env.DB_HOST || '127.0.0.1'}`);
    console.error(`   端口: ${process.env.DB_PORT || 3306}`);
    console.error(`   用户: ${process.env.DB_USER || 'root'}`);
    console.error('   密码: [已隐藏]');
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function main() {
  console.log('🎮 游戏平台数据库初始化工具');
  console.log('================================');
  
  // 检查数据库连接
  const isConnected = await checkConnection();
  if (!isConnected) {
    process.exit(1);
  }
  
  // 初始化数据库
  await initDatabase();
}

// 创建数据表
async function createTables(connection) {
  // 用户表
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS users (
      id VARCHAR(36) PRIMARY KEY,
      username VARCHAR(50) NOT NULL UNIQUE,
      email VARCHAR(100) UNIQUE,
      phone VARCHAR(20) UNIQUE,
      password_hash VARCHAR(255) NOT NULL,
      balance DECIMAL(15,2) DEFAULT 0.00,
      agent_id VARCHAR(36),
      club_id VARCHAR(36),
      status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
      last_login_at TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_username (username),
      INDEX idx_email (email),
      INDEX idx_phone (phone),
      INDEX idx_agent_id (agent_id),
      INDEX idx_club_id (club_id),
      INDEX idx_status (status)
    )
  `);

  // 代理表
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS agents (
      id VARCHAR(36) PRIMARY KEY,
      username VARCHAR(50) NOT NULL UNIQUE,
      email VARCHAR(100) NOT NULL UNIQUE,
      phone VARCHAR(20),
      password_hash VARCHAR(255) NOT NULL,
      balance DECIMAL(15,2) DEFAULT 0.00,
      commission DECIMAL(5,4) DEFAULT 0.0000 COMMENT '返点比例，如0.0500表示5%',
      status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
      last_login_at TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_username (username),
      INDEX idx_email (email),
      INDEX idx_status (status)
    )
  `);

  // 俱乐部表 (代理即俱乐部，一对一关系)
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS clubs (
      id VARCHAR(36) PRIMARY KEY,
      agent_id VARCHAR(36) NOT NULL UNIQUE COMMENT '代理ID，一个代理对应一个俱乐部',
      name VARCHAR(100) NOT NULL,
      description TEXT,
      max_members INT DEFAULT 1000,
      current_members INT DEFAULT 0,
      status ENUM('active', 'inactive') DEFAULT 'active',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_agent_id (agent_id),
      INDEX idx_status (status),
      INDEX idx_name (name)
    )
  `);

  // 游戏房间表 (每个游戏类型只有一个房间)
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS game_rooms (
      id VARCHAR(36) PRIMARY KEY,
      game_type ENUM('sangong', 'niuniu') NOT NULL UNIQUE COMMENT '游戏类型，每种游戏只有一个房间',
      status ENUM('waiting', 'betting', 'dealing', 'playing', 'settling', 'finished') DEFAULT 'waiting',
      current_round INT DEFAULT 0,
      min_bet DECIMAL(10,2) DEFAULT 10.00,
      max_bet DECIMAL(10,2) DEFAULT 1000.00,
      seats JSON COMMENT '座位信息，存储JSON格式数据',
      spectators JSON COMMENT '旁观者列表',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_game_type (game_type),
      INDEX idx_status (status),
      INDEX idx_created_at (created_at)
    )
  `);

  // 游戏记录表
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS game_records (
      id VARCHAR(36) PRIMARY KEY,
      room_id VARCHAR(36) NOT NULL,
      game_type ENUM('sangong', 'niuniu') NOT NULL,
      round_number INT NOT NULL,
      players JSON NOT NULL COMMENT '参与玩家信息',
      game_result JSON NOT NULL COMMENT '游戏结果',
      total_pot DECIMAL(15,2) NOT NULL COMMENT '总奖池',
      started_at TIMESTAMP NOT NULL,
      ended_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_room_id (room_id),
      INDEX idx_game_type (game_type),
      INDEX idx_started_at (started_at),
      INDEX idx_created_at (created_at)
    )
  `);

  // 游戏参与记录表
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS game_participations (
      id VARCHAR(36) PRIMARY KEY,
      game_record_id VARCHAR(36) NOT NULL,
      user_id VARCHAR(36) NOT NULL,
      seat_position INT NOT NULL,
      bet_amount DECIMAL(15,2) NOT NULL,
      win_amount DECIMAL(15,2) DEFAULT 0.00,
      cards JSON,
      hand_result JSON,
      is_winner BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_game_record_id (game_record_id),
      INDEX idx_user_id (user_id)
    )
  `);

  // 旁注记录表
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS side_wagers (
      id VARCHAR(36) PRIMARY KEY,
      game_record_id VARCHAR(36) NOT NULL,
      user_id VARCHAR(36) NOT NULL,
      target_seat INT NOT NULL,
      bet_amount DECIMAL(15,2) NOT NULL,
      win_amount DECIMAL(15,2) DEFAULT 0.00,
      is_winner BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_game_record_id (game_record_id),
      INDEX idx_user_id (user_id)
    )
  `);

  // 交易记录表
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS transactions (
      id VARCHAR(36) PRIMARY KEY,
      user_id VARCHAR(36),
      agent_id VARCHAR(36),
      type ENUM('deposit', 'withdraw', 'bet', 'win', 'commission', 'transfer') NOT NULL,
      amount DECIMAL(15,2) NOT NULL,
      balance_before DECIMAL(15,2) NOT NULL,
      balance_after DECIMAL(15,2) NOT NULL,
      description TEXT,
      reference_id VARCHAR(36) COMMENT '关联ID，如游戏记录ID',
      status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_user_id (user_id),
      INDEX idx_agent_id (agent_id),
      INDEX idx_type (type),
      INDEX idx_status (status),
      INDEX idx_created_at (created_at)
    )
  `);

  // 用户会话表
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS user_sessions (
      id VARCHAR(36) PRIMARY KEY,
      user_id VARCHAR(36) NOT NULL,
      user_type ENUM('user', 'agent', 'admin') NOT NULL,
      token_hash VARCHAR(255) NOT NULL,
      refresh_token_hash VARCHAR(255),
      ip_address VARCHAR(45),
      user_agent TEXT,
      expires_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_user_id (user_id),
      INDEX idx_token_hash (token_hash),
      INDEX idx_expires_at (expires_at)
    )
  `);

  // 系统管理员表
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS admins (
      id VARCHAR(36) PRIMARY KEY,
      username VARCHAR(50) NOT NULL UNIQUE,
      email VARCHAR(100) NOT NULL UNIQUE,
      password_hash VARCHAR(255) NOT NULL,
      role ENUM('super_admin', 'admin', 'operator') DEFAULT 'operator',
      permissions JSON COMMENT '权限列表',
      status ENUM('active', 'inactive') DEFAULT 'active',
      last_login_at TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_username (username),
      INDEX idx_email (email),
      INDEX idx_role (role),
      INDEX idx_status (status)
    )
  `);

  // 系统配置表
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS system_configs (
      id INT AUTO_INCREMENT PRIMARY KEY,
      config_key VARCHAR(100) NOT NULL UNIQUE,
      config_value TEXT NOT NULL,
      description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_config_key (config_key)
    )
  `);

  console.log('✅ 数据表创建完成');

  // 添加外键约束
  console.log('🔄 添加外键约束...');

  const foreignKeys = [
    'ALTER TABLE clubs ADD CONSTRAINT fk_clubs_agent FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE',
    'ALTER TABLE game_participations ADD CONSTRAINT fk_game_participations_game_record FOREIGN KEY (game_record_id) REFERENCES game_records(id) ON DELETE CASCADE',
    'ALTER TABLE game_participations ADD CONSTRAINT fk_game_participations_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE',
    'ALTER TABLE side_wagers ADD CONSTRAINT fk_side_wagers_game_record FOREIGN KEY (game_record_id) REFERENCES game_records(id) ON DELETE CASCADE',
    'ALTER TABLE side_wagers ADD CONSTRAINT fk_side_wagers_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE'
  ];

  for (const fkSql of foreignKeys) {
    try {
      await connection.execute(fkSql);
    } catch (error) {
      // 忽略已存在的约束错误
      if (!error.message.includes('Duplicate key name') && !error.message.includes('already exists')) {
        console.warn('外键约束添加警告:', error.message);
      }
    }
  }

  // 插入默认数据
  console.log('🔄 插入默认配置数据...');
  await insertDefaultData(connection);
}

// 插入默认数据
async function insertDefaultData(connection) {
  // 插入系统配置
  const configs = [
    ['game.min_players', '2', '游戏最少玩家数'],
    ['game.max_seats', '10', '游戏最大座位数'],
    ['game.default_min_bet', '10', '默认最小下注金额'],
    ['game.default_max_bet', '1000', '默认最大下注金额'],
    ['game.betting_time', '30', '下注时间（秒）'],
    ['game.sangong_enabled', 'true', '三公游戏是否启用'],
    ['game.niuniu_enabled', 'true', '牛牛游戏是否启用'],
    ['system.maintenance_mode', 'false', '系统维护模式'],
    ['system.registration_enabled', 'true', '是否允许用户注册']
  ];

  for (const [key, value, desc] of configs) {
    await connection.execute(
      'INSERT IGNORE INTO system_configs (config_key, config_value, description) VALUES (?, ?, ?)',
      [key, value, desc]
    );
  }

  // 创建默认超级管理员账户 (密码: admin123)
  await connection.execute(`
    INSERT IGNORE INTO admins (id, username, email, password_hash, role, permissions) VALUES
    ('admin-super-001', 'superadmin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'super_admin', '["all"]')
  `);

  // 创建默认游戏房间
  const defaultSeats = JSON.stringify(Array.from({ length: 10 }, (_, i) => ({
    position: i + 1,
    userId: null,
    username: null,
    balance: 0,
    bet: 0,
    cards: [],
    isReady: false,
    sideWagers: []
  })));

  await connection.execute(`
    INSERT IGNORE INTO game_rooms (id, game_type, seats, spectators) VALUES
    ('room-sangong-001', 'sangong', ?, '[]'),
    ('room-niuniu-001', 'niuniu', ?, '[]')
  `, [defaultSeats, defaultSeats]);

  console.log('✅ 默认数据插入完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { initDatabase, checkConnection };
