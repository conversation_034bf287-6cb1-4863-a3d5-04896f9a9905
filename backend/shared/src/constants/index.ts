import { Rank, Suit } from '../types';

// 游戏配置常量
export const GAME_CONFIG = {
  // 房间配置
  MAX_SEATS: 10,
  MIN_PLAYERS: 2,
  MAX_SPECTATORS: 100,
  
  // 下注配置
  DEFAULT_MIN_BET: 10,
  DEFAULT_MAX_BET: 1000,
  
  // 时间配置 (毫秒)
  BETTING_TIME: 30000,    // 下注时间 30秒
  READY_TIME: 15000,      // 准备时间 15秒
  DEALING_TIME: 3000,     // 发牌动画时间 3秒
  SETTLING_TIME: 5000,    // 结算显示时间 5秒
  
  // 游戏轮次
  MAX_ROUNDS: 100
};

// 扑克牌配置
export const CARD_CONFIG = {
  // 牌的点数值映射
  RANK_VALUES: {
    [Rank.ACE]: 1,
    [Rank.TWO]: 2,
    [Rank.THREE]: 3,
    [Rank.FOUR]: 4,
    [Rank.FIVE]: 5,
    [Rank.SIX]: 6,
    [Rank.SEVEN]: 7,
    [Rank.EIGHT]: 8,
    [Rank.NINE]: 9,
    [Rank.TEN]: 10,
    [Rank.JACK]: 10,    // J、Q、K在牛牛中都是10点
    [Rank.QUEEN]: 10,
    [Rank.KING]: 10
  },
  
  // 三公中的公牌
  SANGONG_HONOR_CARDS: [Rank.JACK, Rank.QUEEN, Rank.KING],
  
  // 完整牌组 (52张，去掉大小王)
  FULL_DECK: (() => {
    const deck = [];
    for (const suit of Object.values(Suit)) {
      for (const rank of Object.values(Rank)) {
        deck.push({ suit, rank });
      }
    }
    return deck;
  })()
};

// 三公游戏常量
export const SANGONG_CONFIG = {
  CARDS_PER_PLAYER: 3,
  
  // 牌型优先级 (数字越大优先级越高)
  HAND_RANKS: {
    POINT_CARD: 1,      // 点数牌
    MIXED_HONOR: 2,     // 混三公
    SMALL_TRIPLE: 3,    // 小三公
    BIG_TRIPLE: 4       // 大三公
  },
  
  // 大三公顺序 (KKK > QQQ > JJJ)
  BIG_TRIPLE_ORDER: {
    [Rank.KING]: 3,
    [Rank.QUEEN]: 2,
    [Rank.JACK]: 1
  },
  
  // 小三公顺序 (101010 > 999 > ... > AAA)
  SMALL_TRIPLE_ORDER: {
    [Rank.TEN]: 10,
    [Rank.NINE]: 9,
    [Rank.EIGHT]: 8,
    [Rank.SEVEN]: 7,
    [Rank.SIX]: 6,
    [Rank.FIVE]: 5,
    [Rank.FOUR]: 4,
    [Rank.THREE]: 3,
    [Rank.TWO]: 2,
    [Rank.ACE]: 1
  }
};

// 牛牛游戏常量
export const NIUNIU_CONFIG = {
  CARDS_PER_PLAYER: 5,
  
  // 牌型优先级 (数字越大优先级越高)
  HAND_RANKS: {
    NO_NIU: 0,          // 无牛
    NIU_1: 1,           // 牛1
    NIU_2: 2,           // 牛2
    NIU_3: 3,           // 牛3
    NIU_4: 4,           // 牛4
    NIU_5: 5,           // 牛5
    NIU_6: 6,           // 牛6
    NIU_7: 7,           // 牛7
    NIU_8: 8,           // 牛8
    NIU_9: 9,           // 牛9
    NIU_NIU: 10,        // 牛牛
    WU_HUA_NIU: 11,     // 五花牛 (五张牌均为J、Q、K)
    SI_ZHA: 12,         // 四炸 (四张牌点数相同)
    WU_XIAO_NIU: 13     // 五小牛 (五张牌相加点数小于等于10)
  },
  
  // 赔率配置
  PAYOUT_MULTIPLIERS: {
    NO_NIU: 1,
    NIU_1: 1,
    NIU_2: 1,
    NIU_3: 1,
    NIU_4: 1,
    NIU_5: 1,
    NIU_6: 1,
    NIU_7: 2,           // 牛7开始有倍数
    NIU_8: 2,
    NIU_9: 3,
    NIU_NIU: 4,
    WU_HUA_NIU: 5,
    SI_ZHA: 6,
    WU_XIAO_NIU: 8
  }
};

// 数据库表名
export const DB_TABLES = {
  USERS: 'users',
  AGENTS: 'agents',
  CLUBS: 'clubs',
  GAME_ROOMS: 'game_rooms',
  GAME_RECORDS: 'game_records',
  TRANSACTIONS: 'transactions',
  USER_SESSIONS: 'user_sessions'
};

// Redis键前缀
export const REDIS_KEYS = {
  USER_SESSION: 'session:user:',
  GAME_ROOM: 'room:',
  GAME_STATE: 'game:state:',
  USER_ONLINE: 'online:users',
  ROOM_PLAYERS: 'room:players:'
};

// API路径
export const API_PATHS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh'
  },
  
  // 用户相关
  USERS: {
    PROFILE: '/api/users/profile',
    BALANCE: '/api/users/balance',
    HISTORY: '/api/users/history'
  },
  
  // 代理相关
  AGENTS: {
    LIST: '/api/agents',
    CREATE: '/api/agents',
    UPDATE: '/api/agents/:id',
    DELETE: '/api/agents/:id'
  },
  
  // 俱乐部相关
  CLUBS: {
    LIST: '/api/clubs',
    JOIN: '/api/clubs/:id/join',
    LEAVE: '/api/clubs/:id/leave',
    MEMBERS: '/api/clubs/:id/members'
  },
  
  // 游戏相关
  GAMES: {
    ROOMS: '/api/games/rooms',
    JOIN: '/api/games/rooms/:id/join',
    LEAVE: '/api/games/rooms/:id/leave',
    HISTORY: '/api/games/history'
  }
};

// WebSocket事件
export const WS_EVENTS = {
  CONNECTION: 'connection',
  DISCONNECT: 'disconnect',
  JOIN_ROOM: 'join_room',
  LEAVE_ROOM: 'leave_room',
  PLACE_BET: 'place_bet',
  READY: 'ready',
  GAME_UPDATE: 'game_update',
  CHAT: 'chat',
  ERROR: 'error'
};

// 错误代码
export const ERROR_CODES = {
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  
  // 用户错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  
  // 游戏错误
  ROOM_FULL: 'ROOM_FULL',
  GAME_IN_PROGRESS: 'GAME_IN_PROGRESS',
  INVALID_BET: 'INVALID_BET',
  NOT_YOUR_TURN: 'NOT_YOUR_TURN',
  
  // 系统错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR'
};
