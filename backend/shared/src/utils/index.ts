import { Card, Suit, Rank } from '../types';
import { CARD_CONFIG } from '../constants';

/**
 * 生成UUID
 */
export function generateId(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 创建一副完整的扑克牌
 */
export function createDeck(): Card[] {
  const deck: Card[] = [];
  
  for (const suit of Object.values(Suit)) {
    for (const rank of Object.values(Rank)) {
      deck.push({
        suit,
        rank,
        value: CARD_CONFIG.RANK_VALUES[rank]
      });
    }
  }
  
  return deck;
}

/**
 * 洗牌算法 (Fisher-Yates)
 */
export function shuffleDeck(deck: Card[]): Card[] {
  const shuffled = [...deck];
  
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  
  return shuffled;
}

/**
 * 从牌组中发牌
 */
export function dealCards(deck: Card[], count: number): { cards: Card[], remainingDeck: Card[] } {
  if (deck.length < count) {
    throw new Error('牌组中的牌不足');
  }
  
  const cards = deck.slice(0, count);
  const remainingDeck = deck.slice(count);
  
  return { cards, remainingDeck };
}

/**
 * 计算牌的点数和
 */
export function calculateCardSum(cards: Card[]): number {
  return cards.reduce((sum, card) => sum + card.value, 0);
}

/**
 * 获取牌的显示名称
 */
export function getCardDisplayName(card: Card): string {
  const suitNames = {
    [Suit.HEARTS]: '♥',
    [Suit.DIAMONDS]: '♦',
    [Suit.CLUBS]: '♣',
    [Suit.SPADES]: '♠'
  };
  
  return `${suitNames[card.suit]}${card.rank}`;
}

/**
 * 格式化货币显示
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2
  }).format(amount);
}

/**
 * 格式化时间显示
 */
export function formatDateTime(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date);
}

/**
 * 验证用户名格式
 */
export function validateUsername(username: string): boolean {
  // 用户名长度3-20位，只能包含字母、数字、下划线
  const regex = /^[a-zA-Z0-9_]{3,20}$/;
  return regex.test(username);
}

/**
 * 验证邮箱格式
 */
export function validateEmail(email: string): boolean {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

/**
 * 验证手机号格式
 */
export function validatePhone(phone: string): boolean {
  // 中国大陆手机号格式
  const regex = /^1[3-9]\d{9}$/;
  return regex.test(phone);
}

/**
 * 生成随机数 (包含min和max)
 */
export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 延迟执行
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 深拷贝对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * 检查对象是否为空
 */
export function isEmpty(obj: any): boolean {
  if (obj === null || obj === undefined) return true;
  if (typeof obj === 'string') return obj.length === 0;
  if (Array.isArray(obj)) return obj.length === 0;
  if (typeof obj === 'object') return Object.keys(obj).length === 0;
  return false;
}

/**
 * 安全的JSON解析
 */
export function safeJsonParse<T>(str: string, defaultValue: T): T {
  try {
    return JSON.parse(str);
  } catch {
    return defaultValue;
  }
}

/**
 * 计算分页信息
 */
export function calculatePagination(total: number, page: number, limit: number) {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;
  
  return {
    total,
    page,
    limit,
    totalPages,
    hasNext,
    hasPrev,
    offset: (page - 1) * limit
  };
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * 检查是否为有效的下注金额
 */
export function isValidBetAmount(amount: number, minBet: number, maxBet: number, balance: number): boolean {
  return amount >= minBet && amount <= maxBet && amount <= balance && amount > 0;
}

/**
 * 计算返点金额
 */
export function calculateCommission(amount: number, rate: number): number {
  return Math.floor(amount * rate * 100) / 100; // 保留两位小数
}
