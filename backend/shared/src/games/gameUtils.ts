import { GameType, GameSeat } from '../types';
import { SangongGame } from './sangong';
import { NiuniuGame } from './niuniu';

/**
 * 游戏工厂类
 */
export class GameFactory {
  /**
   * 根据游戏类型发牌
   */
  static dealCards(gameType: GameType, playerCount: number) {
    switch (gameType) {
      case GameType.SANGONG:
        return SangongGame.dealCards(playerCount);
      case GameType.NIUNIU:
        return NiuniuGame.dealCards(playerCount);
      default:
        throw new Error(`Unsupported game type: ${gameType}`);
    }
  }
  
  /**
   * 根据游戏类型计算结果
   */
  static calculateGameResult(gameType: GameType, players: any[]) {
    switch (gameType) {
      case GameType.SANGONG:
        return SangongGame.calculateGameResult(players);
      case GameType.NIUNIU:
        return NiuniuGame.calculateGameResult(players);
      default:
        throw new Error(`Unsupported game type: ${gameType}`);
    }
  }
  
  /**
   * 检查游戏是否可以开始
   */
  static canStartGame(gameType: GameType, players: any[]): boolean {
    switch (gameType) {
      case GameType.SANGONG:
        return SangongGame.canStartGame(players);
      case GameType.NIUNIU:
        return NiuniuGame.canStartGame(players);
      default:
        return false;
    }
  }
}

/**
 * 游戏状态管理工具
 */
export class GameStateManager {
  /**
   * 获取房间内的活跃玩家
   */
  static getActivePlayers(seats: GameSeat[]): GameSeat[] {
    return seats.filter(seat => seat.userId && seat.isReady && seat.bet > 0);
  }
  
  /**
   * 获取房间内的所有坐下的玩家
   */
  static getSeatedPlayers(seats: GameSeat[]): GameSeat[] {
    return seats.filter(seat => seat.userId);
  }
  
  /**
   * 检查是否有足够的玩家开始游戏
   */
  static hasEnoughPlayers(seats: GameSeat[], minPlayers: number = 2): boolean {
    const activePlayers = this.getActivePlayers(seats);
    return activePlayers.length >= minPlayers;
  }
  
  /**
   * 重置座位状态（游戏结束后）
   */
  static resetSeatsForNewRound(seats: GameSeat[]): GameSeat[] {
    return seats.map(seat => ({
      ...seat,
      bet: 0,
      cards: [],
      isReady: false,
      cardsShown: false,
      sideWagers: []
    }));
  }
  
  /**
   * 更新玩家余额
   */
  static updatePlayerBalances(seats: GameSeat[], gameResult: any): GameSeat[] {
    return seats.map(seat => {
      if (!seat.userId) return seat;
      
      const playerResult = gameResult.players.find((p: any) => p.userId === seat.userId);
      if (playerResult) {
        return {
          ...seat,
          balance: seat.balance + playerResult.winAmount
        };
      }
      
      return seat;
    });
  }
}

/**
 * 游戏计时器管理
 */
export class GameTimer {
  private timers: Map<string, NodeJS.Timeout> = new Map();
  
  /**
   * 设置计时器
   */
  setTimer(key: string, callback: () => void, delay: number): void {
    this.clearTimer(key);
    const timer = setTimeout(callback, delay);
    this.timers.set(key, timer);
  }
  
  /**
   * 清除计时器
   */
  clearTimer(key: string): void {
    const timer = this.timers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(key);
    }
  }
  
  /**
   * 清除所有计时器
   */
  clearAllTimers(): void {
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
  }
  
  /**
   * 检查计时器是否存在
   */
  hasTimer(key: string): boolean {
    return this.timers.has(key);
  }
}

/**
 * 游戏事件类型
 */
export enum GameEvent {
  GAME_START = 'game_start',
  BETTING_START = 'betting_start',
  BETTING_END = 'betting_end',
  DEALING_START = 'dealing_start',
  DEALING_END = 'dealing_end',
  GAME_END = 'game_end',
  ROUND_END = 'round_end'
}

/**
 * 游戏事件数据
 */
export interface GameEventData {
  gameType: GameType;
  roomId: string;
  event: GameEvent;
  data?: any;
  timestamp: Date;
}

/**
 * 游戏验证工具
 */
export class GameValidator {
  /**
   * 验证下注金额
   */
  static validateBetAmount(amount: number, minBet: number, maxBet: number, balance: number): boolean {
    return amount >= minBet && amount <= maxBet && amount <= balance && amount > 0;
  }
  
  /**
   * 验证座位号
   */
  static validateSeatNumber(seatNumber: number, maxSeats: number = 10): boolean {
    return seatNumber >= 1 && seatNumber <= maxSeats;
  }
  
  /**
   * 验证游戏状态转换
   */
  static canTransitionTo(currentStatus: string, targetStatus: string): boolean {
    const validTransitions: { [key: string]: string[] } = {
      'waiting': ['betting'],
      'betting': ['dealing', 'waiting'],
      'dealing': ['playing'],
      'playing': ['settling'],
      'settling': ['waiting', 'finished'],
      'finished': ['waiting']
    };
    
    return validTransitions[currentStatus]?.includes(targetStatus) || false;
  }
}
