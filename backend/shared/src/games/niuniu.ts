import { Card, Rank } from '../types';
import { NIUNIU_CONFIG, CARD_CONFIG } from '../constants';
import { createDeck, shuffleDeck, dealCards } from '../utils';

// 牛牛牌型枚举
export enum NiuniuHandType {
  NO_NIU = 'no_niu',           // 无牛
  NIU_1 = 'niu_1',             // 牛1
  NIU_2 = 'niu_2',             // 牛2
  NIU_3 = 'niu_3',             // 牛3
  NIU_4 = 'niu_4',             // 牛4
  NIU_5 = 'niu_5',             // 牛5
  NIU_6 = 'niu_6',             // 牛6
  NIU_7 = 'niu_7',             // 牛7
  NIU_8 = 'niu_8',             // 牛8
  NIU_9 = 'niu_9',             // 牛9
  NIU_NIU = 'niu_niu',         // 牛牛
  WU_HUA_NIU = 'wu_hua_niu',   // 五花牛
  SI_ZHA = 'si_zha',           // 四炸
  WU_XIAO_NIU = 'wu_xiao_niu'  // 五小牛
}

// 牛牛手牌结果
export interface NiuniuHand {
  cards: Card[];
  handType: NiuniuHandType;
  points: number;
  rank: number;
  multiplier: number;
  combination?: Card[]; // 组成牛的三张牌
  description: string;
}

// 牛牛游戏结果
export interface NiuniuGameResult {
  players: {
    userId: string;
    hand: NiuniuHand;
    bet: number;
    winAmount: number;
    isWinner: boolean;
  }[];
  winners: string[];
  totalPot: number;
}

export class NiuniuGame {
  /**
   * 判断牌型
   */
  static evaluateHand(cards: Card[]): NiuniuHand {
    if (cards.length !== NIUNIU_CONFIG.CARDS_PER_PLAYER) {
      throw new Error('牛牛游戏需要5张牌');
    }
    
    // 检查特殊牌型
    const specialHand = this.checkSpecialHands(cards);
    if (specialHand) {
      return specialHand;
    }
    
    // 检查普通牛牛牌型
    return this.checkNormalHands(cards);
  }
  
  /**
   * 检查特殊牌型
   */
  private static checkSpecialHands(cards: Card[]): NiuniuHand | null {
    // 五小牛：五张牌相加点数小于等于10
    const totalPoints = cards.reduce((sum, card) => sum + card.value, 0);
    if (totalPoints <= 10) {
      return {
        cards,
        handType: NiuniuHandType.WU_XIAO_NIU,
        points: 0,
        rank: NIUNIU_CONFIG.HAND_RANKS.WU_XIAO_NIU * 1000,
        multiplier: NIUNIU_CONFIG.PAYOUT_MULTIPLIERS.WU_XIAO_NIU,
        description: `五小牛 (${totalPoints}点)`
      };
    }
    
    // 四炸：四张牌点数相同
    const valueGroups = this.groupByValue(cards);
    const hasQuads = Object.values(valueGroups).some(group => group.length === 4);
    if (hasQuads) {
      return {
        cards,
        handType: NiuniuHandType.SI_ZHA,
        points: 0,
        rank: NIUNIU_CONFIG.HAND_RANKS.SI_ZHA * 1000,
        multiplier: NIUNIU_CONFIG.PAYOUT_MULTIPLIERS.SI_ZHA,
        description: '四炸'
      };
    }
    
    // 五花牛：五张牌均为J、Q、K
    const isAllHonors = cards.every(card => 
      [Rank.JACK, Rank.QUEEN, Rank.KING].includes(card.rank)
    );
    if (isAllHonors) {
      return {
        cards,
        handType: NiuniuHandType.WU_HUA_NIU,
        points: 0,
        rank: NIUNIU_CONFIG.HAND_RANKS.WU_HUA_NIU * 1000,
        multiplier: NIUNIU_CONFIG.PAYOUT_MULTIPLIERS.WU_HUA_NIU,
        description: '五花牛'
      };
    }
    
    return null;
  }
  
  /**
   * 检查普通牛牛牌型
   */
  private static checkNormalHands(cards: Card[]): NiuniuHand {
    // 尝试找到能组成10的倍数的三张牌组合
    const combinations = this.getThreeCardCombinations(cards);
    
    for (const combination of combinations) {
      const combinationSum = combination.reduce((sum, card) => sum + card.value, 0);
      
      if (combinationSum % 10 === 0) {
        // 找到了牛，计算剩余两张牌的点数
        const remainingCards = cards.filter(card => !combination.includes(card));
        const remainingSum = remainingCards.reduce((sum, card) => sum + card.value, 0);
        const points = remainingSum % 10;
        
        let handType: NiuniuHandType;
        let rank: number;
        
        if (points === 0) {
          handType = NiuniuHandType.NIU_NIU;
          rank = NIUNIU_CONFIG.HAND_RANKS.NIU_NIU * 1000;
        } else {
          handType = `niu_${points}` as NiuniuHandType;
          rank = NIUNIU_CONFIG.HAND_RANKS[`NIU_${points}` as keyof typeof NIUNIU_CONFIG.HAND_RANKS] * 1000;
        }
        
        return {
          cards,
          handType,
          points,
          rank: rank + this.getMaxCardValue(cards),
          multiplier: NIUNIU_CONFIG.PAYOUT_MULTIPLIERS[handType.toUpperCase() as keyof typeof NIUNIU_CONFIG.PAYOUT_MULTIPLIERS] || 1,
          combination,
          description: points === 0 ? '牛牛' : `牛${points}`
        };
      }
    }
    
    // 无牛
    return {
      cards,
      handType: NiuniuHandType.NO_NIU,
      points: 0,
      rank: NIUNIU_CONFIG.HAND_RANKS.NO_NIU * 1000 + this.getMaxCardValue(cards),
      multiplier: NIUNIU_CONFIG.PAYOUT_MULTIPLIERS.NO_NIU,
      description: '无牛'
    };
  }
  
  /**
   * 获取三张牌的所有组合
   */
  private static getThreeCardCombinations(cards: Card[]): Card[][] {
    const combinations: Card[][] = [];
    
    for (let i = 0; i < cards.length - 2; i++) {
      for (let j = i + 1; j < cards.length - 1; j++) {
        for (let k = j + 1; k < cards.length; k++) {
          combinations.push([cards[i], cards[j], cards[k]]);
        }
      }
    }
    
    return combinations;
  }
  
  /**
   * 按牌值分组
   */
  private static groupByValue(cards: Card[]): { [value: number]: Card[] } {
    const groups: { [value: number]: Card[] } = {};
    
    cards.forEach(card => {
      if (!groups[card.value]) {
        groups[card.value] = [];
      }
      groups[card.value].push(card);
    });
    
    return groups;
  }
  
  /**
   * 获取最大牌值（用于比较同点数的牌）
   */
  private static getMaxCardValue(cards: Card[]): number {
    return Math.max(...cards.map(card => card.value));
  }
  
  /**
   * 比较两手牌的大小
   */
  static compareHands(hand1: NiuniuHand, hand2: NiuniuHand): number {
    return hand2.rank - hand1.rank; // 返回正数表示hand2更大
  }
  
  /**
   * 发牌
   */
  static dealCards(playerCount: number): { playerCards: Card[][], remainingDeck: Card[] } {
    if (playerCount < 2 || playerCount > 10) {
      throw new Error('玩家数量必须在2-10之间');
    }
    
    const deck = shuffleDeck(createDeck());
    const playerCards: Card[][] = [];
    let currentDeck = deck;
    
    // 给每个玩家发5张牌
    for (let i = 0; i < playerCount; i++) {
      const { cards, remainingDeck } = dealCards(currentDeck, NIUNIU_CONFIG.CARDS_PER_PLAYER);
      playerCards.push(cards);
      currentDeck = remainingDeck;
    }
    
    return {
      playerCards,
      remainingDeck: currentDeck
    };
  }
  
  /**
   * 计算游戏结果
   */
  static calculateGameResult(
    players: Array<{
      userId: string;
      cards: Card[];
      bet: number;
      sideWagers?: Array<{ userId: string; amount: number }>;
    }>
  ): NiuniuGameResult {
    // 评估每个玩家的手牌
    const evaluatedPlayers = players.map(player => ({
      ...player,
      hand: this.evaluateHand(player.cards)
    }));
    
    // 排序找出获胜者
    evaluatedPlayers.sort((a, b) => this.compareHands(a.hand, b.hand));
    
    // 计算总奖池
    const totalPot = players.reduce((sum, player) => {
      let playerTotal = player.bet;
      if (player.sideWagers) {
        playerTotal += player.sideWagers.reduce((wagerSum, wager) => wagerSum + wager.amount, 0);
      }
      return sum + playerTotal;
    }, 0);
    
    // 大吃小机制：最大的牌型获胜，获得所有下注
    const winner = evaluatedPlayers[0];
    const winners = [winner.userId];
    
    // 计算每个玩家的输赢（考虑倍数）
    const result: NiuniuGameResult = {
      players: evaluatedPlayers.map(player => {
        const isWinner = player.userId === winner.userId;
        let winAmount = 0;
        
        if (isWinner) {
          // 获胜者获得除自己下注外的所有金额，乘以自己的倍数
          const othersBets = totalPot - player.bet;
          if (player.sideWagers) {
            const sideWagerTotal = player.sideWagers.reduce((sum, wager) => sum + wager.amount, 0);
            winAmount = (othersBets - sideWagerTotal) * player.hand.multiplier;
          } else {
            winAmount = othersBets * player.hand.multiplier;
          }
        } else {
          // 失败者失去所有下注，乘以获胜者的倍数
          winAmount = -(player.bet * winner.hand.multiplier);
          if (player.sideWagers) {
            winAmount -= player.sideWagers.reduce((sum, wager) => sum + wager.amount * winner.hand.multiplier, 0);
          }
        }
        
        return {
          userId: player.userId,
          hand: player.hand,
          bet: player.bet,
          winAmount,
          isWinner
        };
      }),
      winners,
      totalPot
    };
    
    return result;
  }
  
  /**
   * 获取牌型描述
   */
  static getHandTypeDescription(handType: NiuniuHandType): string {
    const descriptions = {
      [NiuniuHandType.WU_XIAO_NIU]: '五小牛',
      [NiuniuHandType.SI_ZHA]: '四炸',
      [NiuniuHandType.WU_HUA_NIU]: '五花牛',
      [NiuniuHandType.NIU_NIU]: '牛牛',
      [NiuniuHandType.NIU_9]: '牛九',
      [NiuniuHandType.NIU_8]: '牛八',
      [NiuniuHandType.NIU_7]: '牛七',
      [NiuniuHandType.NIU_6]: '牛六',
      [NiuniuHandType.NIU_5]: '牛五',
      [NiuniuHandType.NIU_4]: '牛四',
      [NiuniuHandType.NIU_3]: '牛三',
      [NiuniuHandType.NIU_2]: '牛二',
      [NiuniuHandType.NIU_1]: '牛一',
      [NiuniuHandType.NO_NIU]: '无牛'
    };
    
    return descriptions[handType];
  }
  
  /**
   * 验证游戏是否可以开始
   */
  static canStartGame(players: Array<{ userId: string; isReady: boolean; bet: number }>): boolean {
    // 至少需要2个玩家
    if (players.length < 2) {
      return false;
    }
    
    // 所有玩家都必须准备并下注
    return players.every(player => player.isReady && player.bet > 0);
  }
}
