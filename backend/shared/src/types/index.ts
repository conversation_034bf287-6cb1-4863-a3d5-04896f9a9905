// 扑克牌相关类型
export enum Suit {
  HEARTS = 'hearts',    // 红桃
  DIAMONDS = 'diamonds', // 方块
  CLUBS = 'clubs',      // 梅花
  SPADES = 'spades'     // 黑桃
}

export enum Rank {
  ACE = 'A',
  TWO = '2',
  THREE = '3',
  FOUR = '4',
  FIVE = '5',
  SIX = '6',
  SEVEN = '7',
  EIGHT = '8',
  NINE = '9',
  TEN = '10',
  JACK = 'J',
  QUEEN = 'Q',
  KING = 'K'
}

export interface Card {
  suit: Suit;
  rank: Rank;
  value: number; // 牌的点数值
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  balance: number;
  agentId?: string;
  clubId?: string;
  status: UserStatus;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned'
}

// 代理相关类型
export interface Agent {
  id: string;
  username: string;
  email: string;
  balance: number;
  commission: number; // 返点比例
  status: AgentStatus;
  createdAt: Date;
  updatedAt: Date;
}

export enum AgentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned'
}

// 俱乐部相关类型
export interface Club {
  id: string;
  name: string;
  agentId: string;
  description?: string;
  maxMembers: number;
  currentMembers: number;
  status: ClubStatus;
  createdAt: Date;
  updatedAt: Date;
}

export enum ClubStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

// 游戏相关类型
export enum GameType {
  SANGONG = 'sangong',  // 三公
  NIUNIU = 'niuniu'     // 牛牛
}

export enum GameStatus {
  WAITING = 'waiting',     // 等待玩家
  BETTING = 'betting',     // 下注阶段
  DEALING = 'dealing',     // 发牌阶段
  PLAYING = 'playing',     // 游戏进行中
  SETTLING = 'settling',   // 结算阶段
  FINISHED = 'finished'    // 游戏结束
}

export interface GameRoom {
  id: string;
  gameType: GameType;
  status: GameStatus;
  seats: GameSeat[];
  spectators: string[]; // 旁观者用户ID列表
  currentRound: number;
  minBet: number;
  maxBet: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface GameSeat {
  position: number; // 1-10
  userId?: string;
  username?: string;
  balance: number;
  bet: number;
  cards: Card[];
  isReady: boolean;
  cardsShown?: boolean; // 是否已开牌
  sideWagers: SideWager[]; // 旁注
}

export interface SideWager {
  userId: string;
  username: string;
  amount: number;
  targetSeat: number; // 押注的座位号
}

// 游戏记录类型
export interface GameRecord {
  id: string;
  roomId: string;
  gameType: GameType;
  players: GamePlayer[];
  result: GameResult;
  totalPot: number;
  createdAt: Date;
}

export interface GamePlayer {
  userId: string;
  username: string;
  seat: number;
  cards: Card[];
  bet: number;
  winAmount: number;
  sideWagers: SideWager[];
}

export interface GameResult {
  winners: string[]; // 获胜者用户ID列表
  payouts: { [userId: string]: number }; // 每个玩家的盈亏
}

// WebSocket消息类型
export enum MessageType {
  // 连接相关
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  
  // 房间相关
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_UPDATE = 'room_update',
  
  // 游戏相关
  PLACE_BET = 'place_bet',
  READY = 'ready',
  DEAL_CARDS = 'deal_cards',
  GAME_START = 'game_start',
  GAME_END = 'game_end',
  
  // 旁注相关
  PLACE_SIDE_WAGER = 'place_side_wager',
  
  // 聊天相关
  CHAT_MESSAGE = 'chat_message',
  
  // 错误相关
  ERROR = 'error'
}

export interface WebSocketMessage {
  type: MessageType;
  data: any;
  timestamp: Date;
  userId?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页类型
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
