"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateId = generateId;
exports.createDeck = createDeck;
exports.shuffleDeck = shuffleDeck;
exports.dealCards = dealCards;
exports.calculateCardSum = calculateCardSum;
exports.getCardDisplayName = getCardDisplayName;
exports.formatCurrency = formatCurrency;
exports.formatDateTime = formatDateTime;
exports.validateUsername = validateUsername;
exports.validateEmail = validateEmail;
exports.validatePhone = validatePhone;
exports.randomInt = randomInt;
exports.delay = delay;
exports.deepClone = deepClone;
exports.isEmpty = isEmpty;
exports.safeJsonParse = safeJsonParse;
exports.calculatePagination = calculatePagination;
exports.generateRandomString = generateRandomString;
exports.isValidBetAmount = isValidBetAmount;
exports.calculateCommission = calculateCommission;
const types_1 = require("../types");
const constants_1 = require("../constants");
/**
 * 生成UUID
 */
function generateId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}
/**
 * 创建一副完整的扑克牌
 */
function createDeck() {
    const deck = [];
    for (const suit of Object.values(types_1.Suit)) {
        for (const rank of Object.values(types_1.Rank)) {
            deck.push({
                suit,
                rank,
                value: constants_1.CARD_CONFIG.RANK_VALUES[rank]
            });
        }
    }
    return deck;
}
/**
 * 洗牌算法 (Fisher-Yates)
 */
function shuffleDeck(deck) {
    const shuffled = [...deck];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}
/**
 * 从牌组中发牌
 */
function dealCards(deck, count) {
    if (deck.length < count) {
        throw new Error('牌组中的牌不足');
    }
    const cards = deck.slice(0, count);
    const remainingDeck = deck.slice(count);
    return { cards, remainingDeck };
}
/**
 * 计算牌的点数和
 */
function calculateCardSum(cards) {
    return cards.reduce((sum, card) => sum + card.value, 0);
}
/**
 * 获取牌的显示名称
 */
function getCardDisplayName(card) {
    const suitNames = {
        [types_1.Suit.HEARTS]: '♥',
        [types_1.Suit.DIAMONDS]: '♦',
        [types_1.Suit.CLUBS]: '♣',
        [types_1.Suit.SPADES]: '♠'
    };
    return `${suitNames[card.suit]}${card.rank}`;
}
/**
 * 格式化货币显示
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2
    }).format(amount);
}
/**
 * 格式化时间显示
 */
function formatDateTime(date) {
    return new Intl.DateTimeFormat('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).format(date);
}
/**
 * 验证用户名格式
 */
function validateUsername(username) {
    // 用户名长度3-20位，只能包含字母、数字、下划线
    const regex = /^[a-zA-Z0-9_]{3,20}$/;
    return regex.test(username);
}
/**
 * 验证邮箱格式
 */
function validateEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}
/**
 * 验证手机号格式
 */
function validatePhone(phone) {
    // 中国大陆手机号格式
    const regex = /^1[3-9]\d{9}$/;
    return regex.test(phone);
}
/**
 * 生成随机数 (包含min和max)
 */
function randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
/**
 * 延迟执行
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * 深拷贝对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    if (typeof obj === 'object') {
        const cloned = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key]);
            }
        }
        return cloned;
    }
    return obj;
}
/**
 * 检查对象是否为空
 */
function isEmpty(obj) {
    if (obj === null || obj === undefined)
        return true;
    if (typeof obj === 'string')
        return obj.length === 0;
    if (Array.isArray(obj))
        return obj.length === 0;
    if (typeof obj === 'object')
        return Object.keys(obj).length === 0;
    return false;
}
/**
 * 安全的JSON解析
 */
function safeJsonParse(str, defaultValue) {
    try {
        return JSON.parse(str);
    }
    catch {
        return defaultValue;
    }
}
/**
 * 计算分页信息
 */
function calculatePagination(total, page, limit) {
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;
    return {
        total,
        page,
        limit,
        totalPages,
        hasNext,
        hasPrev,
        offset: (page - 1) * limit
    };
}
/**
 * 生成随机字符串
 */
function generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
/**
 * 检查是否为有效的下注金额
 */
function isValidBetAmount(amount, minBet, maxBet, balance) {
    return amount >= minBet && amount <= maxBet && amount <= balance && amount > 0;
}
/**
 * 计算返点金额
 */
function calculateCommission(amount, rate) {
    return Math.floor(amount * rate * 100) / 100; // 保留两位小数
}
//# sourceMappingURL=index.js.map