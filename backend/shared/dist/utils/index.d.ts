import { Card } from '../types';
/**
 * 生成UUID
 */
export declare function generateId(): string;
/**
 * 创建一副完整的扑克牌
 */
export declare function createDeck(): Card[];
/**
 * 洗牌算法 (<PERSON>-Yates)
 */
export declare function shuffleDeck(deck: Card[]): Card[];
/**
 * 从牌组中发牌
 */
export declare function dealCards(deck: Card[], count: number): {
    cards: Card[];
    remainingDeck: Card[];
};
/**
 * 计算牌的点数和
 */
export declare function calculateCardSum(cards: Card[]): number;
/**
 * 获取牌的显示名称
 */
export declare function getCardDisplayName(card: Card): string;
/**
 * 格式化货币显示
 */
export declare function formatCurrency(amount: number): string;
/**
 * 格式化时间显示
 */
export declare function formatDateTime(date: Date): string;
/**
 * 验证用户名格式
 */
export declare function validateUsername(username: string): boolean;
/**
 * 验证邮箱格式
 */
export declare function validateEmail(email: string): boolean;
/**
 * 验证手机号格式
 */
export declare function validatePhone(phone: string): boolean;
/**
 * 生成随机数 (包含min和max)
 */
export declare function randomInt(min: number, max: number): number;
/**
 * 延迟执行
 */
export declare function delay(ms: number): Promise<void>;
/**
 * 深拷贝对象
 */
export declare function deepClone<T>(obj: T): T;
/**
 * 检查对象是否为空
 */
export declare function isEmpty(obj: any): boolean;
/**
 * 安全的JSON解析
 */
export declare function safeJsonParse<T>(str: string, defaultValue: T): T;
/**
 * 计算分页信息
 */
export declare function calculatePagination(total: number, page: number, limit: number): {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    offset: number;
};
/**
 * 生成随机字符串
 */
export declare function generateRandomString(length: number): string;
/**
 * 检查是否为有效的下注金额
 */
export declare function isValidBetAmount(amount: number, minBet: number, maxBet: number, balance: number): boolean;
/**
 * 计算返点金额
 */
export declare function calculateCommission(amount: number, rate: number): number;
//# sourceMappingURL=index.d.ts.map