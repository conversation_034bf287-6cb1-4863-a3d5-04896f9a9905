"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NiuniuGame = exports.NiuniuHandType = void 0;
const types_1 = require("../types");
const constants_1 = require("../constants");
const utils_1 = require("../utils");
// 牛牛牌型枚举
var NiuniuHandType;
(function (NiuniuHandType) {
    NiuniuHandType["NO_NIU"] = "no_niu";
    NiuniuHandType["NIU_1"] = "niu_1";
    NiuniuHandType["NIU_2"] = "niu_2";
    NiuniuHandType["NIU_3"] = "niu_3";
    NiuniuHandType["NIU_4"] = "niu_4";
    NiuniuHandType["NIU_5"] = "niu_5";
    NiuniuHandType["NIU_6"] = "niu_6";
    NiuniuHandType["NIU_7"] = "niu_7";
    NiuniuHandType["NIU_8"] = "niu_8";
    NiuniuHandType["NIU_9"] = "niu_9";
    NiuniuHandType["NIU_NIU"] = "niu_niu";
    NiuniuHandType["WU_HUA_NIU"] = "wu_hua_niu";
    NiuniuHandType["SI_ZHA"] = "si_zha";
    NiuniuHandType["WU_XIAO_NIU"] = "wu_xiao_niu"; // 五小牛
})(NiuniuHandType || (exports.NiuniuHandType = NiuniuHandType = {}));
class NiuniuGame {
    /**
     * 判断牌型
     */
    static evaluateHand(cards) {
        if (cards.length !== constants_1.NIUNIU_CONFIG.CARDS_PER_PLAYER) {
            throw new Error('牛牛游戏需要5张牌');
        }
        // 检查特殊牌型
        const specialHand = this.checkSpecialHands(cards);
        if (specialHand) {
            return specialHand;
        }
        // 检查普通牛牛牌型
        return this.checkNormalHands(cards);
    }
    /**
     * 检查特殊牌型
     */
    static checkSpecialHands(cards) {
        // 五小牛：五张牌相加点数小于等于10
        const totalPoints = cards.reduce((sum, card) => sum + card.value, 0);
        if (totalPoints <= 10) {
            return {
                cards,
                handType: NiuniuHandType.WU_XIAO_NIU,
                points: 0,
                rank: constants_1.NIUNIU_CONFIG.HAND_RANKS.WU_XIAO_NIU * 1000,
                multiplier: constants_1.NIUNIU_CONFIG.PAYOUT_MULTIPLIERS.WU_XIAO_NIU,
                description: `五小牛 (${totalPoints}点)`
            };
        }
        // 四炸：四张牌点数相同
        const valueGroups = this.groupByValue(cards);
        const hasQuads = Object.values(valueGroups).some(group => group.length === 4);
        if (hasQuads) {
            return {
                cards,
                handType: NiuniuHandType.SI_ZHA,
                points: 0,
                rank: constants_1.NIUNIU_CONFIG.HAND_RANKS.SI_ZHA * 1000,
                multiplier: constants_1.NIUNIU_CONFIG.PAYOUT_MULTIPLIERS.SI_ZHA,
                description: '四炸'
            };
        }
        // 五花牛：五张牌均为J、Q、K
        const isAllHonors = cards.every(card => [types_1.Rank.JACK, types_1.Rank.QUEEN, types_1.Rank.KING].includes(card.rank));
        if (isAllHonors) {
            return {
                cards,
                handType: NiuniuHandType.WU_HUA_NIU,
                points: 0,
                rank: constants_1.NIUNIU_CONFIG.HAND_RANKS.WU_HUA_NIU * 1000,
                multiplier: constants_1.NIUNIU_CONFIG.PAYOUT_MULTIPLIERS.WU_HUA_NIU,
                description: '五花牛'
            };
        }
        return null;
    }
    /**
     * 检查普通牛牛牌型
     */
    static checkNormalHands(cards) {
        // 尝试找到能组成10的倍数的三张牌组合
        const combinations = this.getThreeCardCombinations(cards);
        for (const combination of combinations) {
            const combinationSum = combination.reduce((sum, card) => sum + card.value, 0);
            if (combinationSum % 10 === 0) {
                // 找到了牛，计算剩余两张牌的点数
                const remainingCards = cards.filter(card => !combination.includes(card));
                const remainingSum = remainingCards.reduce((sum, card) => sum + card.value, 0);
                const points = remainingSum % 10;
                let handType;
                let rank;
                if (points === 0) {
                    handType = NiuniuHandType.NIU_NIU;
                    rank = constants_1.NIUNIU_CONFIG.HAND_RANKS.NIU_NIU * 1000;
                }
                else {
                    handType = `niu_${points}`;
                    rank = constants_1.NIUNIU_CONFIG.HAND_RANKS[`NIU_${points}`] * 1000;
                }
                return {
                    cards,
                    handType,
                    points,
                    rank: rank + this.getMaxCardValue(cards),
                    multiplier: constants_1.NIUNIU_CONFIG.PAYOUT_MULTIPLIERS[handType.toUpperCase()] || 1,
                    combination,
                    description: points === 0 ? '牛牛' : `牛${points}`
                };
            }
        }
        // 无牛
        return {
            cards,
            handType: NiuniuHandType.NO_NIU,
            points: 0,
            rank: constants_1.NIUNIU_CONFIG.HAND_RANKS.NO_NIU * 1000 + this.getMaxCardValue(cards),
            multiplier: constants_1.NIUNIU_CONFIG.PAYOUT_MULTIPLIERS.NO_NIU,
            description: '无牛'
        };
    }
    /**
     * 获取三张牌的所有组合
     */
    static getThreeCardCombinations(cards) {
        const combinations = [];
        for (let i = 0; i < cards.length - 2; i++) {
            for (let j = i + 1; j < cards.length - 1; j++) {
                for (let k = j + 1; k < cards.length; k++) {
                    combinations.push([cards[i], cards[j], cards[k]]);
                }
            }
        }
        return combinations;
    }
    /**
     * 按牌值分组
     */
    static groupByValue(cards) {
        const groups = {};
        cards.forEach(card => {
            if (!groups[card.value]) {
                groups[card.value] = [];
            }
            groups[card.value].push(card);
        });
        return groups;
    }
    /**
     * 获取最大牌值（用于比较同点数的牌）
     */
    static getMaxCardValue(cards) {
        return Math.max(...cards.map(card => card.value));
    }
    /**
     * 比较两手牌的大小
     */
    static compareHands(hand1, hand2) {
        return hand2.rank - hand1.rank; // 返回正数表示hand2更大
    }
    /**
     * 发牌
     */
    static dealCards(playerCount) {
        if (playerCount < 2 || playerCount > 10) {
            throw new Error('玩家数量必须在2-10之间');
        }
        const deck = (0, utils_1.shuffleDeck)((0, utils_1.createDeck)());
        const playerCards = [];
        let currentDeck = deck;
        // 给每个玩家发5张牌
        for (let i = 0; i < playerCount; i++) {
            const { cards, remainingDeck } = (0, utils_1.dealCards)(currentDeck, constants_1.NIUNIU_CONFIG.CARDS_PER_PLAYER);
            playerCards.push(cards);
            currentDeck = remainingDeck;
        }
        return {
            playerCards,
            remainingDeck: currentDeck
        };
    }
    /**
     * 计算游戏结果
     */
    static calculateGameResult(players) {
        // 评估每个玩家的手牌
        const evaluatedPlayers = players.map(player => ({
            ...player,
            hand: this.evaluateHand(player.cards)
        }));
        // 排序找出获胜者
        evaluatedPlayers.sort((a, b) => this.compareHands(a.hand, b.hand));
        // 计算总奖池
        const totalPot = players.reduce((sum, player) => {
            let playerTotal = player.bet;
            if (player.sideWagers) {
                playerTotal += player.sideWagers.reduce((wagerSum, wager) => wagerSum + wager.amount, 0);
            }
            return sum + playerTotal;
        }, 0);
        // 大吃小机制：最大的牌型获胜，获得所有下注
        const winner = evaluatedPlayers[0];
        const winners = [winner.userId];
        // 计算每个玩家的输赢（考虑倍数）
        const result = {
            players: evaluatedPlayers.map(player => {
                const isWinner = player.userId === winner.userId;
                let winAmount = 0;
                if (isWinner) {
                    // 获胜者获得除自己下注外的所有金额，乘以自己的倍数
                    const othersBets = totalPot - player.bet;
                    if (player.sideWagers) {
                        const sideWagerTotal = player.sideWagers.reduce((sum, wager) => sum + wager.amount, 0);
                        winAmount = (othersBets - sideWagerTotal) * player.hand.multiplier;
                    }
                    else {
                        winAmount = othersBets * player.hand.multiplier;
                    }
                }
                else {
                    // 失败者失去所有下注，乘以获胜者的倍数
                    winAmount = -(player.bet * winner.hand.multiplier);
                    if (player.sideWagers) {
                        winAmount -= player.sideWagers.reduce((sum, wager) => sum + wager.amount * winner.hand.multiplier, 0);
                    }
                }
                return {
                    userId: player.userId,
                    hand: player.hand,
                    bet: player.bet,
                    winAmount,
                    isWinner
                };
            }),
            winners,
            totalPot
        };
        return result;
    }
    /**
     * 获取牌型描述
     */
    static getHandTypeDescription(handType) {
        const descriptions = {
            [NiuniuHandType.WU_XIAO_NIU]: '五小牛',
            [NiuniuHandType.SI_ZHA]: '四炸',
            [NiuniuHandType.WU_HUA_NIU]: '五花牛',
            [NiuniuHandType.NIU_NIU]: '牛牛',
            [NiuniuHandType.NIU_9]: '牛九',
            [NiuniuHandType.NIU_8]: '牛八',
            [NiuniuHandType.NIU_7]: '牛七',
            [NiuniuHandType.NIU_6]: '牛六',
            [NiuniuHandType.NIU_5]: '牛五',
            [NiuniuHandType.NIU_4]: '牛四',
            [NiuniuHandType.NIU_3]: '牛三',
            [NiuniuHandType.NIU_2]: '牛二',
            [NiuniuHandType.NIU_1]: '牛一',
            [NiuniuHandType.NO_NIU]: '无牛'
        };
        return descriptions[handType];
    }
    /**
     * 验证游戏是否可以开始
     */
    static canStartGame(players) {
        // 至少需要2个玩家
        if (players.length < 2) {
            return false;
        }
        // 所有玩家都必须准备并下注
        return players.every(player => player.isReady && player.bet > 0);
    }
}
exports.NiuniuGame = NiuniuGame;
//# sourceMappingURL=niuniu.js.map