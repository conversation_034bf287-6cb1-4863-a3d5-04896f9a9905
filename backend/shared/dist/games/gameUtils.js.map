{"version": 3, "file": "gameUtils.js", "sourceRoot": "", "sources": ["../../src/games/gameUtils.ts"], "names": [], "mappings": ";;;AAAA,oCAA8C;AAC9C,uCAAwC;AACxC,qCAAsC;AAEtC;;GAEG;AACH,MAAa,WAAW;IACtB;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,QAAkB,EAAE,WAAmB;QACtD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,gBAAQ,CAAC,OAAO;gBACnB,OAAO,qBAAW,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC5C,KAAK,gBAAQ,CAAC,MAAM;gBAClB,OAAO,mBAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC3C;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,QAAkB,EAAE,OAAc;QAC3D,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,gBAAQ,CAAC,OAAO;gBACnB,OAAO,qBAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAClD,KAAK,gBAAQ,CAAC,MAAM;gBAClB,OAAO,mBAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YACjD;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,QAAkB,EAAE,OAAc;QACpD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,gBAAQ,CAAC,OAAO;gBACnB,OAAO,qBAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC3C,KAAK,gBAAQ,CAAC,MAAM;gBAClB,OAAO,mBAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC1C;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;CACF;AA1CD,kCA0CC;AAED;;GAEG;AACH,MAAa,gBAAgB;IAC3B;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,KAAiB;QACvC,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,KAAiB;QACvC,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,KAAiB,EAAE,aAAqB,CAAC;QAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACnD,OAAO,aAAa,CAAC,MAAM,IAAI,UAAU,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,KAAiB;QAC5C,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,GAAG,IAAI;YACP,GAAG,EAAE,CAAC;YACN,KAAK,EAAE,EAAE;YACT,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,EAAE;SACf,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,KAAiB,EAAE,UAAe;QAC5D,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,MAAM;gBAAE,OAAO,IAAI,CAAC;YAE9B,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YACnF,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO;oBACL,GAAG,IAAI;oBACP,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,SAAS;iBAC/C,CAAC;YACJ,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAvDD,4CAuDC;AAED;;GAEG;AACH,MAAa,SAAS;IAAtB;QACU,WAAM,GAAgC,IAAI,GAAG,EAAE,CAAC;IAoC1D,CAAC;IAlCC;;OAEG;IACH,QAAQ,CAAC,GAAW,EAAE,QAAoB,EAAE,KAAa;QACvD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACrB,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,GAAW;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,GAAW;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;CACF;AArCD,8BAqCC;AAED;;GAEG;AACH,IAAY,SAQX;AARD,WAAY,SAAS;IACnB,sCAAyB,CAAA;IACzB,4CAA+B,CAAA;IAC/B,wCAA2B,CAAA;IAC3B,4CAA+B,CAAA;IAC/B,wCAA2B,CAAA;IAC3B,kCAAqB,CAAA;IACrB,oCAAuB,CAAA;AACzB,CAAC,EARW,SAAS,yBAAT,SAAS,QAQpB;AAaD;;GAEG;AACH,MAAa,aAAa;IACxB;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,MAAc,EAAE,MAAc,EAAE,MAAc,EAAE,OAAe;QACtF,OAAO,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,UAAkB,EAAE,WAAmB,EAAE;QACjE,OAAO,UAAU,IAAI,CAAC,IAAI,UAAU,IAAI,QAAQ,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,aAAqB,EAAE,YAAoB;QAChE,MAAM,gBAAgB,GAAgC;YACpD,SAAS,EAAE,CAAC,SAAS,CAAC;YACtB,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;YACjC,SAAS,EAAE,CAAC,SAAS,CAAC;YACtB,SAAS,EAAE,CAAC,UAAU,CAAC;YACvB,UAAU,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;YACnC,UAAU,EAAE,CAAC,SAAS,CAAC;SACxB,CAAC;QAEF,OAAO,gBAAgB,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC;IAC1E,CAAC;CACF;AA9BD,sCA8BC"}