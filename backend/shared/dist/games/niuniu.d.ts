import { Card } from '../types';
export declare enum NiuniuHandType {
    NO_NIU = "no_niu",// 无牛
    NIU_1 = "niu_1",// 牛1
    NIU_2 = "niu_2",// 牛2
    NIU_3 = "niu_3",// 牛3
    NIU_4 = "niu_4",// 牛4
    NIU_5 = "niu_5",// 牛5
    NIU_6 = "niu_6",// 牛6
    NIU_7 = "niu_7",// 牛7
    NIU_8 = "niu_8",// 牛8
    NIU_9 = "niu_9",// 牛9
    NIU_NIU = "niu_niu",// 牛牛
    WU_HUA_NIU = "wu_hua_niu",// 五花牛
    SI_ZHA = "si_zha",// 四炸
    WU_XIAO_NIU = "wu_xiao_niu"
}
export interface NiuniuHand {
    cards: Card[];
    handType: NiuniuHandType;
    points: number;
    rank: number;
    multiplier: number;
    combination?: Card[];
    description: string;
}
export interface NiuniuGameResult {
    players: {
        userId: string;
        hand: NiuniuHand;
        bet: number;
        winAmount: number;
        isWinner: boolean;
    }[];
    winners: string[];
    totalPot: number;
}
export declare class NiuniuGame {
    /**
     * 判断牌型
     */
    static evaluateHand(cards: Card[]): NiuniuHand;
    /**
     * 检查特殊牌型
     */
    private static checkSpecialHands;
    /**
     * 检查普通牛牛牌型
     */
    private static checkNormalHands;
    /**
     * 获取三张牌的所有组合
     */
    private static getThreeCardCombinations;
    /**
     * 按牌值分组
     */
    private static groupByValue;
    /**
     * 获取最大牌值（用于比较同点数的牌）
     */
    private static getMaxCardValue;
    /**
     * 比较两手牌的大小
     */
    static compareHands(hand1: NiuniuHand, hand2: NiuniuHand): number;
    /**
     * 发牌
     */
    static dealCards(playerCount: number): {
        playerCards: Card[][];
        remainingDeck: Card[];
    };
    /**
     * 计算游戏结果
     */
    static calculateGameResult(players: Array<{
        userId: string;
        cards: Card[];
        bet: number;
        sideWagers?: Array<{
            userId: string;
            amount: number;
        }>;
    }>): NiuniuGameResult;
    /**
     * 获取牌型描述
     */
    static getHandTypeDescription(handType: NiuniuHandType): string;
    /**
     * 验证游戏是否可以开始
     */
    static canStartGame(players: Array<{
        userId: string;
        isReady: boolean;
        bet: number;
    }>): boolean;
}
//# sourceMappingURL=niuniu.d.ts.map