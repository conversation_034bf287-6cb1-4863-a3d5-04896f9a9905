{"version": 3, "file": "sangong.js", "sourceRoot": "", "sources": ["../../src/games/sangong.ts"], "names": [], "mappings": ";;;AAAA,oCAA4C;AAC5C,4CAA2D;AAC3D,oCAA8D;AAE9D,SAAS;AACT,IAAY,eAKX;AALD,WAAY,eAAe;IACzB,4CAAyB,CAAA;IACzB,8CAA2B,CAAA;IAC3B,gDAA6B,CAAA;IAC7B,4CAAyB,CAAA,CAAO,MAAM;AACxC,CAAC,EALW,eAAe,+BAAf,eAAe,QAK1B;AAyBD,MAAa,WAAW;IACtB;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,KAAa;QAC/B,IAAI,KAAK,CAAC,MAAM,KAAK,0BAAc,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;QAC/B,CAAC;QAED,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACrC,uBAAW,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CACpD,CAAC;QACF,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;QAErC,kBAAkB;QAClB,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;QAED,qBAAqB;QACrB,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC;QAED,MAAM;QACN,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,KAAa;QAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,uBAAuB;QACvB,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,WAAW,GAAG,CAAC,YAAI,CAAC,IAAI,EAAE,YAAI,CAAC,KAAK,EAAE,YAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEtE,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO;oBACL,KAAK;oBACL,QAAQ,EAAE,eAAe,CAAC,UAAU;oBACpC,MAAM,EAAE,CAAC;oBACT,IAAI,EAAE,0BAAc,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,GAAI,0BAAc,CAAC,gBAAwB,CAAC,IAAI,CAAC;oBAClG,UAAU,EAAE,CAAC;oBACb,WAAW,EAAE,OAAO,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;iBACzC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,qCAAqC;gBACrC,OAAO;oBACL,KAAK;oBACL,QAAQ,EAAE,eAAe,CAAC,YAAY;oBACtC,MAAM,EAAE,CAAC;oBACT,IAAI,EAAE,0BAAc,CAAC,UAAU,CAAC,YAAY,GAAG,IAAI,GAAI,0BAAc,CAAC,kBAA0B,CAAC,IAAI,CAAC;oBACtG,UAAU,EAAE,CAAC;oBACb,WAAW,EAAE,OAAO,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;iBACzC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,eAAe;QACf,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACrC,0BAAc,CAAC,gBAAwB,CAAC,CAAC,CAAC,GAAI,0BAAc,CAAC,gBAAwB,CAAC,CAAC,CAAC,CAC1F,CAAC;QACF,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAE/B,OAAO;YACL,KAAK;YACL,QAAQ,EAAE,eAAe,CAAC,WAAW;YACrC,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,0BAAc,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,GAAI,0BAAc,CAAC,gBAAwB,CAAC,OAAO,CAAC;YACtG,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,KAAa,EAAE,UAAkB;QACjE,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC3C,MAAM,YAAY,GAAG,KAAK;aACvB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,uBAAW,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACnE,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CACnB,0BAAc,CAAC,gBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAI,0BAAc,CAAC,gBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CACtH,CAAC,IAAI,CAAC;QAET,OAAO;YACL,KAAK;YACL,QAAQ,EAAE,eAAe,CAAC,WAAW;YACrC,MAAM;YACN,IAAI,EAAE,0BAAc,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,GAAG,MAAM,GAAG,EAAE,GAAI,0BAAc,CAAC,gBAAwB,CAAC,YAAY,CAAC;YACzH,UAAU;YACV,WAAW,EAAE,OAAO,MAAM,GAAG;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,KAAa;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAEjF,OAAO;YACL,KAAK;YACL,QAAQ,EAAE,eAAe,CAAC,UAAU;YACpC,MAAM;YACN,IAAI,EAAE,0BAAc,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,GAAG,MAAM,GAAG,EAAE,GAAG,OAAO,CAAC,KAAK;YAC/E,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,GAAG,MAAM,GAAG;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAAC,KAAa;QAC1C,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjE,OAAO,GAAG,GAAG,EAAE,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,KAAkB,EAAE,KAAkB;QACxD,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,WAAmB;QAClC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,IAAI,GAAG,IAAA,mBAAW,EAAC,IAAA,kBAAU,GAAE,CAAC,CAAC;QACvC,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,WAAW,GAAG,IAAI,CAAC;QAEvB,YAAY;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,IAAA,iBAAS,EAAC,WAAW,EAAE,0BAAc,CAAC,gBAAgB,CAAC,CAAC;YACzF,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxB,WAAW,GAAG,aAAa,CAAC;QAC9B,CAAC;QAED,OAAO;YACL,WAAW;YACX,aAAa,EAAE,WAAW;SAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,OAKE;QAEF,YAAY;QACZ,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC9C,GAAG,MAAM;YACT,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC;SACtC,CAAC,CAAC,CAAC;QAEJ,UAAU;QACV,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAEnE,QAAQ;QACR,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YAC9C,IAAI,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC;YAC7B,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACtB,WAAW,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAC3F,CAAC;YACD,OAAO,GAAG,GAAG,WAAW,CAAC;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,uBAAuB;QACvB,MAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,OAAO,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEhC,YAAY;QACZ,MAAM,MAAM,GAAsB;YAChC,OAAO,EAAE,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACrC,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC;gBACjD,IAAI,SAAS,GAAG,CAAC,CAAC;gBAElB,IAAI,QAAQ,EAAE,CAAC;oBACb,mBAAmB;oBACnB,SAAS,GAAG,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC;oBAClC,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;wBACtB,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBAC/E,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,YAAY;oBACZ,SAAS,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC;oBACxB,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;wBACtB,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBAC/E,CAAC;gBACH,CAAC;gBAED,OAAO;oBACL,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,GAAG,EAAE,MAAM,CAAC,GAAG;oBACf,SAAS;oBACT,QAAQ;iBACT,CAAC;YACJ,CAAC,CAAC;YACF,OAAO;YACP,QAAQ;SACT,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,QAAyB;QACrD,MAAM,YAAY,GAAG;YACnB,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,KAAK;YACnC,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,KAAK;YACrC,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,KAAK;YACpC,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,KAAK;SACpC,CAAC;QAEF,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,OAAiE;QACnF,WAAW;QACX,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,eAAe;QACf,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACnE,CAAC;CACF;AAzPD,kCAyPC"}