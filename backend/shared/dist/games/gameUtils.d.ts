import { GameType, GameSeat } from '../types';
/**
 * 游戏工厂类
 */
export declare class GameFactory {
    /**
     * 根据游戏类型发牌
     */
    static dealCards(gameType: GameType, playerCount: number): {
        playerCards: import("../types").Card[][];
        remainingDeck: import("../types").Card[];
    };
    /**
     * 根据游戏类型计算结果
     */
    static calculateGameResult(gameType: GameType, players: any[]): import("./sangong").SangongGameResult | import("./niuniu").NiuniuGameResult;
    /**
     * 检查游戏是否可以开始
     */
    static canStartGame(gameType: GameType, players: any[]): boolean;
}
/**
 * 游戏状态管理工具
 */
export declare class GameStateManager {
    /**
     * 获取房间内的活跃玩家
     */
    static getActivePlayers(seats: GameSeat[]): GameSeat[];
    /**
     * 获取房间内的所有坐下的玩家
     */
    static getSeatedPlayers(seats: GameSeat[]): GameSeat[];
    /**
     * 检查是否有足够的玩家开始游戏
     */
    static hasEnoughPlayers(seats: GameSeat[], minPlayers?: number): boolean;
    /**
     * 重置座位状态（游戏结束后）
     */
    static resetSeatsForNewRound(seats: GameSeat[]): GameSeat[];
    /**
     * 更新玩家余额
     */
    static updatePlayerBalances(seats: GameSeat[], gameResult: any): GameSeat[];
}
/**
 * 游戏计时器管理
 */
export declare class GameTimer {
    private timers;
    /**
     * 设置计时器
     */
    setTimer(key: string, callback: () => void, delay: number): void;
    /**
     * 清除计时器
     */
    clearTimer(key: string): void;
    /**
     * 清除所有计时器
     */
    clearAllTimers(): void;
    /**
     * 检查计时器是否存在
     */
    hasTimer(key: string): boolean;
}
/**
 * 游戏事件类型
 */
export declare enum GameEvent {
    GAME_START = "game_start",
    BETTING_START = "betting_start",
    BETTING_END = "betting_end",
    DEALING_START = "dealing_start",
    DEALING_END = "dealing_end",
    GAME_END = "game_end",
    ROUND_END = "round_end"
}
/**
 * 游戏事件数据
 */
export interface GameEventData {
    gameType: GameType;
    roomId: string;
    event: GameEvent;
    data?: any;
    timestamp: Date;
}
/**
 * 游戏验证工具
 */
export declare class GameValidator {
    /**
     * 验证下注金额
     */
    static validateBetAmount(amount: number, minBet: number, maxBet: number, balance: number): boolean;
    /**
     * 验证座位号
     */
    static validateSeatNumber(seatNumber: number, maxSeats?: number): boolean;
    /**
     * 验证游戏状态转换
     */
    static canTransitionTo(currentStatus: string, targetStatus: string): boolean;
}
//# sourceMappingURL=gameUtils.d.ts.map