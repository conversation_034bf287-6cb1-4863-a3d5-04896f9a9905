{"version": 3, "file": "niuniu.js", "sourceRoot": "", "sources": ["../../src/games/niuniu.ts"], "names": [], "mappings": ";;;AAAA,oCAAsC;AACtC,4CAA0D;AAC1D,oCAA8D;AAE9D,SAAS;AACT,IAAY,cAeX;AAfD,WAAY,cAAc;IACxB,mCAAiB,CAAA;IACjB,iCAAe,CAAA;IACf,iCAAe,CAAA;IACf,iCAAe,CAAA;IACf,iCAAe,CAAA;IACf,iCAAe,CAAA;IACf,iCAAe,CAAA;IACf,iCAAe,CAAA;IACf,iCAAe,CAAA;IACf,iCAAe,CAAA;IACf,qCAAmB,CAAA;IACnB,2CAAyB,CAAA;IACzB,mCAAiB,CAAA;IACjB,6CAA2B,CAAA,CAAE,MAAM;AACrC,CAAC,EAfW,cAAc,8BAAd,cAAc,QAezB;AA0BD,MAAa,UAAU;IACrB;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,KAAa;QAC/B,IAAI,KAAK,CAAC,MAAM,KAAK,yBAAa,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;QAC/B,CAAC;QAED,SAAS;QACT,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,WAAW;QACX,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,KAAa;QAC5C,oBAAoB;QACpB,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACrE,IAAI,WAAW,IAAI,EAAE,EAAE,CAAC;YACtB,OAAO;gBACL,KAAK;gBACL,QAAQ,EAAE,cAAc,CAAC,WAAW;gBACpC,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,yBAAa,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI;gBACjD,UAAU,EAAE,yBAAa,CAAC,kBAAkB,CAAC,WAAW;gBACxD,WAAW,EAAE,QAAQ,WAAW,IAAI;aACrC,CAAC;QACJ,CAAC;QAED,aAAa;QACb,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;QAC9E,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO;gBACL,KAAK;gBACL,QAAQ,EAAE,cAAc,CAAC,MAAM;gBAC/B,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,yBAAa,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI;gBAC5C,UAAU,EAAE,yBAAa,CAAC,kBAAkB,CAAC,MAAM;gBACnD,WAAW,EAAE,IAAI;aAClB,CAAC;QACJ,CAAC;QAED,iBAAiB;QACjB,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CACrC,CAAC,YAAI,CAAC,IAAI,EAAE,YAAI,CAAC,KAAK,EAAE,YAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CACvD,CAAC;QACF,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO;gBACL,KAAK;gBACL,QAAQ,EAAE,cAAc,CAAC,UAAU;gBACnC,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,yBAAa,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI;gBAChD,UAAU,EAAE,yBAAa,CAAC,kBAAkB,CAAC,UAAU;gBACvD,WAAW,EAAE,KAAK;aACnB,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,KAAa;QAC3C,qBAAqB;QACrB,MAAM,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAE1D,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAE9E,IAAI,cAAc,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;gBAC9B,kBAAkB;gBAClB,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBACzE,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC/E,MAAM,MAAM,GAAG,YAAY,GAAG,EAAE,CAAC;gBAEjC,IAAI,QAAwB,CAAC;gBAC7B,IAAI,IAAY,CAAC;gBAEjB,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;oBACjB,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC;oBAClC,IAAI,GAAG,yBAAa,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;gBACjD,CAAC;qBAAM,CAAC;oBACN,QAAQ,GAAG,OAAO,MAAM,EAAoB,CAAC;oBAC7C,IAAI,GAAG,yBAAa,CAAC,UAAU,CAAC,OAAO,MAAM,EAA2C,CAAC,GAAG,IAAI,CAAC;gBACnG,CAAC;gBAED,OAAO;oBACL,KAAK;oBACL,QAAQ;oBACR,MAAM;oBACN,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;oBACxC,UAAU,EAAE,yBAAa,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAmD,CAAC,IAAI,CAAC;oBAC1H,WAAW;oBACX,WAAW,EAAE,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;iBAChD,CAAC;YACJ,CAAC;QACH,CAAC;QAED,KAAK;QACL,OAAO;YACL,KAAK;YACL,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,yBAAa,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;YAC1E,UAAU,EAAE,yBAAa,CAAC,kBAAkB,CAAC,MAAM;YACnD,WAAW,EAAE,IAAI;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,KAAa;QACnD,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC1C,YAAY,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,YAAY,CAAC,KAAa;QACvC,MAAM,MAAM,GAAgC,EAAE,CAAC;QAE/C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YAC1B,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAAC,KAAa;QAC1C,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,KAAiB,EAAE,KAAiB;QACtD,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,WAAmB;QAClC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,IAAI,GAAG,IAAA,mBAAW,EAAC,IAAA,kBAAU,GAAE,CAAC,CAAC;QACvC,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,WAAW,GAAG,IAAI,CAAC;QAEvB,YAAY;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,IAAA,iBAAS,EAAC,WAAW,EAAE,yBAAa,CAAC,gBAAgB,CAAC,CAAC;YACxF,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxB,WAAW,GAAG,aAAa,CAAC;QAC9B,CAAC;QAED,OAAO;YACL,WAAW;YACX,aAAa,EAAE,WAAW;SAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,OAKE;QAEF,YAAY;QACZ,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC9C,GAAG,MAAM;YACT,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC;SACtC,CAAC,CAAC,CAAC;QAEJ,UAAU;QACV,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAEnE,QAAQ;QACR,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YAC9C,IAAI,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC;YAC7B,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACtB,WAAW,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAC3F,CAAC;YACD,OAAO,GAAG,GAAG,WAAW,CAAC;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,uBAAuB;QACvB,MAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,OAAO,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEhC,kBAAkB;QAClB,MAAM,MAAM,GAAqB;YAC/B,OAAO,EAAE,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACrC,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC;gBACjD,IAAI,SAAS,GAAG,CAAC,CAAC;gBAElB,IAAI,QAAQ,EAAE,CAAC;oBACb,2BAA2B;oBAC3B,MAAM,UAAU,GAAG,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC;oBACzC,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;wBACtB,MAAM,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;wBACvF,SAAS,GAAG,CAAC,UAAU,GAAG,cAAc,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACrE,CAAC;yBAAM,CAAC;wBACN,SAAS,GAAG,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBAClD,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,qBAAqB;oBACrB,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACnD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;wBACtB,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;oBACxG,CAAC;gBACH,CAAC;gBAED,OAAO;oBACL,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,GAAG,EAAE,MAAM,CAAC,GAAG;oBACf,SAAS;oBACT,QAAQ;iBACT,CAAC;YACJ,CAAC,CAAC;YACF,OAAO;YACP,QAAQ;SACT,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,QAAwB;QACpD,MAAM,YAAY,GAAG;YACnB,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,KAAK;YACnC,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI;YAC7B,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,KAAK;YAClC,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,IAAI;YAC9B,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;YAC5B,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;YAC5B,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;YAC5B,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;YAC5B,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;YAC5B,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;YAC5B,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;YAC5B,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;YAC5B,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;YAC5B,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI;SAC9B,CAAC;QAEF,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,OAAiE;QACnF,WAAW;QACX,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,eAAe;QACf,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACnE,CAAC;CACF;AAzSD,gCAySC"}