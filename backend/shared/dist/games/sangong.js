"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SangongGame = exports.SangongHandType = void 0;
const types_1 = require("../types");
const constants_1 = require("../constants");
const utils_1 = require("../utils");
// 三公牌型枚举
var SangongHandType;
(function (SangongHandType) {
    SangongHandType["POINT_CARD"] = "point_card";
    SangongHandType["MIXED_HONOR"] = "mixed_honor";
    SangongHandType["SMALL_TRIPLE"] = "small_triple";
    SangongHandType["BIG_TRIPLE"] = "big_triple"; // 大三公
})(SangongHandType || (exports.SangongHandType = SangongHandType = {}));
class SangongGame {
    /**
     * 判断牌型
     */
    static evaluateHand(cards) {
        if (cards.length !== constants_1.SANGONG_CONFIG.CARDS_PER_PLAYER) {
            throw new Error('三公游戏需要3张牌');
        }
        const honorCards = cards.filter(card => constants_1.CARD_CONFIG.SANGONG_HONOR_CARDS.includes(card.rank));
        const honorCount = honorCards.length;
        // 检查是否为三公（三张都是公牌）
        if (honorCount === 3) {
            return this.evaluateTripleHonor(cards);
        }
        // 检查是否为混三公（有公牌但不是三张）
        if (honorCount > 0) {
            return this.evaluateMixedHonor(cards, honorCount);
        }
        // 点数牌
        return this.evaluatePointCard(cards);
    }
    /**
     * 评估大三公/小三公
     */
    static evaluateTripleHonor(cards) {
        const ranks = cards.map(card => card.rank);
        // 检查是否为大三公（三张相同的K/Q/J）
        if (ranks.every(rank => rank === ranks[0])) {
            const rank = ranks[0];
            const isBigTriple = [types_1.Rank.KING, types_1.Rank.QUEEN, types_1.Rank.JACK].includes(rank);
            if (isBigTriple) {
                return {
                    cards,
                    handType: SangongHandType.BIG_TRIPLE,
                    points: 0,
                    rank: constants_1.SANGONG_CONFIG.HAND_RANKS.BIG_TRIPLE * 1000 + constants_1.SANGONG_CONFIG.BIG_TRIPLE_ORDER[rank],
                    honorCount: 3,
                    description: `大三公 ${rank}${rank}${rank}`
                };
            }
            else {
                // 小三公（三张相同的数字牌，但这里不应该出现，因为我们已经过滤了公牌）
                return {
                    cards,
                    handType: SangongHandType.SMALL_TRIPLE,
                    points: 0,
                    rank: constants_1.SANGONG_CONFIG.HAND_RANKS.SMALL_TRIPLE * 1000 + constants_1.SANGONG_CONFIG.SMALL_TRIPLE_ORDER[rank],
                    honorCount: 3,
                    description: `小三公 ${rank}${rank}${rank}`
                };
            }
        }
        // 混三公（三张不同的公牌）
        const sortedRanks = ranks.sort((a, b) => constants_1.SANGONG_CONFIG.BIG_TRIPLE_ORDER[b] - constants_1.SANGONG_CONFIG.BIG_TRIPLE_ORDER[a]);
        const maxRank = sortedRanks[0];
        return {
            cards,
            handType: SangongHandType.MIXED_HONOR,
            points: 0,
            rank: constants_1.SANGONG_CONFIG.HAND_RANKS.MIXED_HONOR * 1000 + constants_1.SANGONG_CONFIG.BIG_TRIPLE_ORDER[maxRank],
            honorCount: 3,
            description: `混三公 ${ranks.join('')}`
        };
    }
    /**
     * 评估混三公（部分公牌）
     */
    static evaluateMixedHonor(cards, honorCount) {
        const points = this.calculatePoints(cards);
        const maxHonorRank = cards
            .filter(card => constants_1.CARD_CONFIG.SANGONG_HONOR_CARDS.includes(card.rank))
            .reduce((max, card) => constants_1.SANGONG_CONFIG.BIG_TRIPLE_ORDER[card.rank] > constants_1.SANGONG_CONFIG.BIG_TRIPLE_ORDER[max.rank] ? card : max).rank;
        return {
            cards,
            handType: SangongHandType.MIXED_HONOR,
            points,
            rank: constants_1.SANGONG_CONFIG.HAND_RANKS.MIXED_HONOR * 1000 + points * 10 + constants_1.SANGONG_CONFIG.BIG_TRIPLE_ORDER[maxHonorRank],
            honorCount,
            description: `混三公 ${points}点`
        };
    }
    /**
     * 评估点数牌
     */
    static evaluatePointCard(cards) {
        const points = this.calculatePoints(cards);
        const maxCard = cards.reduce((max, card) => card.value > max.value ? card : max);
        return {
            cards,
            handType: SangongHandType.POINT_CARD,
            points,
            rank: constants_1.SANGONG_CONFIG.HAND_RANKS.POINT_CARD * 1000 + points * 10 + maxCard.value,
            honorCount: 0,
            description: `${points}点`
        };
    }
    /**
     * 计算点数
     */
    static calculatePoints(cards) {
        const sum = cards.reduce((total, card) => total + card.value, 0);
        return sum % 10;
    }
    /**
     * 比较两手牌的大小
     */
    static compareHands(hand1, hand2) {
        return hand2.rank - hand1.rank; // 返回正数表示hand2更大
    }
    /**
     * 发牌
     */
    static dealCards(playerCount) {
        if (playerCount < 2 || playerCount > 10) {
            throw new Error('玩家数量必须在2-10之间');
        }
        const deck = (0, utils_1.shuffleDeck)((0, utils_1.createDeck)());
        const playerCards = [];
        let currentDeck = deck;
        // 给每个玩家发3张牌
        for (let i = 0; i < playerCount; i++) {
            const { cards, remainingDeck } = (0, utils_1.dealCards)(currentDeck, constants_1.SANGONG_CONFIG.CARDS_PER_PLAYER);
            playerCards.push(cards);
            currentDeck = remainingDeck;
        }
        return {
            playerCards,
            remainingDeck: currentDeck
        };
    }
    /**
     * 计算游戏结果
     */
    static calculateGameResult(players) {
        // 评估每个玩家的手牌
        const evaluatedPlayers = players.map(player => ({
            ...player,
            hand: this.evaluateHand(player.cards)
        }));
        // 排序找出获胜者
        evaluatedPlayers.sort((a, b) => this.compareHands(a.hand, b.hand));
        // 计算总奖池
        const totalPot = players.reduce((sum, player) => {
            let playerTotal = player.bet;
            if (player.sideWagers) {
                playerTotal += player.sideWagers.reduce((wagerSum, wager) => wagerSum + wager.amount, 0);
            }
            return sum + playerTotal;
        }, 0);
        // 大吃小机制：最大的牌型获胜，获得所有下注
        const winner = evaluatedPlayers[0];
        const winners = [winner.userId];
        // 计算每个玩家的输赢
        const result = {
            players: evaluatedPlayers.map(player => {
                const isWinner = player.userId === winner.userId;
                let winAmount = 0;
                if (isWinner) {
                    // 获胜者获得除自己下注外的所有金额
                    winAmount = totalPot - player.bet;
                    if (player.sideWagers) {
                        winAmount -= player.sideWagers.reduce((sum, wager) => sum + wager.amount, 0);
                    }
                }
                else {
                    // 失败者失去所有下注
                    winAmount = -player.bet;
                    if (player.sideWagers) {
                        winAmount -= player.sideWagers.reduce((sum, wager) => sum + wager.amount, 0);
                    }
                }
                return {
                    userId: player.userId,
                    hand: player.hand,
                    bet: player.bet,
                    winAmount,
                    isWinner
                };
            }),
            winners,
            totalPot
        };
        return result;
    }
    /**
     * 获取牌型描述
     */
    static getHandTypeDescription(handType) {
        const descriptions = {
            [SangongHandType.BIG_TRIPLE]: '大三公',
            [SangongHandType.SMALL_TRIPLE]: '小三公',
            [SangongHandType.MIXED_HONOR]: '混三公',
            [SangongHandType.POINT_CARD]: '点数牌'
        };
        return descriptions[handType];
    }
    /**
     * 验证游戏是否可以开始
     */
    static canStartGame(players) {
        // 至少需要2个玩家
        if (players.length < 2) {
            return false;
        }
        // 所有玩家都必须准备并下注
        return players.every(player => player.isReady && player.bet > 0);
    }
}
exports.SangongGame = SangongGame;
//# sourceMappingURL=sangong.js.map