import { Card } from '../types';
export declare enum SangongHandType {
    POINT_CARD = "point_card",// 点数牌
    MIXED_HONOR = "mixed_honor",// 混三公
    SMALL_TRIPLE = "small_triple",// 小三公
    BIG_TRIPLE = "big_triple"
}
export interface SangongHand {
    cards: Card[];
    handType: SangongHandType;
    points: number;
    rank: number;
    honorCount: number;
    description: string;
}
export interface SangongGameResult {
    players: {
        userId: string;
        hand: SangongHand;
        bet: number;
        winAmount: number;
        isWinner: boolean;
    }[];
    winners: string[];
    totalPot: number;
}
export declare class SangongGame {
    /**
     * 判断牌型
     */
    static evaluateHand(cards: Card[]): SangongHand;
    /**
     * 评估大三公/小三公
     */
    private static evaluateTripleHonor;
    /**
     * 评估混三公（部分公牌）
     */
    private static evaluateMixedHonor;
    /**
     * 评估点数牌
     */
    private static evaluatePointCard;
    /**
     * 计算点数
     */
    private static calculatePoints;
    /**
     * 比较两手牌的大小
     */
    static compareHands(hand1: SangongHand, hand2: SangongHand): number;
    /**
     * 发牌
     */
    static dealCards(playerCount: number): {
        playerCards: Card[][];
        remainingDeck: Card[];
    };
    /**
     * 计算游戏结果
     */
    static calculateGameResult(players: Array<{
        userId: string;
        cards: Card[];
        bet: number;
        sideWagers?: Array<{
            userId: string;
            amount: number;
        }>;
    }>): SangongGameResult;
    /**
     * 获取牌型描述
     */
    static getHandTypeDescription(handType: SangongHandType): string;
    /**
     * 验证游戏是否可以开始
     */
    static canStartGame(players: Array<{
        userId: string;
        isReady: boolean;
        bet: number;
    }>): boolean;
}
//# sourceMappingURL=sangong.d.ts.map