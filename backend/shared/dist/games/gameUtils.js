"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameValidator = exports.GameEvent = exports.GameTimer = exports.GameStateManager = exports.GameFactory = void 0;
const types_1 = require("../types");
const sangong_1 = require("./sangong");
const niuniu_1 = require("./niuniu");
/**
 * 游戏工厂类
 */
class GameFactory {
    /**
     * 根据游戏类型发牌
     */
    static dealCards(gameType, playerCount) {
        switch (gameType) {
            case types_1.GameType.SANGONG:
                return sangong_1.SangongGame.dealCards(playerCount);
            case types_1.GameType.NIUNIU:
                return niuniu_1.NiuniuGame.dealCards(playerCount);
            default:
                throw new Error(`Unsupported game type: ${gameType}`);
        }
    }
    /**
     * 根据游戏类型计算结果
     */
    static calculateGameResult(gameType, players) {
        switch (gameType) {
            case types_1.GameType.SANGONG:
                return sangong_1.SangongGame.calculateGameResult(players);
            case types_1.GameType.NIUNIU:
                return niuniu_1.NiuniuGame.calculateGameResult(players);
            default:
                throw new Error(`Unsupported game type: ${gameType}`);
        }
    }
    /**
     * 检查游戏是否可以开始
     */
    static canStartGame(gameType, players) {
        switch (gameType) {
            case types_1.GameType.SANGONG:
                return sangong_1.SangongGame.canStartGame(players);
            case types_1.GameType.NIUNIU:
                return niuniu_1.NiuniuGame.canStartGame(players);
            default:
                return false;
        }
    }
}
exports.GameFactory = GameFactory;
/**
 * 游戏状态管理工具
 */
class GameStateManager {
    /**
     * 获取房间内的活跃玩家
     */
    static getActivePlayers(seats) {
        return seats.filter(seat => seat.userId && seat.isReady && seat.bet > 0);
    }
    /**
     * 获取房间内的所有坐下的玩家
     */
    static getSeatedPlayers(seats) {
        return seats.filter(seat => seat.userId);
    }
    /**
     * 检查是否有足够的玩家开始游戏
     */
    static hasEnoughPlayers(seats, minPlayers = 2) {
        const activePlayers = this.getActivePlayers(seats);
        return activePlayers.length >= minPlayers;
    }
    /**
     * 重置座位状态（游戏结束后）
     */
    static resetSeatsForNewRound(seats) {
        return seats.map(seat => ({
            ...seat,
            bet: 0,
            cards: [],
            isReady: false,
            cardsShown: false,
            sideWagers: []
        }));
    }
    /**
     * 更新玩家余额
     */
    static updatePlayerBalances(seats, gameResult) {
        return seats.map(seat => {
            if (!seat.userId)
                return seat;
            const playerResult = gameResult.players.find((p) => p.userId === seat.userId);
            if (playerResult) {
                return {
                    ...seat,
                    balance: seat.balance + playerResult.winAmount
                };
            }
            return seat;
        });
    }
}
exports.GameStateManager = GameStateManager;
/**
 * 游戏计时器管理
 */
class GameTimer {
    constructor() {
        this.timers = new Map();
    }
    /**
     * 设置计时器
     */
    setTimer(key, callback, delay) {
        this.clearTimer(key);
        const timer = setTimeout(callback, delay);
        this.timers.set(key, timer);
    }
    /**
     * 清除计时器
     */
    clearTimer(key) {
        const timer = this.timers.get(key);
        if (timer) {
            clearTimeout(timer);
            this.timers.delete(key);
        }
    }
    /**
     * 清除所有计时器
     */
    clearAllTimers() {
        this.timers.forEach(timer => clearTimeout(timer));
        this.timers.clear();
    }
    /**
     * 检查计时器是否存在
     */
    hasTimer(key) {
        return this.timers.has(key);
    }
}
exports.GameTimer = GameTimer;
/**
 * 游戏事件类型
 */
var GameEvent;
(function (GameEvent) {
    GameEvent["GAME_START"] = "game_start";
    GameEvent["BETTING_START"] = "betting_start";
    GameEvent["BETTING_END"] = "betting_end";
    GameEvent["DEALING_START"] = "dealing_start";
    GameEvent["DEALING_END"] = "dealing_end";
    GameEvent["GAME_END"] = "game_end";
    GameEvent["ROUND_END"] = "round_end";
})(GameEvent || (exports.GameEvent = GameEvent = {}));
/**
 * 游戏验证工具
 */
class GameValidator {
    /**
     * 验证下注金额
     */
    static validateBetAmount(amount, minBet, maxBet, balance) {
        return amount >= minBet && amount <= maxBet && amount <= balance && amount > 0;
    }
    /**
     * 验证座位号
     */
    static validateSeatNumber(seatNumber, maxSeats = 10) {
        return seatNumber >= 1 && seatNumber <= maxSeats;
    }
    /**
     * 验证游戏状态转换
     */
    static canTransitionTo(currentStatus, targetStatus) {
        const validTransitions = {
            'waiting': ['betting'],
            'betting': ['dealing', 'waiting'],
            'dealing': ['playing'],
            'playing': ['settling'],
            'settling': ['waiting', 'finished'],
            'finished': ['waiting']
        };
        return validTransitions[currentStatus]?.includes(targetStatus) || false;
    }
}
exports.GameValidator = GameValidator;
//# sourceMappingURL=gameUtils.js.map