export declare enum Suit {
    HEARTS = "hearts",// 红桃
    DIAMONDS = "diamonds",// 方块
    CLUBS = "clubs",// 梅花
    SPADES = "spades"
}
export declare enum Rank {
    ACE = "A",
    TWO = "2",
    THREE = "3",
    FOUR = "4",
    FIVE = "5",
    SIX = "6",
    SEVEN = "7",
    EIGHT = "8",
    NINE = "9",
    TEN = "10",
    JACK = "J",
    QUEEN = "Q",
    KING = "K"
}
export interface Card {
    suit: Suit;
    rank: Rank;
    value: number;
}
export interface User {
    id: string;
    username: string;
    email?: string;
    phone?: string;
    balance: number;
    agentId?: string;
    clubId?: string;
    status: UserStatus;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum UserStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    BANNED = "banned"
}
export interface Agent {
    id: string;
    username: string;
    email: string;
    balance: number;
    commission: number;
    status: AgentStatus;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum AgentStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    BANNED = "banned"
}
export interface Club {
    id: string;
    name: string;
    agentId: string;
    description?: string;
    maxMembers: number;
    currentMembers: number;
    status: ClubStatus;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum ClubStatus {
    ACTIVE = "active",
    INACTIVE = "inactive"
}
export declare enum GameType {
    SANGONG = "sangong",// 三公
    NIUNIU = "niuniu"
}
export declare enum GameStatus {
    WAITING = "waiting",// 等待玩家
    BETTING = "betting",// 下注阶段
    DEALING = "dealing",// 发牌阶段
    PLAYING = "playing",// 游戏进行中
    SETTLING = "settling",// 结算阶段
    FINISHED = "finished"
}
export interface GameRoom {
    id: string;
    gameType: GameType;
    status: GameStatus;
    seats: GameSeat[];
    spectators: string[];
    currentRound: number;
    minBet: number;
    maxBet: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface GameSeat {
    position: number;
    userId?: string;
    username?: string;
    balance: number;
    bet: number;
    cards: Card[];
    isReady: boolean;
    cardsShown?: boolean;
    sideWagers: SideWager[];
}
export interface SideWager {
    userId: string;
    username: string;
    amount: number;
    targetSeat: number;
}
export interface GameRecord {
    id: string;
    roomId: string;
    gameType: GameType;
    players: GamePlayer[];
    result: GameResult;
    totalPot: number;
    createdAt: Date;
}
export interface GamePlayer {
    userId: string;
    username: string;
    seat: number;
    cards: Card[];
    bet: number;
    winAmount: number;
    sideWagers: SideWager[];
}
export interface GameResult {
    winners: string[];
    payouts: {
        [userId: string]: number;
    };
}
export declare enum MessageType {
    CONNECT = "connect",
    DISCONNECT = "disconnect",
    JOIN_ROOM = "join_room",
    LEAVE_ROOM = "leave_room",
    ROOM_UPDATE = "room_update",
    PLACE_BET = "place_bet",
    READY = "ready",
    DEAL_CARDS = "deal_cards",
    GAME_START = "game_start",
    GAME_END = "game_end",
    PLACE_SIDE_WAGER = "place_side_wager",
    CHAT_MESSAGE = "chat_message",
    ERROR = "error"
}
export interface WebSocketMessage {
    type: MessageType;
    data: any;
    timestamp: Date;
    userId?: string;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
export interface PaginationParams {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
//# sourceMappingURL=index.d.ts.map