"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageType = exports.GameStatus = exports.GameType = exports.ClubStatus = exports.AgentStatus = exports.UserStatus = exports.Rank = exports.Suit = void 0;
// 扑克牌相关类型
var Suit;
(function (Suit) {
    Suit["HEARTS"] = "hearts";
    Suit["DIAMONDS"] = "diamonds";
    Suit["CLUBS"] = "clubs";
    Suit["SPADES"] = "spades"; // 黑桃
})(Suit || (exports.Suit = Suit = {}));
var Rank;
(function (Rank) {
    Rank["ACE"] = "A";
    Rank["TWO"] = "2";
    Rank["THREE"] = "3";
    Rank["FOUR"] = "4";
    Rank["FIVE"] = "5";
    Rank["SIX"] = "6";
    Rank["SEVEN"] = "7";
    Rank["EIGHT"] = "8";
    Rank["NINE"] = "9";
    Rank["TEN"] = "10";
    Rank["JACK"] = "J";
    Rank["QUEEN"] = "Q";
    Rank["KING"] = "K";
})(Rank || (exports.Rank = Rank = {}));
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "active";
    UserStatus["INACTIVE"] = "inactive";
    UserStatus["BANNED"] = "banned";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
var AgentStatus;
(function (AgentStatus) {
    AgentStatus["ACTIVE"] = "active";
    AgentStatus["INACTIVE"] = "inactive";
    AgentStatus["BANNED"] = "banned";
})(AgentStatus || (exports.AgentStatus = AgentStatus = {}));
var ClubStatus;
(function (ClubStatus) {
    ClubStatus["ACTIVE"] = "active";
    ClubStatus["INACTIVE"] = "inactive";
})(ClubStatus || (exports.ClubStatus = ClubStatus = {}));
// 游戏相关类型
var GameType;
(function (GameType) {
    GameType["SANGONG"] = "sangong";
    GameType["NIUNIU"] = "niuniu"; // 牛牛
})(GameType || (exports.GameType = GameType = {}));
var GameStatus;
(function (GameStatus) {
    GameStatus["WAITING"] = "waiting";
    GameStatus["BETTING"] = "betting";
    GameStatus["DEALING"] = "dealing";
    GameStatus["PLAYING"] = "playing";
    GameStatus["SETTLING"] = "settling";
    GameStatus["FINISHED"] = "finished"; // 游戏结束
})(GameStatus || (exports.GameStatus = GameStatus = {}));
// WebSocket消息类型
var MessageType;
(function (MessageType) {
    // 连接相关
    MessageType["CONNECT"] = "connect";
    MessageType["DISCONNECT"] = "disconnect";
    // 房间相关
    MessageType["JOIN_ROOM"] = "join_room";
    MessageType["LEAVE_ROOM"] = "leave_room";
    MessageType["ROOM_UPDATE"] = "room_update";
    // 游戏相关
    MessageType["PLACE_BET"] = "place_bet";
    MessageType["READY"] = "ready";
    MessageType["DEAL_CARDS"] = "deal_cards";
    MessageType["GAME_START"] = "game_start";
    MessageType["GAME_END"] = "game_end";
    // 旁注相关
    MessageType["PLACE_SIDE_WAGER"] = "place_side_wager";
    // 聊天相关
    MessageType["CHAT_MESSAGE"] = "chat_message";
    // 错误相关
    MessageType["ERROR"] = "error";
})(MessageType || (exports.MessageType = MessageType = {}));
//# sourceMappingURL=index.js.map