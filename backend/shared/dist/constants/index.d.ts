import { Rank, Suit } from '../types';
export declare const GAME_CONFIG: {
    MAX_SEATS: number;
    MIN_PLAYERS: number;
    MAX_SPECTATORS: number;
    DEFAULT_MIN_BET: number;
    DEFAULT_MAX_BET: number;
    BETTING_TIME: number;
    READY_TIME: number;
    DEALING_TIME: number;
    SETTLING_TIME: number;
    MAX_ROUNDS: number;
};
export declare const CARD_CONFIG: {
    RANK_VALUES: {
        A: number;
        "2": number;
        "3": number;
        "4": number;
        "5": number;
        "6": number;
        "7": number;
        "8": number;
        "9": number;
        "10": number;
        J: number;
        Q: number;
        K: number;
    };
    SANGONG_HONOR_CARDS: Rank[];
    FULL_DECK: {
        suit: Suit;
        rank: Rank;
    }[];
};
export declare const SANGONG_CONFIG: {
    CARDS_PER_PLAYER: number;
    HAND_RANKS: {
        POINT_CARD: number;
        MIXED_HONOR: number;
        SMALL_TRIPLE: number;
        BIG_TRIPLE: number;
    };
    BIG_TRIPLE_ORDER: {
        K: number;
        Q: number;
        J: number;
    };
    SMALL_TRIPLE_ORDER: {
        "10": number;
        "9": number;
        "8": number;
        "7": number;
        "6": number;
        "5": number;
        "4": number;
        "3": number;
        "2": number;
        A: number;
    };
};
export declare const NIUNIU_CONFIG: {
    CARDS_PER_PLAYER: number;
    HAND_RANKS: {
        NO_NIU: number;
        NIU_1: number;
        NIU_2: number;
        NIU_3: number;
        NIU_4: number;
        NIU_5: number;
        NIU_6: number;
        NIU_7: number;
        NIU_8: number;
        NIU_9: number;
        NIU_NIU: number;
        WU_HUA_NIU: number;
        SI_ZHA: number;
        WU_XIAO_NIU: number;
    };
    PAYOUT_MULTIPLIERS: {
        NO_NIU: number;
        NIU_1: number;
        NIU_2: number;
        NIU_3: number;
        NIU_4: number;
        NIU_5: number;
        NIU_6: number;
        NIU_7: number;
        NIU_8: number;
        NIU_9: number;
        NIU_NIU: number;
        WU_HUA_NIU: number;
        SI_ZHA: number;
        WU_XIAO_NIU: number;
    };
};
export declare const DB_TABLES: {
    USERS: string;
    AGENTS: string;
    CLUBS: string;
    GAME_ROOMS: string;
    GAME_RECORDS: string;
    TRANSACTIONS: string;
    USER_SESSIONS: string;
};
export declare const REDIS_KEYS: {
    USER_SESSION: string;
    GAME_ROOM: string;
    GAME_STATE: string;
    USER_ONLINE: string;
    ROOM_PLAYERS: string;
};
export declare const API_PATHS: {
    AUTH: {
        LOGIN: string;
        REGISTER: string;
        LOGOUT: string;
        REFRESH: string;
    };
    USERS: {
        PROFILE: string;
        BALANCE: string;
        HISTORY: string;
    };
    AGENTS: {
        LIST: string;
        CREATE: string;
        UPDATE: string;
        DELETE: string;
    };
    CLUBS: {
        LIST: string;
        JOIN: string;
        LEAVE: string;
        MEMBERS: string;
    };
    GAMES: {
        ROOMS: string;
        JOIN: string;
        LEAVE: string;
        HISTORY: string;
    };
};
export declare const WS_EVENTS: {
    CONNECTION: string;
    DISCONNECT: string;
    JOIN_ROOM: string;
    LEAVE_ROOM: string;
    PLACE_BET: string;
    READY: string;
    GAME_UPDATE: string;
    CHAT: string;
    ERROR: string;
};
export declare const ERROR_CODES: {
    UNAUTHORIZED: string;
    INVALID_TOKEN: string;
    USER_NOT_FOUND: string;
    INSUFFICIENT_BALANCE: string;
    ROOM_FULL: string;
    GAME_IN_PROGRESS: string;
    INVALID_BET: string;
    NOT_YOUR_TURN: string;
    INTERNAL_ERROR: string;
    DATABASE_ERROR: string;
};
//# sourceMappingURL=index.d.ts.map