"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = exports.server = void 0;
exports.initializeApp = initializeApp;
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("./config/database");
const auth_1 = require("./middleware/auth");
const gameHandler_1 = require("./handlers/gameHandler");
const chatHandler_1 = require("./handlers/chatHandler");
const socket_1 = require("./types/socket");
// 加载环境变量
dotenv_1.default.config();
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
exports.server = server;
// CORS配置
const corsOptions = {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true,
    optionsSuccessStatus: 200
};
// Socket.IO配置
const io = new socket_io_1.Server(server, {
    cors: corsOptions,
    transports: ['websocket', 'polling']
});
exports.io = io;
// Express中间件
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)(corsOptions));
if (process.env.NODE_ENV !== 'test') {
    app.use((0, morgan_1.default)('combined'));
}
app.use(express_1.default.json());
// 基本路由
app.get('/', (req, res) => {
    res.json({
        success: true,
        message: '🎮 游戏平台WebSocket服务',
        version: '1.0.0',
        timestamp: new Date().toISOString()
    });
});
app.get('/health', (req, res) => {
    res.json({
        success: true,
        data: {
            status: 'OK',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            connections: io.engine.clientsCount
        }
    });
});
// 创建处理器实例
const gameHandler = new gameHandler_1.GameHandler(io);
const chatHandler = new chatHandler_1.ChatHandler(io);
// Socket.IO认证中间件
io.use(auth_1.authenticateSocket);
// Socket.IO连接处理
io.on(socket_1.SocketEvents.CONNECTION, (socket) => {
    console.log(`🔌 用户连接: ${socket.username} (${socket.userType}) - ${socket.id}`);
    // 发送连接成功消息
    socket.emit(socket_1.SocketEvents.NOTIFICATION, {
        type: 'success',
        title: '连接成功',
        message: `欢迎 ${socket.username}！`,
        duration: 3000
    });
    // 游戏相关事件
    socket.on(socket_1.SocketEvents.JOIN_ROOM, (data) => {
        gameHandler.handleJoinRoom(socket, data);
    });
    socket.on(socket_1.SocketEvents.LEAVE_ROOM, () => {
        gameHandler.handleLeaveRoom(socket);
    });
    socket.on(socket_1.SocketEvents.SIT_DOWN, (data) => {
        gameHandler.handleSitDown(socket, data);
    });
    socket.on(socket_1.SocketEvents.STAND_UP, () => {
        gameHandler.handleStandUp(socket);
    });
    socket.on(socket_1.SocketEvents.READY, () => {
        gameHandler.handleReady(socket);
    });
    socket.on(socket_1.SocketEvents.PLACE_BET, (data) => {
        gameHandler.handlePlaceBet(socket, data);
    });
    socket.on(socket_1.SocketEvents.PLACE_SIDE_WAGER, (data) => {
        gameHandler.handlePlaceSideWager(socket, data);
    });
    socket.on(socket_1.SocketEvents.SHOW_CARDS, () => {
        gameHandler.handleShowCards(socket);
    });
    // 聊天相关事件
    socket.on(socket_1.SocketEvents.CHAT_MESSAGE, (data) => {
        chatHandler.handleChatMessage(socket, data);
    });
    // 断开连接处理
    socket.on(socket_1.SocketEvents.DISCONNECT, async (reason) => {
        console.log(`🔌 用户断开连接: ${socket.username} - ${reason}`);
        // 自动离开房间
        if (socket.currentRoom) {
            try {
                await gameHandler.handleLeaveRoom(socket);
            }
            catch (error) {
                console.error('自动离开房间失败:', error);
            }
        }
    });
    // 错误处理
    socket.on('error', (error) => {
        console.error(`Socket错误 (${socket.username}):`, error);
    });
});
// Socket.IO错误处理
io.engine.on('connection_error', (err) => {
    console.error('Socket.IO连接错误:', err);
});
// 初始化应用
async function initializeApp() {
    try {
        console.log('🔄 初始化WebSocket应用...');
        // 测试数据库连接
        const dbConnected = await (0, database_1.testDatabaseConnection)();
        if (!dbConnected) {
            throw new Error('数据库连接失败');
        }
        // 初始化Redis
        await (0, database_1.initRedis)();
        console.log('✅ WebSocket应用初始化完成');
        return true;
    }
    catch (error) {
        console.error('❌ WebSocket应用初始化失败:', error);
        return false;
    }
}
exports.default = app;
