"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameHandler = void 0;
const socket_1 = require("../types/socket");
const roomService_1 = require("../services/roomService");
const gameEngine_1 = require("../services/gameEngine");
const auth_1 = require("../middleware/auth");
const database_1 = require("../config/database");
class GameHandler {
    constructor(io) {
        this.io = io;
        this.gameEngine = new gameEngine_1.GameEngine(io);
    }
    /**
     * 处理加入房间
     */
    async handleJoinRoom(socket, data) {
        try {
            if (!await (0, auth_1.validateGamePermission)(socket)) {
                return;
            }
            const { gameType } = data;
            const roomName = `room:${gameType}`;
            // 加入Socket.IO房间
            await socket.join(roomName);
            socket.currentRoom = roomName;
            // 加入游戏房间作为旁观者
            await roomService_1.RoomService.joinAsSpectator(gameType, socket.userId);
            // 发送房间状态
            const roomState = await roomService_1.RoomService.getRoomStateUpdate(gameType);
            socket.emit(socket_1.SocketEvents.ROOM_UPDATE, roomState);
            // 通知其他用户
            socket.to(roomName).emit(socket_1.SocketEvents.NOTIFICATION, {
                type: 'info',
                title: '用户加入',
                message: `${socket.username} 加入了房间`,
                duration: 3000
            });
            console.log(`✅ 用户 ${socket.username} 加入房间 ${gameType}`);
        }
        catch (error) {
            console.error('加入房间失败:', error);
            (0, auth_1.sendError)(socket, 'JOIN_ROOM_FAILED', error.message);
        }
    }
    /**
     * 处理离开房间
     */
    async handleLeaveRoom(socket) {
        try {
            if (!socket.currentRoom || !socket.userId) {
                return;
            }
            const gameType = socket.currentRoom.replace('room:', '');
            // 离开游戏房间
            await roomService_1.RoomService.leaveRoom(gameType, socket.userId);
            // 离开Socket.IO房间
            await socket.leave(socket.currentRoom);
            // 广播房间状态更新
            const roomState = await roomService_1.RoomService.getRoomStateUpdate(gameType);
            this.io.to(socket.currentRoom).emit(socket_1.SocketEvents.ROOM_UPDATE, roomState);
            // 通知其他用户
            socket.to(socket.currentRoom).emit(socket_1.SocketEvents.NOTIFICATION, {
                type: 'info',
                title: '用户离开',
                message: `${socket.username} 离开了房间`,
                duration: 3000
            });
            socket.currentRoom = undefined;
            console.log(`✅ 用户 ${socket.username} 离开房间 ${gameType}`);
        }
        catch (error) {
            console.error('离开房间失败:', error);
            (0, auth_1.sendError)(socket, 'LEAVE_ROOM_FAILED', error.message);
        }
    }
    /**
     * 处理坐下
     */
    async handleSitDown(socket, data) {
        try {
            if (!await (0, auth_1.validateGamePermission)(socket)) {
                return;
            }
            if (!socket.currentRoom || !socket.userId) {
                (0, auth_1.sendError)(socket, 'NOT_IN_ROOM', 'You must join a room first');
                return;
            }
            const gameType = socket.currentRoom.replace('room:', '');
            const { seatNumber } = data;
            // 坐下
            await roomService_1.RoomService.sitDown(gameType, socket.userId, socket.username, seatNumber);
            // 广播房间状态更新
            const roomState = await roomService_1.RoomService.getRoomStateUpdate(gameType);
            this.io.to(socket.currentRoom).emit(socket_1.SocketEvents.ROOM_UPDATE, roomState);
            // 通知其他用户
            socket.to(socket.currentRoom).emit(socket_1.SocketEvents.NOTIFICATION, {
                type: 'success',
                title: '用户坐下',
                message: `${socket.username} 坐在了 ${seatNumber} 号位`,
                duration: 3000
            });
            console.log(`✅ 用户 ${socket.username} 坐在 ${gameType} 房间的 ${seatNumber} 号位`);
            // 检查是否可以开始游戏
            await this.checkAutoStart(gameType);
        }
        catch (error) {
            console.error('坐下失败:', error);
            (0, auth_1.sendError)(socket, 'SIT_DOWN_FAILED', error.message);
        }
    }
    /**
     * 处理站起
     */
    async handleStandUp(socket) {
        try {
            if (!socket.currentRoom || !socket.userId) {
                (0, auth_1.sendError)(socket, 'NOT_IN_ROOM', 'You must join a room first');
                return;
            }
            const gameType = socket.currentRoom.replace('room:', '');
            // 站起
            await roomService_1.RoomService.standUp(gameType, socket.userId);
            // 广播房间状态更新
            const roomState = await roomService_1.RoomService.getRoomStateUpdate(gameType);
            this.io.to(socket.currentRoom).emit(socket_1.SocketEvents.ROOM_UPDATE, roomState);
            // 通知其他用户
            socket.to(socket.currentRoom).emit(socket_1.SocketEvents.NOTIFICATION, {
                type: 'info',
                title: '用户站起',
                message: `${socket.username} 站起了`,
                duration: 3000
            });
            console.log(`✅ 用户 ${socket.username} 从 ${gameType} 房间站起`);
        }
        catch (error) {
            console.error('站起失败:', error);
            (0, auth_1.sendError)(socket, 'STAND_UP_FAILED', error.message);
        }
    }
    /**
     * 处理准备
     */
    async handleReady(socket) {
        try {
            if (!socket.currentRoom || !socket.userId) {
                (0, auth_1.sendError)(socket, 'NOT_IN_ROOM', 'You must join a room first');
                return;
            }
            const gameType = socket.currentRoom.replace('room:', '');
            const room = await roomService_1.RoomService.getGameRoom(gameType);
            // 找到用户的座位
            const seatIndex = room.seats.findIndex((s) => s.userId === socket.userId);
            if (seatIndex === -1) {
                (0, auth_1.sendError)(socket, 'NOT_SEATED', 'You must be seated to ready');
                return;
            }
            // 设置准备状态
            room.seats[seatIndex].isReady = !room.seats[seatIndex].isReady;
            await roomService_1.RoomService.updateGameRoom(gameType, {
                seats: room.seats
            });
            // 广播房间状态更新
            const roomState = await roomService_1.RoomService.getRoomStateUpdate(gameType);
            this.io.to(socket.currentRoom).emit(socket_1.SocketEvents.ROOM_UPDATE, roomState);
            const readyStatus = room.seats[seatIndex].isReady ? '准备' : '取消准备';
            console.log(`✅ 用户 ${socket.username} ${readyStatus}`);
            // 尝试自动开始游戏
            setTimeout(() => {
                this.gameEngine.tryStartGame(gameType);
            }, 1000);
        }
        catch (error) {
            console.error('准备失败:', error);
            (0, auth_1.sendError)(socket, 'READY_FAILED', error.message);
        }
    }
    /**
     * 处理下注
     */
    async handlePlaceBet(socket, data) {
        try {
            if (!socket.currentRoom || !socket.userId) {
                (0, auth_1.sendError)(socket, 'NOT_IN_ROOM', 'You must join a room first');
                return;
            }
            const gameType = socket.currentRoom.replace('room:', '');
            const { amount } = data;
            const room = await roomService_1.RoomService.getGameRoom(gameType);
            // 检查游戏状态
            if (room.status !== 'betting') {
                (0, auth_1.sendError)(socket, 'INVALID_GAME_STATE', 'Not in betting phase');
                return;
            }
            // 找到用户的座位
            const seatIndex = room.seats.findIndex((s) => s.userId === socket.userId);
            if (seatIndex === -1) {
                (0, auth_1.sendError)(socket, 'NOT_SEATED', 'You must be seated to bet');
                return;
            }
            // 检查是否已经下注
            if (room.seats[seatIndex].bet > 0) {
                (0, auth_1.sendError)(socket, 'ALREADY_BET', 'You have already placed a bet');
                return;
            }
            // 验证下注金额
            if (amount < room.min_bet || amount > room.max_bet) {
                (0, auth_1.sendError)(socket, 'INVALID_BET_AMOUNT', `Bet amount must be between ${room.min_bet} and ${room.max_bet}`);
                return;
            }
            if (amount <= 0 || !Number.isInteger(amount)) {
                (0, auth_1.sendError)(socket, 'INVALID_BET_AMOUNT', 'Bet amount must be a positive integer');
                return;
            }
            // 从数据库获取最新用户余额
            const [users] = await database_1.pool.execute('SELECT balance FROM users WHERE id = ?', [socket.userId]);
            if (!Array.isArray(users) || users.length === 0) {
                (0, auth_1.sendError)(socket, 'USER_NOT_FOUND', 'User not found');
                return;
            }
            const userBalance = users[0].balance;
            // 检查用户余额
            if (userBalance < amount) {
                (0, auth_1.sendError)(socket, 'INSUFFICIENT_BALANCE', `Insufficient balance. Current: ${userBalance}, Required: ${amount}`);
                return;
            }
            // 设置下注金额
            room.seats[seatIndex].bet = amount;
            room.seats[seatIndex].balance = userBalance; // 更新座位中的余额显示
            await roomService_1.RoomService.updateGameRoom(gameType, {
                seats: room.seats
            });
            // 广播房间状态更新
            const roomState = await roomService_1.RoomService.getRoomStateUpdate(gameType);
            this.io.to(socket.currentRoom).emit(socket_1.SocketEvents.ROOM_UPDATE, roomState);
            console.log(`✅ 用户 ${socket.username} 下注 ${amount}，余额: ${userBalance}`);
            // 检查是否所有玩家都已下注，如果是则立即开始发牌
            const seatedPlayers = room.seats.filter((seat) => seat.userId);
            const bettingPlayers = seatedPlayers.filter((seat) => seat.bet > 0);
            if (bettingPlayers.length === seatedPlayers.length && seatedPlayers.length >= 2) {
                console.log('🎯 所有玩家已下注，立即开始发牌');
                setTimeout(() => {
                    this.gameEngine.endBettingPhase(gameType);
                }, 1000);
            }
        }
        catch (error) {
            console.error('下注失败:', error);
            (0, auth_1.sendError)(socket, 'PLACE_BET_FAILED', error.message);
        }
    }
    /**
     * 处理旁注
     */
    async handlePlaceSideWager(socket, data) {
        try {
            if (!socket.currentRoom || !socket.userId) {
                (0, auth_1.sendError)(socket, 'NOT_IN_ROOM', 'You must join a room first');
                return;
            }
            const gameType = socket.currentRoom.replace('room:', '');
            const { amount, targetSeat } = data;
            const room = await roomService_1.RoomService.getGameRoom(gameType);
            // 检查游戏状态
            if (room.status !== 'betting') {
                (0, auth_1.sendError)(socket, 'INVALID_GAME_STATE', 'Not in betting phase');
                return;
            }
            // 检查目标座位
            const targetSeatData = room.seats.find((s) => s.position === targetSeat);
            if (!targetSeatData || !targetSeatData.userId) {
                (0, auth_1.sendError)(socket, 'INVALID_TARGET_SEAT', 'Target seat is empty');
                return;
            }
            // 不能押注自己
            if (targetSeatData.userId === socket.userId) {
                (0, auth_1.sendError)(socket, 'CANNOT_BET_SELF', 'Cannot place side wager on yourself');
                return;
            }
            // 验证下注金额
            if (amount < room.min_bet || amount > room.max_bet) {
                (0, auth_1.sendError)(socket, 'INVALID_BET_AMOUNT', `Bet amount must be between ${room.min_bet} and ${room.max_bet}`);
                return;
            }
            // TODO: 检查用户余额（需要从数据库获取最新余额）
            // 添加旁注
            const sideWager = {
                userId: socket.userId,
                username: socket.username,
                amount,
                targetSeat
            };
            targetSeatData.sideWagers.push(sideWager);
            await roomService_1.RoomService.updateGameRoom(gameType, {
                seats: room.seats
            });
            // 广播房间状态更新
            const roomState = await roomService_1.RoomService.getRoomStateUpdate(gameType);
            this.io.to(socket.currentRoom).emit(socket_1.SocketEvents.ROOM_UPDATE, roomState);
            console.log(`✅ 用户 ${socket.username} 对 ${targetSeat} 号位旁注 ${amount}`);
        }
        catch (error) {
            console.error('旁注失败:', error);
            (0, auth_1.sendError)(socket, 'PLACE_SIDE_WAGER_FAILED', error.message);
        }
    }
    /**
     * 检查自动开始游戏
     */
    async checkAutoStart(gameType) {
        try {
            const room = await roomService_1.RoomService.getGameRoom(gameType);
            const roomName = `room:${gameType}`;
            // 只在等待状态下检查
            if (room.status !== 'waiting') {
                return;
            }
            // 计算坐下的玩家数量
            const seatedPlayers = room.seats.filter((seat) => seat && seat.userId).length;
            console.log(`🎮 房间 ${gameType} 当前玩家数: ${seatedPlayers}`);
            // 检查是否达到最少玩家数（2人）
            if (seatedPlayers >= 2) {
                console.log(`⏰ 游戏将在5秒后开始 (${gameType})`);
                // 通知所有玩家
                this.io.to(roomName).emit(socket_1.SocketEvents.NOTIFICATION, {
                    type: 'info',
                    title: '游戏即将开始',
                    message: '5秒后开始游戏，快来坐下吧！',
                    duration: 5000
                });
                // 设置5秒后开始游戏
                setTimeout(async () => {
                    await this.tryAutoStartGame(gameType);
                }, 5000);
            }
        }
        catch (error) {
            console.error('检查自动开始失败:', error);
        }
    }
    /**
     * 尝试自动开始游戏
     */
    async tryAutoStartGame(gameType) {
        try {
            const room = await roomService_1.RoomService.getGameRoom(gameType);
            const roomName = `room:${gameType}`;
            // 再次检查游戏状态
            if (room.status !== 'waiting') {
                return;
            }
            // 计算坐下的玩家数量
            const seatedPlayers = room.seats.filter((seat) => seat && seat.userId).length;
            if (seatedPlayers >= 2) {
                console.log(`🚀 自动开始游戏 ${gameType}，玩家数: ${seatedPlayers}`);
                // 通知玩家游戏开始
                this.io.to(roomName).emit(socket_1.SocketEvents.NOTIFICATION, {
                    type: 'success',
                    title: '游戏开始',
                    message: `游戏开始！${seatedPlayers}名玩家参与`,
                    duration: 3000
                });
                // 尝试开始游戏
                const gameStarted = await this.gameEngine.tryStartGame(gameType);
                if (gameStarted) {
                    console.log(`✅ 游戏 ${gameType} 自动开始成功`);
                }
                else {
                    console.log(`❌ 游戏 ${gameType} 自动开始失败`);
                }
            }
            else {
                console.log(`⚠️ 玩家数不足，无法开始游戏 ${gameType} (需要至少2人，当前${seatedPlayers}人)`);
                // 通知玩家人数不足
                this.io.to(roomName).emit(socket_1.SocketEvents.NOTIFICATION, {
                    type: 'warning',
                    title: '人数不足',
                    message: '至少需要2名玩家才能开始游戏',
                    duration: 3000
                });
            }
        }
        catch (error) {
            console.error('自动开始游戏失败:', error);
        }
    }
    /**
     * 处理开牌
     */
    async handleShowCards(socket) {
        try {
            if (!socket.currentRoom || !socket.userId) {
                (0, auth_1.sendError)(socket, 'NOT_IN_ROOM', 'You must join a room first');
                return;
            }
            const gameType = socket.currentRoom.replace('room:', '');
            const room = await roomService_1.RoomService.getGameRoom(gameType);
            // 检查游戏状态
            if (room.status !== 'playing') {
                (0, auth_1.sendError)(socket, 'INVALID_GAME_STATE', 'Not in playing phase');
                return;
            }
            // 找到用户的座位
            const seatIndex = room.seats.findIndex((s) => s.userId === socket.userId);
            if (seatIndex === -1) {
                (0, auth_1.sendError)(socket, 'NOT_SEATED', 'You must be seated to show cards');
                return;
            }
            const seat = room.seats[seatIndex];
            if (!seat.cards || seat.cards.length === 0) {
                (0, auth_1.sendError)(socket, 'NO_CARDS', 'You have no cards to show');
                return;
            }
            // 检查是否已经开牌
            if (seat.cardsShown) {
                (0, auth_1.sendError)(socket, 'ALREADY_SHOWN', 'Cards already shown');
                return;
            }
            // 标记用户已开牌
            room.seats[seatIndex].cardsShown = true;
            await roomService_1.RoomService.updateGameRoom(gameType, {
                seats: room.seats
            });
            // 广播开牌事件（显示该玩家的牌给所有人）
            this.io.to(socket.currentRoom).emit(socket_1.SocketEvents.SHOW_CARDS, {
                userId: socket.userId,
                username: socket.username,
                position: seat.position,
                cards: seat.cards,
                gameType
            });
            // 发送开牌统计到聊天
            console.log('🎴 准备发送开牌统计到聊天');
            this.sendCardStatistics(socket.currentRoom, socket.username, seat.position, seat.cards, gameType);
            // 广播房间状态更新
            const roomState = await roomService_1.RoomService.getRoomStateUpdate(gameType);
            this.io.to(socket.currentRoom).emit(socket_1.SocketEvents.ROOM_UPDATE, roomState);
            console.log(`✅ 用户 ${socket.username} 开牌`);
            // 检查是否所有玩家都已开牌
            const seatedPlayers = room.seats.filter((seat) => seat.userId);
            const shownPlayers = seatedPlayers.filter((seat) => seat.cardsShown);
            console.log(`🎯 开牌状态检查:`);
            console.log(`   - 坐下玩家数: ${seatedPlayers.length}`);
            console.log(`   - 已开牌玩家数: ${shownPlayers.length}`);
            console.log(`   - 坐下玩家:`, seatedPlayers.map((p) => ({ userId: p.userId, cardsShown: p.cardsShown })));
            // 第一个玩家开牌时就清除自动结算计时器，改为由开牌逻辑控制
            if (shownPlayers.length === 1) {
                console.log('🎯 第一个玩家开牌，清除自动结算计时器');
                this.gameEngine.clearTimer(`game:${gameType}`);
            }
            if (shownPlayers.length === seatedPlayers.length) {
                console.log('🎯 所有玩家已开牌，开始结算');
                setTimeout(() => {
                    this.gameEngine.triggerGameEnd(gameType);
                }, 2000); // 2秒后开始结算
            }
            else {
                console.log(`⏳ 等待其他玩家开牌 (${shownPlayers.length}/${seatedPlayers.length})`);
            }
        }
        catch (error) {
            console.error('开牌失败:', error);
            (0, auth_1.sendError)(socket, 'SHOW_CARDS_FAILED', error.message);
        }
    }
    /**
     * 发送开牌统计到聊天
     */
    sendCardStatistics(roomName, username, position, cards, gameType) {
        console.log('🎴 开始发送开牌统计:', { roomName, username, position, cards: cards.length });
        try {
            // 计算牌型和点数
            const handResult = this.calculateHandResult(cards, gameType);
            // 构建牌面描述
            const cardDescriptions = cards.map(card => {
                const suitSymbols = { hearts: '♥', diamonds: '♦', clubs: '♣', spades: '♠' };
                const suit = suitSymbols[card.suit] || card.suit;
                return `${card.rank}${suit}`;
            }).join(' ');
            // 发送统计消息到聊天
            const statisticsMessage = {
                userId: 'system',
                username: '系统',
                userType: 'system',
                message: `🎴 ${position}号位 ${username} 开牌：${cardDescriptions} - ${handResult.description}`,
                type: 'text',
                timestamp: new Date(),
                isSystem: true,
                messageType: 'info'
            };
            this.io.to(roomName).emit(socket_1.SocketEvents.CHAT_MESSAGE, statisticsMessage);
        }
        catch (error) {
            console.error('发送开牌统计失败:', error);
        }
    }
    /**
     * 计算手牌结果
     */
    calculateHandResult(cards, gameType) {
        if (gameType === 'sangong') {
            // 使用三公游戏的牌型判断逻辑
            const { SangongGame } = require('@games/shared');
            const hand = SangongGame.evaluateHand(cards);
            return {
                description: hand.description,
                points: hand.points
            };
        }
        // 默认返回
        return { description: '未知牌型' };
    }
}
exports.GameHandler = GameHandler;
