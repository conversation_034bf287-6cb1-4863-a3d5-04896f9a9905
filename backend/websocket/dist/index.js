"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = require("./app");
const database_1 = require("./config/database");
const PORT = process.env.PORT || 3002;
async function startServer() {
    try {
        // 初始化应用
        const initialized = await (0, app_1.initializeApp)();
        if (!initialized) {
            process.exit(1);
        }
        // 启动服务器
        app_1.server.listen(PORT, () => {
            console.log(`🚀 WebSocket服务器启动成功`);
            console.log(`📡 服务地址: http://localhost:${PORT}`);
            console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
            console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
        });
        // 优雅关闭处理
        const gracefulShutdown = async (signal) => {
            console.log(`\n📡 收到 ${signal} 信号，开始优雅关闭...`);
            app_1.server.close(async () => {
                console.log('🔄 WebSocket服务器已关闭');
                try {
                    await (0, database_1.closeConnections)();
                    console.log('✅ 优雅关闭完成');
                    process.exit(0);
                }
                catch (error) {
                    console.error('❌ 关闭过程中出错:', error);
                    process.exit(1);
                }
            });
            // 强制关闭超时
            setTimeout(() => {
                console.error('❌ 强制关闭超时，立即退出');
                process.exit(1);
            }, 10000);
        };
        // 监听关闭信号
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        // 监听未捕获的异常
        process.on('uncaughtException', (error) => {
            console.error('❌ 未捕获的异常:', error);
            gracefulShutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            console.error('❌ 未处理的Promise拒绝:', reason);
            gracefulShutdown('unhandledRejection');
        });
    }
    catch (error) {
        console.error('❌ WebSocket服务器启动失败:', error);
        process.exit(1);
    }
}
// 启动服务器
startServer();
