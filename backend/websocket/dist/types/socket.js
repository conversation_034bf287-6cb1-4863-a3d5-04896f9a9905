"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketEvents = void 0;
// WebSocket消息类型
var SocketEvents;
(function (SocketEvents) {
    // 连接相关
    SocketEvents["CONNECTION"] = "connection";
    SocketEvents["DISCONNECT"] = "disconnect";
    SocketEvents["AUTHENTICATE"] = "authenticate";
    // 房间相关
    SocketEvents["JOIN_ROOM"] = "join_room";
    SocketEvents["LEAVE_ROOM"] = "leave_room";
    SocketEvents["ROOM_UPDATE"] = "room_update";
    SocketEvents["ROOM_PLAYERS"] = "room_players";
    // 游戏相关
    SocketEvents["SIT_DOWN"] = "sit_down";
    SocketEvents["STAND_UP"] = "stand_up";
    SocketEvents["READY"] = "ready";
    SocketEvents["PLACE_BET"] = "place_bet";
    SocketEvents["PLACE_SIDE_WAGER"] = "place_side_wager";
    // 游戏流程
    SocketEvents["GAME_START"] = "game_start";
    SocketEvents["DEAL_CARDS"] = "deal_cards";
    SocketEvents["SHOW_CARDS"] = "show_cards";
    SocketEvents["GAME_END"] = "game_end";
    SocketEvents["ROUND_RESULT"] = "round_result";
    // 聊天相关
    SocketEvents["CHAT_MESSAGE"] = "chat_message";
    // 错误相关
    SocketEvents["ERROR"] = "error";
    // 系统消息
    SocketEvents["NOTIFICATION"] = "notification";
    // 余额更新
    SocketEvents["BALANCE_UPDATE"] = "balance_update";
})(SocketEvents || (exports.SocketEvents = SocketEvents = {}));
