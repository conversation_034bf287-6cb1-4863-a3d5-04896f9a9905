"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.redisClient = exports.pool = exports.redisConfig = exports.dbConfig = void 0;
exports.initRedis = initRedis;
exports.testDatabaseConnection = testDatabaseConnection;
exports.closeConnections = closeConnections;
const promise_1 = __importDefault(require("mysql2/promise"));
const redis_1 = require("redis");
// MySQL数据库配置
exports.dbConfig = {
    host: process.env.DB_HOST || '127.0.0.1',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'root',
    database: process.env.DB_NAME || 'games',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
};
// Redis配置
exports.redisConfig = {
    host: process.env.REDIS_HOST || '127.0.0.1',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || undefined
};
// 创建MySQL连接池
exports.pool = promise_1.default.createPool(exports.dbConfig);
// 创建Redis客户端
exports.redisClient = (0, redis_1.createClient)({
    socket: {
        host: exports.redisConfig.host,
        port: exports.redisConfig.port
    },
    password: exports.redisConfig.password
});
// Redis连接错误处理
exports.redisClient.on('error', (err) => {
    console.error('Redis连接错误:', err);
});
exports.redisClient.on('connect', () => {
    console.log('✅ Redis连接成功');
});
// 初始化Redis连接
async function initRedis() {
    try {
        await exports.redisClient.connect();
        console.log('✅ Redis初始化完成');
    }
    catch (error) {
        console.error('❌ Redis初始化失败:', error);
        throw error;
    }
}
// 测试数据库连接
async function testDatabaseConnection() {
    try {
        const connection = await exports.pool.getConnection();
        await connection.execute('SELECT 1');
        connection.release();
        console.log('✅ MySQL数据库连接成功');
        return true;
    }
    catch (error) {
        console.error('❌ MySQL数据库连接失败:', error);
        return false;
    }
}
// 关闭所有连接
async function closeConnections() {
    try {
        await exports.pool.end();
        await exports.redisClient.quit();
        console.log('✅ 数据库连接已关闭');
    }
    catch (error) {
        console.error('❌ 关闭数据库连接时出错:', error);
    }
}
