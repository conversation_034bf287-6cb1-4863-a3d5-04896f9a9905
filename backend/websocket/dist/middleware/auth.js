"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateSocket = authenticateSocket;
exports.requireUserType = requireUserType;
exports.sendError = sendError;
exports.validateGamePermission = validateGamePermission;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const database_1 = require("../config/database");
const socket_1 = require("../types/socket");
/**
 * WebSocket认证中间件
 */
async function authenticateSocket(socket, next) {
    try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        if (!token) {
            const error = new Error('Authentication token required');
            error.data = { code: 'AUTH_REQUIRED', message: 'Authentication token required' };
            return next(error);
        }
        // 验证JWT令牌
        const jwtSecret = process.env.JWT_SECRET || 'games-platform-super-secret-jwt-key-2024';
        const decoded = jsonwebtoken_1.default.verify(token, jwtSecret);
        // 检查会话是否有效
        const [sessions] = await database_1.pool.execute('SELECT id, expires_at FROM user_sessions WHERE id = ? AND user_id = ? AND expires_at > NOW()', [decoded.sessionId, decoded.userId]);
        if (!Array.isArray(sessions) || sessions.length === 0) {
            const error = new Error('Session expired or invalid');
            error.data = { code: 'SESSION_EXPIRED', message: 'Session expired or invalid' };
            return next(error);
        }
        // 设置socket用户信息
        socket.userId = decoded.userId;
        socket.userType = decoded.userType;
        socket.username = decoded.username;
        console.log(`✅ 用户认证成功: ${decoded.username} (${decoded.userType})`);
        next();
    }
    catch (error) {
        console.error('❌ Socket认证失败:', error);
        const authError = new Error('Invalid token');
        authError.data = { code: 'INVALID_TOKEN', message: 'Invalid authentication token' };
        next(authError);
    }
}
/**
 * 检查用户权限
 */
function requireUserType(...allowedTypes) {
    return (socket, next) => {
        if (!socket.userType || !allowedTypes.includes(socket.userType)) {
            const error = new Error('Insufficient permissions');
            error.data = { code: 'INSUFFICIENT_PERMISSIONS', message: 'Insufficient permissions' };
            return next(error);
        }
        next();
    };
}
/**
 * 发送错误消息到客户端
 */
function sendError(socket, code, message, details) {
    const errorResponse = {
        code,
        message,
        details
    };
    socket.emit(socket_1.SocketEvents.ERROR, errorResponse);
}
/**
 * 验证用户是否可以进行游戏操作
 */
async function validateGamePermission(socket) {
    if (!socket.userId || socket.userType !== 'user') {
        sendError(socket, 'INVALID_USER_TYPE', 'Only regular users can play games');
        return false;
    }
    try {
        // 检查用户是否属于某个俱乐部
        const [users] = await database_1.pool.execute('SELECT agent_id, club_id, balance, status FROM users WHERE id = ?', [socket.userId]);
        if (!Array.isArray(users) || users.length === 0) {
            sendError(socket, 'USER_NOT_FOUND', 'User not found');
            return false;
        }
        const user = users[0];
        if (user.status !== 'active') {
            sendError(socket, 'USER_INACTIVE', 'User account is inactive');
            return false;
        }
        if (!user.club_id) {
            sendError(socket, 'NO_CLUB', 'User must join a club to play games');
            return false;
        }
        return true;
    }
    catch (error) {
        console.error('验证游戏权限时出错:', error);
        sendError(socket, 'VALIDATION_ERROR', 'Failed to validate game permission');
        return false;
    }
}
