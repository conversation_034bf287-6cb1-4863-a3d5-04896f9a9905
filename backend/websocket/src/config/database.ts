import mysql from 'mysql2/promise';
import { createClient } from 'redis';

// MySQL数据库配置
export const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'games',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// Redis配置
export const redisConfig = {
  host: process.env.REDIS_HOST || '127.0.0.1',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined
};

// 创建MySQL连接池
export const pool = mysql.createPool(dbConfig);

// 创建Redis客户端
export const redisClient = createClient({
  socket: {
    host: redisConfig.host,
    port: redisConfig.port
  },
  password: redisConfig.password
});

// Redis连接错误处理
redisClient.on('error', (err) => {
  console.error('Redis连接错误:', err);
});

redisClient.on('connect', () => {
  console.log('✅ Redis连接成功');
});

// 初始化Redis连接
export async function initRedis() {
  try {
    await redisClient.connect();
    console.log('✅ Redis初始化完成');
  } catch (error) {
    console.error('❌ Redis初始化失败:', error);
    throw error;
  }
}

// 测试数据库连接
export async function testDatabaseConnection() {
  try {
    const connection = await pool.getConnection();
    await connection.execute('SELECT 1');
    connection.release();
    console.log('✅ MySQL数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ MySQL数据库连接失败:', error);
    return false;
  }
}

// 关闭所有连接
export async function closeConnections() {
  try {
    await pool.end();
    await redisClient.quit();
    console.log('✅ 数据库连接已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接时出错:', error);
  }
}
