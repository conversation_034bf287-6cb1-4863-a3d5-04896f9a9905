import { Socket } from 'socket.io';
import { GameType, GameSeat, SideWager } from '@games/shared';

// 扩展Socket接口以包含用户信息
export interface AuthenticatedSocket extends Socket {
  userId?: string;
  userType?: 'user' | 'agent' | 'admin';
  username?: string;
  currentRoom?: string;
}

// WebSocket消息类型
export enum SocketEvents {
  // 连接相关
  CONNECTION = 'connection',
  DISCONNECT = 'disconnect',
  AUTHENTICATE = 'authenticate',
  
  // 房间相关
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_UPDATE = 'room_update',
  ROOM_PLAYERS = 'room_players',
  
  // 游戏相关
  SIT_DOWN = 'sit_down',
  STAND_UP = 'stand_up',
  READY = 'ready',
  PLACE_BET = 'place_bet',
  PLACE_SIDE_WAGER = 'place_side_wager',
  
  // 游戏流程
  GAME_START = 'game_start',
  DEAL_CARDS = 'deal_cards',
  SHOW_CARDS = 'show_cards',
  GAME_END = 'game_end',
  ROUND_RESULT = 'round_result',
  
  // 聊天相关
  CHAT_MESSAGE = 'chat_message',
  
  // 错误相关
  ERROR = 'error',
  
  // 系统消息
  NOTIFICATION = 'notification',

  // 余额更新
  BALANCE_UPDATE = 'balance_update'
}

// 消息数据结构
export interface SocketMessage<T = any> {
  event: SocketEvents;
  data: T;
  timestamp: Date;
  userId?: string;
}

// 加入房间请求
export interface JoinRoomRequest {
  gameType: GameType;
}

// 坐下请求
export interface SitDownRequest {
  seatNumber: number; // 1-10
}

// 下注请求
export interface PlaceBetRequest {
  amount: number;
}

// 旁注请求
export interface PlaceSideWagerRequest {
  amount: number;
  targetSeat: number; // 押注的座位号
}

// 聊天消息
export interface ChatMessage {
  message: string;
  type?: 'text' | 'emoji';
}

// 房间状态更新
export interface RoomStateUpdate {
  roomId: string;
  gameType: GameType;
  status: string;
  seats: GameSeat[];
  spectators: string[];
  currentRound: number;
  minBet: number;
  maxBet: number;
  playerCount: number;
}

// 游戏结果
export interface GameResult {
  winners: string[];
  payouts: { [userId: string]: number };
  roundNumber: number;
}

// 错误响应
export interface ErrorResponse {
  code: string;
  message: string;
  details?: any;
}

// 通知消息
export interface NotificationMessage {
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  duration?: number; // 显示时长（毫秒）
}
