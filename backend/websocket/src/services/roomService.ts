import { pool, redisClient } from '../config/database';
import { GameType, GameSeat, SideWager, GAME_CONFIG } from '@games/shared';
import { RoomStateUpdate } from '../types/socket';

export class RoomService {
  /**
   * 获取游戏房间信息
   */
  static async getGameRoom(gameType: GameType): Promise<any> {
    try {
      const [rooms] = await pool.execute(
        'SELECT * FROM game_rooms WHERE game_type = ?',
        [gameType]
      );
      
      if (!Array.isArray(rooms) || rooms.length === 0) {
        throw new Error(`Game room for ${gameType} not found`);
      }
      
      const room = rooms[0] as any;
      
      // JSON字段已经被MySQL驱动自动解析，无需再次解析
      room.seats = room.seats || [];
      room.spectators = room.spectators || [];
      room.game_state = room.game_state || {};
      
      return room;
    } catch (error) {
      console.error('获取游戏房间失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新游戏房间状态
   */
  static async updateGameRoom(gameType: GameType, updates: any): Promise<void> {
    try {
      const setClause = [];
      const values = [];
      
      for (const [key, value] of Object.entries(updates)) {
        if (key === 'seats' || key === 'spectators') {
          setClause.push(`${key} = ?`);
          values.push(JSON.stringify(value));
        } else {
          setClause.push(`${key} = ?`);
          values.push(value);
        }
      }
      
      values.push(gameType);
      
      await pool.execute(
        `UPDATE game_rooms SET ${setClause.join(', ')}, updated_at = NOW() WHERE game_type = ?`,
        values
      );
      
      // 缓存到Redis
      const cacheKey = `room:${gameType}`;
      const room = await this.getGameRoom(gameType);
      await redisClient.setEx(cacheKey, 300, JSON.stringify(room)); // 缓存5分钟
      
    } catch (error) {
      console.error('更新游戏房间失败:', error);
      throw error;
    }
  }
  
  /**
   * 用户坐下
   */
  static async sitDown(gameType: GameType, userId: string, username: string, seatNumber: number): Promise<boolean> {
    try {
      const room = await this.getGameRoom(gameType);
      
      // 检查座位是否有效
      if (seatNumber < 1 || seatNumber > GAME_CONFIG.MAX_SEATS) {
        throw new Error('Invalid seat number');
      }
      
      // 检查座位是否已被占用
      const seat = room.seats.find((s: GameSeat) => s.position === seatNumber);
      if (seat && seat.userId) {
        throw new Error('Seat is already occupied');
      }
      
      // 检查用户是否已经坐在其他位置
      const existingSeat = room.seats.find((s: GameSeat) => s.userId === userId);
      if (existingSeat) {
        throw new Error('User is already seated');
      }
      
      // 获取用户余额
      const [users] = await pool.execute(
        'SELECT balance FROM users WHERE id = ?',
        [userId]
      );
      
      if (!Array.isArray(users) || users.length === 0) {
        throw new Error('User not found');
      }
      
      const userBalance = (users[0] as any).balance;
      
      // 更新座位信息
      const seatIndex = room.seats.findIndex((s: GameSeat) => s.position === seatNumber);
      if (seatIndex !== -1) {
        room.seats[seatIndex] = {
          position: seatNumber,
          userId,
          username,
          balance: userBalance,
          bet: 0,
          cards: [],
          isReady: false,
          cardsShown: false,
          sideWagers: []
        };
      }
      
      // 从旁观者列表中移除（如果存在）
      room.spectators = room.spectators.filter((id: string) => id !== userId);
      
      await this.updateGameRoom(gameType, {
        seats: room.seats,
        spectators: room.spectators
      });
      
      return true;
    } catch (error) {
      console.error('用户坐下失败:', error);
      throw error;
    }
  }
  
  /**
   * 用户站起
   */
  static async standUp(gameType: GameType, userId: string): Promise<boolean> {
    try {
      const room = await this.getGameRoom(gameType);
      
      // 找到用户的座位
      const seatIndex = room.seats.findIndex((s: GameSeat) => s.userId === userId);
      if (seatIndex === -1) {
        throw new Error('User is not seated');
      }
      
      // 检查游戏状态，如果游戏进行中不能站起
      if (room.status === 'playing' || room.status === 'dealing') {
        throw new Error('Cannot stand up during game');
      }
      
      // 清空座位
      room.seats[seatIndex] = {
        position: room.seats[seatIndex].position,
        userId: null,
        username: null,
        balance: 0,
        bet: 0,
        cards: [],
        isReady: false,
        cardsShown: false,
        sideWagers: []
      };
      
      // 添加到旁观者列表
      if (!room.spectators.includes(userId)) {
        room.spectators.push(userId);
      }
      
      await this.updateGameRoom(gameType, {
        seats: room.seats,
        spectators: room.spectators
      });
      
      return true;
    } catch (error) {
      console.error('用户站起失败:', error);
      throw error;
    }
  }
  
  /**
   * 加入房间作为旁观者
   */
  static async joinAsSpectator(gameType: GameType, userId: string): Promise<boolean> {
    try {
      const room = await this.getGameRoom(gameType);
      
      // 检查用户是否已经在座位上
      const isSeated = room.seats.some((s: GameSeat) => s.userId === userId);
      if (isSeated) {
        return true; // 已经在座位上，不需要加入旁观者
      }
      
      // 检查是否已经是旁观者
      if (room.spectators.includes(userId)) {
        return true; // 已经是旁观者
      }
      
      // 检查旁观者数量限制
      if (room.spectators.length >= GAME_CONFIG.MAX_SPECTATORS) {
        throw new Error('Too many spectators');
      }
      
      // 添加到旁观者列表
      room.spectators.push(userId);
      
      await this.updateGameRoom(gameType, {
        spectators: room.spectators
      });
      
      return true;
    } catch (error) {
      console.error('加入旁观者失败:', error);
      throw error;
    }
  }
  
  /**
   * 离开房间
   */
  static async leaveRoom(gameType: GameType, userId: string): Promise<boolean> {
    try {
      const room = await this.getGameRoom(gameType);
      
      // 检查用户是否在座位上
      const seatIndex = room.seats.findIndex((s: GameSeat) => s.userId === userId);
      if (seatIndex !== -1) {
        // 如果游戏进行中，不能离开
        if (room.status === 'playing' || room.status === 'dealing') {
          throw new Error('Cannot leave during game');
        }
        
        // 清空座位
        room.seats[seatIndex] = {
          position: room.seats[seatIndex].position,
          userId: null,
          username: null,
          balance: 0,
          bet: 0,
          cards: [],
          isReady: false,
          sideWagers: []
        };
      }
      
      // 从旁观者列表中移除
      room.spectators = room.spectators.filter((id: string) => id !== userId);
      
      await this.updateGameRoom(gameType, {
        seats: room.seats,
        spectators: room.spectators
      });
      
      return true;
    } catch (error) {
      console.error('离开房间失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取房间状态更新数据
   */
  static async getRoomStateUpdate(gameType: GameType): Promise<RoomStateUpdate> {
    const room = await this.getGameRoom(gameType);

    // 计算玩家数量
    const playerCount = room.seats.filter((seat: any) => seat.userId).length;

    return {
      roomId: room.id,
      gameType: room.game_type,
      status: room.status,
      seats: room.seats,
      spectators: room.spectators,
      currentRound: room.current_round,
      minBet: room.min_bet,
      maxBet: room.max_bet,
      playerCount
    };
  }
  
  /**
   * 获取房间内的所有用户ID
   */
  static async getRoomUserIds(gameType: GameType): Promise<string[]> {
    const room = await this.getGameRoom(gameType);
    const userIds: string[] = [];
    
    // 添加座位上的用户
    room.seats.forEach((seat: GameSeat) => {
      if (seat.userId) {
        userIds.push(seat.userId);
      }
    });
    
    // 添加旁观者
    userIds.push(...room.spectators);
    
    return [...new Set(userIds)]; // 去重
  }
}
