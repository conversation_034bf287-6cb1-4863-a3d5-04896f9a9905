import { Server } from 'socket.io';
import { pool } from '../config/database';
import { RoomService } from './roomService';
import { GameType, GameStatus, GAME_CONFIG, generateId } from '@games/shared';
import { GameFactory, GameStateManager, GameTimer } from '@games/shared';
import { SocketEvents, GameResult } from '../types/socket';

export class GameEngine {
  private io: Server;
  private gameTimers: GameTimer;
  
  constructor(io: Server) {
    this.io = io;
    this.gameTimers = new GameTimer();
  }
  
  /**
   * 尝试开始游戏
   */
  async tryStartGame(gameType: GameType): Promise<boolean> {
    try {
      const room = await RoomService.getGameRoom(gameType);
      const roomName = `room:${gameType}`;

      // 检查游戏状态
      if (room.status !== 'waiting') {
        return false;
      }

      // 获取坐下的玩家（不需要准备好或下注）
      const seatedPlayers = GameStateManager.getSeatedPlayers(room.seats);

      // 检查是否有足够的玩家
      if (seatedPlayers.length < GAME_CONFIG.MIN_PLAYERS) {
        return false;
      }

      console.log(`🎮 开始 ${gameType} 游戏，玩家数量: ${seatedPlayers.length}`);

      // 开始下注阶段
      await this.startBettingPhase(gameType);

      return true;
    } catch (error) {
      console.error('尝试开始游戏失败:', error);
      return false;
    }
  }
  
  /**
   * 开始下注阶段
   */
  private async startBettingPhase(gameType: GameType): Promise<void> {
    const roomName = `room:${gameType}`;
    
    // 更新游戏状态
    await RoomService.updateGameRoom(gameType, {
      status: 'betting'
    });
    
    // 广播游戏开始
    this.io.to(roomName).emit(SocketEvents.GAME_START, {
      gameType,
      phase: 'betting',
      timeLimit: GAME_CONFIG.BETTING_TIME
    });
    
    // 设置下注倒计时
    this.gameTimers.setTimer(`betting:${gameType}`, async () => {
      await this.endBettingPhase(gameType);
    }, GAME_CONFIG.BETTING_TIME);
  }
  
  /**
   * 结束下注阶段
   */
  async endBettingPhase(gameType: GameType): Promise<void> {
    const roomName = `room:${gameType}`;

    try {
      // 检查当前玩家数量
      const room = await RoomService.getGameRoom(gameType);
      const seatedPlayers = GameStateManager.getSeatedPlayers(room.seats);

      console.log(`🎯 下注阶段结束检查:`);
      console.log(`   - 当前坐下玩家数: ${seatedPlayers.length}`);

      if (seatedPlayers.length < 2) {
        console.log('❌ 玩家数量不足，重置游戏状态');

        // 重置游戏状态
        await RoomService.updateGameRoom(gameType, {
          status: 'waiting'
        });

        // 广播游戏取消
        this.io.to(roomName).emit(SocketEvents.NOTIFICATION, {
          type: 'warning',
          title: '游戏取消',
          message: '玩家数量不足，游戏已取消',
          duration: 5000
        });

        return;
      }

      // 更新游戏状态
      await RoomService.updateGameRoom(gameType, {
        status: 'dealing'
      });

      // 广播下注结束
      this.io.to(roomName).emit(SocketEvents.NOTIFICATION, {
        type: 'info',
        title: '下注结束',
        message: '开始发牌...',
        duration: 3000
      });

      // 开始发牌
      await this.startDealingPhase(gameType);
    } catch (error) {
      console.error('结束下注阶段失败:', error);
    }
  }
  
  /**
   * 开始发牌阶段
   */
  private async startDealingPhase(gameType: GameType): Promise<void> {
    const roomName = `room:${gameType}`;
    
    try {
      const room = await RoomService.getGameRoom(gameType);
      const seatedPlayers = GameStateManager.getSeatedPlayers(room.seats);

      console.log(`🃏 发牌调试信息:`);
      console.log(`   - 房间状态: ${room.status}`);
      console.log(`   - 总座位数: ${room.seats.length}`);
      console.log(`   - 坐下玩家数: ${seatedPlayers.length}`);
      console.log(`   - 坐下玩家:`, seatedPlayers.map(p => ({ userId: p.userId, position: p.position })));

      // 发牌
      const { playerCards } = GameFactory.dealCards(gameType, seatedPlayers.length);
      
      // 更新座位信息
      const updatedSeats = room.seats.map((seat: any) => {
        if (seat.userId) {
          const playerIndex = seatedPlayers.findIndex(p => p.userId === seat.userId);
          if (playerIndex !== -1) {
            return {
              ...seat,
              cards: playerCards[playerIndex]
            };
          }
        }
        return seat;
      });
      
      await RoomService.updateGameRoom(gameType, {
        seats: updatedSeats,
        status: 'playing'
      });
      
      // 广播发牌（每个玩家只能看到自己的牌）
      seatedPlayers.forEach((player, index) => {
        const playerSockets = this.io.sockets.sockets;
        for (const [socketId, socket] of playerSockets) {
          if ((socket as any).userId === player.userId) {
            socket.emit(SocketEvents.DEAL_CARDS, {
              cards: playerCards[index],
              gameType
            });
            break;
          }
        }
      });
      
      // 广播游戏状态更新（不包含牌面信息）
      const roomState = await RoomService.getRoomStateUpdate(gameType);
      this.io.to(roomName).emit(SocketEvents.ROOM_UPDATE, {
        ...roomState,
        seats: roomState.seats.map(seat => ({
          ...seat,
          cards: seat.userId ? Array(seat.cards.length).fill({ hidden: true }) : []
        }))
      });
      
      // 设置游戏结算倒计时
      this.gameTimers.setTimer(`game:${gameType}`, async () => {
        await this.endGame(gameType);
      }, GAME_CONFIG.DEALING_TIME + 5000); // 发牌动画时间 + 5秒查看时间
      
    } catch (error) {
      console.error('发牌阶段失败:', error);
    }
  }
  
  /**
   * 结束游戏
   */
  private async endGame(gameType: GameType): Promise<void> {
    const roomName = `room:${gameType}`;
    
    try {
      const room = await RoomService.getGameRoom(gameType);
      const seatedPlayers = GameStateManager.getSeatedPlayers(room.seats);

      // 准备游戏结果数据 - 只包含有牌的玩家
      const gameData = seatedPlayers
        .filter(player => player.cards && player.cards.length > 0)
        .map(player => ({
          userId: player.userId!,
          cards: player.cards,
          bet: player.bet,
          sideWagers: player.sideWagers || []
        }));
      
      // 计算游戏结果
      const gameResult = GameFactory.calculateGameResult(gameType, gameData);

      console.log('🎯 游戏结算详情:');
      console.log('   - 游戏数据:', JSON.stringify(gameData, null, 2));
      console.log('   - 结算结果:', JSON.stringify(gameResult, null, 2));

      // 更新数据库中的用户余额
      const updatedBalances = await this.updatePlayerBalances(gameResult);

      // 发送余额更新事件给相关玩家
      this.broadcastBalanceUpdates(updatedBalances);

      // 发送结算统计到聊天
      console.log('🎯 准备发送结算统计到聊天');
      this.sendGameResultStatistics(roomName, gameResult, seatedPlayers);

      // 保存游戏记录
      await this.saveGameRecord(gameType, room.id, gameResult);
      
      // 更新房间状态
      await RoomService.updateGameRoom(gameType, {
        status: 'settling',
        current_round: room.current_round + 1
      });
      
      // 广播游戏结果
      this.io.to(roomName).emit(SocketEvents.GAME_END, {
        gameType,
        result: gameResult,
        roundNumber: room.current_round + 1
      });
      
      // 显示结果一段时间后重置房间
      this.gameTimers.setTimer(`settle:${gameType}`, async () => {
        await this.resetRoom(gameType);
      }, GAME_CONFIG.SETTLING_TIME);
      
    } catch (error) {
      console.error('结束游戏失败:', error);
    }
  }
  
  /**
   * 更新玩家余额
   */
  private async updatePlayerBalances(gameResult: any): Promise<Array<{userId: string, newBalance: number}>> {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      console.log('💰 开始更新玩家余额:');
      const updatedBalances: Array<{userId: string, newBalance: number}> = [];

      for (const player of gameResult.players) {
        console.log(`   - 玩家 ${player.userId}: ${player.isWinner ? '获胜' : '失败'}, 金额变化: ${player.winAmount}`);

        // 获取更新前的余额
        const [beforeRows] = await connection.execute(
          'SELECT balance FROM users WHERE id = ?',
          [player.userId]
        ) as any;

        const balanceBefore = beforeRows.length > 0 ? parseFloat(beforeRows[0].balance) : 0;
        console.log(`     更新前余额: ${balanceBefore}`);

        // 更新用户余额
        const [updateResult] = await connection.execute(
          'UPDATE users SET balance = balance + ? WHERE id = ?',
          [player.winAmount, player.userId]
        ) as any;

        console.log(`     数据库更新结果: 影响行数 ${updateResult.affectedRows}`);

        // 获取更新后的余额
        const [afterRows] = await connection.execute(
          'SELECT balance FROM users WHERE id = ?',
          [player.userId]
        ) as any;

        const balanceAfter = afterRows.length > 0 ? parseFloat(afterRows[0].balance) : 0;
        console.log(`     更新后余额: ${balanceAfter}`);

        // 收集更新后的余额
        updatedBalances.push({
          userId: player.userId,
          newBalance: balanceAfter
        });

        // 记录交易
        const transactionId = generateId();
        const transactionType = player.winAmount > 0 ? 'win' : 'bet';

        await connection.execute(
          `INSERT INTO transactions (id, user_id, type, amount, balance_before, balance_after, description)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            transactionId,
            player.userId,
            transactionType,
            Math.abs(player.winAmount),
            balanceBefore,
            balanceAfter,
            `游戏${player.isWinner ? '获胜' : '失败'}`
          ]
        );
      }

      await connection.commit();
      return updatedBalances;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 广播余额更新给相关玩家
   */
  private broadcastBalanceUpdates(updatedBalances: Array<{userId: string, newBalance: number}>): void {
    console.log('📢 广播余额更新:');

    for (const balanceUpdate of updatedBalances) {
      console.log(`   - 用户 ${balanceUpdate.userId}: 新余额 ${balanceUpdate.newBalance}`);

      // 找到该用户的所有连接的socket
      const playerSockets = this.io.sockets.sockets;
      for (const [socketId, socket] of playerSockets) {
        if ((socket as any).userId === balanceUpdate.userId) {
          socket.emit(SocketEvents.BALANCE_UPDATE, {
            newBalance: balanceUpdate.newBalance
          });
        }
      }
    }
  }

  /**
   * 保存游戏记录
   */
  private async saveGameRecord(gameType: GameType, roomId: string, gameResult: any): Promise<void> {
    const recordId = generateId();

    // 先尝试插入，如果失败则跳过（避免阻塞游戏流程）
    try {
      await pool.execute(
        `INSERT INTO game_records (id, room_id, game_type, round_number, players, game_result, total_pot)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          recordId,
          roomId,
          gameType,
          1, // 简化处理
          JSON.stringify(gameResult.players),
          JSON.stringify(gameResult),
          gameResult.totalPot
        ]
      );
    } catch (error) {
      console.warn('保存游戏记录失败，但游戏继续:', (error as Error).message);
      // 不抛出错误，让游戏继续进行
    }
  }
  
  /**
   * 重置房间
   */
  private async resetRoom(gameType: GameType): Promise<void> {
    const roomName = `room:${gameType}`;
    
    try {
      const room = await RoomService.getGameRoom(gameType);
      
      // 重置座位状态
      const resetSeats = GameStateManager.resetSeatsForNewRound(room.seats);
      
      await RoomService.updateGameRoom(gameType, {
        status: 'waiting',
        seats: resetSeats
      });
      
      // 广播房间重置
      const roomState = await RoomService.getRoomStateUpdate(gameType);
      this.io.to(roomName).emit(SocketEvents.ROOM_UPDATE, roomState);
      
      this.io.to(roomName).emit(SocketEvents.NOTIFICATION, {
        type: 'info',
        title: '新一轮游戏',
        message: '房间已重置，可以开始新的游戏',
        duration: 3000
      });
      
      console.log(`✅ ${gameType} 房间已重置`);
    } catch (error) {
      console.error('重置房间失败:', error);
    }
  }
  
  /**
   * 触发游戏结算（公共方法）
   */
  async triggerGameEnd(gameType: GameType): Promise<void> {
    await this.endGame(gameType);
  }

  /**
   * 强制结束游戏（用于异常情况）
   */
  async forceEndGame(gameType: GameType): Promise<void> {
    this.gameTimers.clearTimer(`betting:${gameType}`);
    this.gameTimers.clearTimer(`game:${gameType}`);
    this.gameTimers.clearTimer(`settle:${gameType}`);

    await this.resetRoom(gameType);
  }
  
  /**
   * 设置计时器
   */
  setTimer(key: string, callback: () => void, delay: number): void {
    this.gameTimers.setTimer(key, callback, delay);
  }

  /**
   * 清除计时器
   */
  clearTimer(key: string): void {
    this.gameTimers.clearTimer(key);
  }

  /**
   * 发送游戏结算统计到聊天
   */
  private sendGameResultStatistics(roomName: string, gameResult: any, seatedPlayers: any[]): void {
    try {
      // 发送分隔线
      this.sendSystemChatMessage(roomName, '═══════════ 🎯 本局结算 ═══════════');

      // 发送每个玩家的结果
      gameResult.players.forEach((player: any) => {
        const seatInfo = seatedPlayers.find(p => p.userId === player.userId);
        const username = seatInfo?.username || '未知玩家';
        const position = seatInfo?.position || '?';

        const resultIcon = player.isWinner ? '🏆' : '💸';
        const winLoseText = player.isWinner ? '获胜' : '失败';
        const amountText = player.winAmount > 0 ? `+${player.winAmount}` : `${player.winAmount}`;

        this.sendSystemChatMessage(
          roomName,
          `${resultIcon} ${position}号位 ${username}: ${player.hand.description} | ${winLoseText} ${amountText}`
        );
      });

      // 发送获胜者信息
      const winners = gameResult.players.filter((p: any) => p.isWinner);
      if (winners.length > 0) {
        const winnerNames = winners.map((w: any) => {
          const seatInfo = seatedPlayers.find(p => p.userId === w.userId);
          return seatInfo?.username || '未知玩家';
        }).join(', ');

        this.sendSystemChatMessage(roomName, `🎉 恭喜 ${winnerNames} 获胜！`);
      }

      // 发送总奖池信息
      this.sendSystemChatMessage(roomName, `💰 本局奖池: ${gameResult.totalPot}`);

      // 发送分隔线
      this.sendSystemChatMessage(roomName, '═══════════════════════════════════');

    } catch (error) {
      console.error('发送游戏结算统计失败:', error);
    }
  }

  /**
   * 发送系统聊天消息
   */
  private sendSystemChatMessage(roomName: string, message: string): void {
    const systemMessage = {
      userId: 'system',
      username: '系统',
      userType: 'system' as const,
      message,
      type: 'text' as const,
      timestamp: new Date(),
      isSystem: true,
      messageType: 'info' as const
    };

    this.io.to(roomName).emit(SocketEvents.CHAT_MESSAGE, systemMessage);
  }

  /**
   * 清理所有计时器
   */
  cleanup(): void {
    this.gameTimers.clearAllTimers();
  }
}
