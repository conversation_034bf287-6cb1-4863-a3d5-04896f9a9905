import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

import { testDatabaseConnection, initRedis } from './config/database';
import { authenticateSocket } from './middleware/auth';
import { GameHandler } from './handlers/gameHandler';
import { ChatHandler } from './handlers/chatHandler';
import { AuthenticatedSocket, SocketEvents } from './types/socket';

// 加载环境变量
dotenv.config();

const app = express();
const server = createServer(app);

// CORS配置
const corsOptions = {
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200
};

// Socket.IO配置
const io = new Server(server, {
  cors: corsOptions,
  transports: ['websocket', 'polling']
});

// Express中间件
app.use(helmet());
app.use(cors(corsOptions));

if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined'));
}

app.use(express.json());

// 基本路由
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '🎮 游戏平台WebSocket服务',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      connections: io.engine.clientsCount
    }
  });
});

// 创建处理器实例
const gameHandler = new GameHandler(io);
const chatHandler = new ChatHandler(io);

// Socket.IO认证中间件
io.use(authenticateSocket);

// Socket.IO连接处理
io.on(SocketEvents.CONNECTION, (socket: AuthenticatedSocket) => {
  console.log(`🔌 用户连接: ${socket.username} (${socket.userType}) - ${socket.id}`);
  
  // 发送连接成功消息
  socket.emit(SocketEvents.NOTIFICATION, {
    type: 'success',
    title: '连接成功',
    message: `欢迎 ${socket.username}！`,
    duration: 3000
  });
  
  // 游戏相关事件
  socket.on(SocketEvents.JOIN_ROOM, (data) => {
    gameHandler.handleJoinRoom(socket, data);
  });
  
  socket.on(SocketEvents.LEAVE_ROOM, () => {
    gameHandler.handleLeaveRoom(socket);
  });
  
  socket.on(SocketEvents.SIT_DOWN, (data) => {
    gameHandler.handleSitDown(socket, data);
  });
  
  socket.on(SocketEvents.STAND_UP, () => {
    gameHandler.handleStandUp(socket);
  });
  
  socket.on(SocketEvents.READY, () => {
    gameHandler.handleReady(socket);
  });
  
  socket.on(SocketEvents.PLACE_BET, (data) => {
    gameHandler.handlePlaceBet(socket, data);
  });
  
  socket.on(SocketEvents.PLACE_SIDE_WAGER, (data) => {
    gameHandler.handlePlaceSideWager(socket, data);
  });

  socket.on(SocketEvents.SHOW_CARDS, () => {
    gameHandler.handleShowCards(socket);
  });

  // 聊天相关事件
  socket.on(SocketEvents.CHAT_MESSAGE, (data) => {
    chatHandler.handleChatMessage(socket, data);
  });
  
  // 断开连接处理
  socket.on(SocketEvents.DISCONNECT, async (reason) => {
    console.log(`🔌 用户断开连接: ${socket.username} - ${reason}`);
    
    // 自动离开房间
    if (socket.currentRoom) {
      try {
        await gameHandler.handleLeaveRoom(socket);
      } catch (error) {
        console.error('自动离开房间失败:', error);
      }
    }
  });
  
  // 错误处理
  socket.on('error', (error) => {
    console.error(`Socket错误 (${socket.username}):`, error);
  });
});

// Socket.IO错误处理
io.engine.on('connection_error', (err) => {
  console.error('Socket.IO连接错误:', err);
});

// 初始化应用
export async function initializeApp() {
  try {
    console.log('🔄 初始化WebSocket应用...');
    
    // 测试数据库连接
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      throw new Error('数据库连接失败');
    }
    
    // 初始化Redis
    await initRedis();
    
    console.log('✅ WebSocket应用初始化完成');
    return true;
  } catch (error) {
    console.error('❌ WebSocket应用初始化失败:', error);
    return false;
  }
}

export { server, io };
export default app;
