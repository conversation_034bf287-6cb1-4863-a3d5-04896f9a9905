import { Server } from 'socket.io';
import { AuthenticatedSocket, SocketEvents, ChatMessage } from '../types/socket';
import { sendError } from '../middleware/auth';

export class ChatHandler {
  constructor(private io: Server) {}
  
  /**
   * 处理聊天消息
   */
  async handleChatMessage(socket: AuthenticatedSocket, data: ChatMessage) {
    try {
      if (!socket.currentRoom || !socket.userId || !socket.username) {
        sendError(socket, 'NOT_IN_ROOM', 'You must join a room first');
        return;
      }
      
      const { message, type = 'text' } = data;
      
      // 验证消息内容
      if (!message || message.trim().length === 0) {
        sendError(socket, 'EMPTY_MESSAGE', 'Message cannot be empty');
        return;
      }
      
      if (message.length > 200) {
        sendError(socket, 'MESSAGE_TOO_LONG', 'Message is too long (max 200 characters)');
        return;
      }
      
      // 简单的内容过滤
      const filteredMessage = this.filterMessage(message);
      
      // 构建聊天消息
      const chatMessage = {
        userId: socket.userId,
        username: socket.username,
        userType: socket.userType,
        message: filteredMessage,
        type,
        timestamp: new Date()
      };
      
      // 广播到房间内的所有用户
      this.io.to(socket.currentRoom).emit(SocketEvents.CHAT_MESSAGE, chatMessage);
      
      console.log(`💬 [${socket.currentRoom}] ${socket.username}: ${filteredMessage}`);
    } catch (error) {
      console.error('处理聊天消息失败:', error);
      sendError(socket, 'CHAT_FAILED', (error as Error).message);
    }
  }
  
  /**
   * 简单的消息过滤
   */
  private filterMessage(message: string): string {
    // 基本的敏感词过滤
    const sensitiveWords = [
      '赌博', '外挂', '作弊', '刷钱', '代充',
      '色情', '暴力', '政治', '反动'
    ];
    
    let filtered = message;
    sensitiveWords.forEach(word => {
      const regex = new RegExp(word, 'gi');
      filtered = filtered.replace(regex, '*'.repeat(word.length));
    });
    
    return filtered;
  }
  
  /**
   * 发送系统消息
   */
  sendSystemMessage(roomName: string, message: string, type: 'info' | 'warning' | 'error' = 'info') {
    const systemMessage = {
      userId: 'system',
      username: '系统',
      userType: 'system',
      message,
      type: 'text',
      timestamp: new Date(),
      isSystem: true,
      messageType: type
    };
    
    this.io.to(roomName).emit(SocketEvents.CHAT_MESSAGE, systemMessage);
  }
  
  /**
   * 发送游戏相关消息
   */
  sendGameMessage(roomName: string, message: string) {
    this.sendSystemMessage(roomName, `🎮 ${message}`, 'info');
  }
}
