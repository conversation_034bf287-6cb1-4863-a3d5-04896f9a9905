{"name": "@games/api", "version": "1.0.0", "description": "游戏平台API服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts"}, "keywords": ["games", "api", "express"], "author": "", "license": "ISC", "dependencies": {"@games/shared": "file:../shared", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.6.0", "node-fetch": "^3.3.2", "redis": "^4.6.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.0.0", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.0.0"}}