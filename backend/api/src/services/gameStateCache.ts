import { cacheService } from './cacheService';

interface GameRoom {
  id: string;
  gameType: 'sangong' | 'niuniu';
  status: 'waiting' | 'betting' | 'dealing' | 'playing' | 'settling' | 'finished';
  seats: GameSeat[];
  spectators: string[];
  currentRound: number;
  minBet: number;
  maxBet: number;
  createdAt: Date;
  updatedAt: Date;
}

interface GameSeat {
  position: number;
  userId: string | null;
  username: string | null;
  balance: number;
  bet: number;
  cards: any[];
  isReady: boolean;
  sideWagers: SideWager[];
}

interface SideWager {
  userId: string;
  username: string;
  amount: number;
  targetSeat: number;
}

interface OnlineUser {
  userId: string;
  username: string;
  userType: 'user' | 'agent' | 'admin';
  socketId: string;
  roomId?: string;
  lastActivity: Date;
}

class GameStateCacheService {
  private readonly ROOM_PREFIX = 'game_room:';
  private readonly USER_ROOM_PREFIX = 'user_room:';
  private readonly ONLINE_USERS_PREFIX = 'online_users';
  private readonly ROOM_USERS_PREFIX = 'room_users:';
  private readonly GAME_STATS_PREFIX = 'game_stats:';
  
  private readonly ROOM_TTL = 24 * 60 * 60; // 24小时
  private readonly USER_TTL = 30 * 60; // 30分钟
  private readonly STATS_TTL = 5 * 60; // 5分钟

  /**
   * 保存游戏房间状态
   */
  async saveRoomState(roomId: string, roomData: GameRoom): Promise<void> {
    try {
      await cacheService.set(
        `${this.ROOM_PREFIX}${roomId}`,
        roomData,
        this.ROOM_TTL
      );
      
      // 更新房间统计
      await this.updateRoomStats(roomData.gameType);
    } catch (error) {
      console.error('保存房间状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取游戏房间状态
   */
  async getRoomState(roomId: string): Promise<GameRoom | null> {
    try {
      return await cacheService.get<GameRoom>(`${this.ROOM_PREFIX}${roomId}`);
    } catch (error) {
      console.error('获取房间状态失败:', error);
      return null;
    }
  }

  /**
   * 删除游戏房间状态
   */
  async deleteRoomState(roomId: string): Promise<void> {
    try {
      await cacheService.del(`${this.ROOM_PREFIX}${roomId}`);
    } catch (error) {
      console.error('删除房间状态失败:', error);
      throw error;
    }
  }

  /**
   * 设置用户所在房间
   */
  async setUserRoom(userId: string, roomId: string): Promise<void> {
    try {
      await cacheService.set(
        `${this.USER_ROOM_PREFIX}${userId}`,
        roomId,
        this.ROOM_TTL
      );
    } catch (error) {
      console.error('设置用户房间失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户所在房间
   */
  async getUserRoom(userId: string): Promise<string | null> {
    try {
      return await cacheService.get<string>(`${this.USER_ROOM_PREFIX}${userId}`);
    } catch (error) {
      console.error('获取用户房间失败:', error);
      return null;
    }
  }

  /**
   * 移除用户房间关联
   */
  async removeUserRoom(userId: string): Promise<void> {
    try {
      await cacheService.del(`${this.USER_ROOM_PREFIX}${userId}`);
    } catch (error) {
      console.error('移除用户房间关联失败:', error);
      throw error;
    }
  }

  /**
   * 添加在线用户
   */
  async addOnlineUser(user: OnlineUser): Promise<void> {
    try {
      await cacheService.sadd(this.ONLINE_USERS_PREFIX, user);
      
      // 设置用户详细信息
      await cacheService.set(
        `online_user:${user.userId}`,
        user,
        this.USER_TTL
      );
    } catch (error) {
      console.error('添加在线用户失败:', error);
      throw error;
    }
  }

  /**
   * 移除在线用户
   */
  async removeOnlineUser(userId: string): Promise<void> {
    try {
      // 先获取用户信息
      const user = await cacheService.get<OnlineUser>(`online_user:${userId}`);
      if (user) {
        await cacheService.srem(this.ONLINE_USERS_PREFIX, user);
      }
      
      await cacheService.del(`online_user:${userId}`);
    } catch (error) {
      console.error('移除在线用户失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有在线用户
   */
  async getOnlineUsers(): Promise<OnlineUser[]> {
    try {
      return await cacheService.smembers<OnlineUser>(this.ONLINE_USERS_PREFIX);
    } catch (error) {
      console.error('获取在线用户失败:', error);
      return [];
    }
  }

  /**
   * 获取在线用户数量
   */
  async getOnlineUserCount(): Promise<number> {
    try {
      return await cacheService.scard(this.ONLINE_USERS_PREFIX);
    } catch (error) {
      console.error('获取在线用户数量失败:', error);
      return 0;
    }
  }

  /**
   * 添加用户到房间
   */
  async addUserToRoom(roomId: string, userId: string): Promise<void> {
    try {
      await cacheService.sadd(`${this.ROOM_USERS_PREFIX}${roomId}`, userId);
      await this.setUserRoom(userId, roomId);
    } catch (error) {
      console.error('添加用户到房间失败:', error);
      throw error;
    }
  }

  /**
   * 从房间移除用户
   */
  async removeUserFromRoom(roomId: string, userId: string): Promise<void> {
    try {
      await cacheService.srem(`${this.ROOM_USERS_PREFIX}${roomId}`, userId);
      await this.removeUserRoom(userId);
    } catch (error) {
      console.error('从房间移除用户失败:', error);
      throw error;
    }
  }

  /**
   * 获取房间内的用户列表
   */
  async getRoomUsers(roomId: string): Promise<string[]> {
    try {
      return await cacheService.smembers<string>(`${this.ROOM_USERS_PREFIX}${roomId}`);
    } catch (error) {
      console.error('获取房间用户列表失败:', error);
      return [];
    }
  }

  /**
   * 更新房间统计
   */
  async updateRoomStats(gameType: string): Promise<void> {
    try {
      const key = `${this.GAME_STATS_PREFIX}${gameType}`;
      await cacheService.incr(`${key}:total_rooms`);
      await cacheService.expire(`${key}:total_rooms`, this.STATS_TTL);
    } catch (error) {
      console.error('更新房间统计失败:', error);
    }
  }

  /**
   * 获取游戏统计
   */
  async getGameStats(gameType?: string): Promise<any> {
    try {
      if (gameType) {
        const key = `${this.GAME_STATS_PREFIX}${gameType}`;
        const totalRooms = await cacheService.get(`${key}:total_rooms`) || 0;
        return {
          gameType,
          totalRooms,
          timestamp: new Date()
        };
      } else {
        // 获取所有游戏类型的统计
        const sangongStats = await this.getGameStats('sangong');
        const niuniuStats = await this.getGameStats('niuniu');
        
        return {
          sangong: sangongStats,
          niuniu: niuniuStats,
          timestamp: new Date()
        };
      }
    } catch (error) {
      console.error('获取游戏统计失败:', error);
      return null;
    }
  }

  /**
   * 清理过期数据
   */
  async cleanupExpiredData(): Promise<void> {
    try {
      console.log('开始清理过期游戏数据...');
      
      // 这里可以实现清理逻辑
      // 由于Redis的TTL机制，大部分过期数据会自动清理
      
      console.log('过期游戏数据清理完成');
    } catch (error) {
      console.error('清理过期数据失败:', error);
    }
  }

  /**
   * 获取实时统计
   */
  async getRealTimeStats(): Promise<{
    onlineUsers: number;
    activeRooms: number;
    gameStats: any;
  }> {
    try {
      const onlineUsers = await this.getOnlineUserCount();
      const gameStats = await this.getGameStats();
      
      return {
        onlineUsers,
        activeRooms: (gameStats.sangong?.totalRooms || 0) + (gameStats.niuniu?.totalRooms || 0),
        gameStats
      };
    } catch (error) {
      console.error('获取实时统计失败:', error);
      return {
        onlineUsers: 0,
        activeRooms: 0,
        gameStats: null
      };
    }
  }
}

export const gameStateCache = new GameStateCacheService();
export default gameStateCache;
