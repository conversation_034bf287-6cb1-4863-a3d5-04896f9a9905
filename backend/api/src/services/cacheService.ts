import { createClient, RedisClientType } from 'redis';

class CacheService {
  private client: RedisClientType;
  private connected: boolean = false;

  constructor() {
    this.client = createClient({
      socket: {
        host: process.env.REDIS_HOST || '127.0.0.1',
        port: parseInt(process.env.REDIS_PORT || '6379')
      },
      password: process.env.REDIS_PASSWORD || undefined
    });

    this.client.on('error', (err) => {
      console.error('Redis连接错误:', err);
      this.connected = false;
    });

    this.client.on('connect', () => {
      console.log('✅ Redis连接成功');
      this.connected = true;
    });

    this.client.on('disconnect', () => {
      console.log('❌ Redis连接断开');
      this.connected = false;
    });
  }

  /**
   * 初始化Redis连接
   */
  async connect(): Promise<void> {
    try {
      await this.client.connect();
    } catch (error) {
      console.error('Redis连接失败:', error);
      throw error;
    }
  }

  /**
   * 关闭Redis连接
   */
  async disconnect(): Promise<void> {
    try {
      await this.client.quit();
      this.connected = false;
    } catch (error) {
      console.error('Redis断开连接失败:', error);
    }
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        await this.client.setEx(key, ttl, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }
    } catch (error) {
      console.error('Redis设置缓存失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      if (value === null) {
        return null;
      }
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Redis获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error) {
      console.error('Redis删除缓存失败:', error);
      throw error;
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis检查键存在失败:', error);
      return false;
    }
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    try {
      await this.client.expire(key, ttl);
    } catch (error) {
      console.error('Redis设置过期时间失败:', error);
      throw error;
    }
  }

  /**
   * 获取剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);
    } catch (error) {
      console.error('Redis获取过期时间失败:', error);
      return -1;
    }
  }

  /**
   * 批量设置
   */
  async mset(keyValues: Record<string, any>, ttl?: number): Promise<void> {
    try {
      const serializedKeyValues: Record<string, string> = {};
      for (const [key, value] of Object.entries(keyValues)) {
        serializedKeyValues[key] = JSON.stringify(value);
      }
      
      await this.client.mSet(serializedKeyValues);
      
      if (ttl) {
        const keys = Object.keys(keyValues);
        await Promise.all(keys.map(key => this.expire(key, ttl)));
      }
    } catch (error) {
      console.error('Redis批量设置失败:', error);
      throw error;
    }
  }

  /**
   * 批量获取
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      const values = await this.client.mGet(keys);
      return values.map(value => {
        if (value === null) return null;
        try {
          return JSON.parse(value) as T;
        } catch {
          return null;
        }
      });
    } catch (error) {
      console.error('Redis批量获取失败:', error);
      return keys.map(() => null);
    }
  }

  /**
   * 增加数值
   */
  async incr(key: string): Promise<number> {
    try {
      return await this.client.incr(key);
    } catch (error) {
      console.error('Redis增加数值失败:', error);
      throw error;
    }
  }

  /**
   * 减少数值
   */
  async decr(key: string): Promise<number> {
    try {
      return await this.client.decr(key);
    } catch (error) {
      console.error('Redis减少数值失败:', error);
      throw error;
    }
  }

  /**
   * 按增量增加数值
   */
  async incrBy(key: string, increment: number): Promise<number> {
    try {
      return await this.client.incrBy(key, increment);
    } catch (error) {
      console.error('Redis按增量增加失败:', error);
      throw error;
    }
  }

  /**
   * 列表左推入
   */
  async lpush(key: string, ...values: any[]): Promise<number> {
    try {
      const serializedValues = values.map(v => JSON.stringify(v));
      return await this.client.lPush(key, serializedValues);
    } catch (error) {
      console.error('Redis列表左推入失败:', error);
      throw error;
    }
  }

  /**
   * 列表右推入
   */
  async rpush(key: string, ...values: any[]): Promise<number> {
    try {
      const serializedValues = values.map(v => JSON.stringify(v));
      return await this.client.rPush(key, serializedValues);
    } catch (error) {
      console.error('Redis列表右推入失败:', error);
      throw error;
    }
  }

  /**
   * 列表左弹出
   */
  async lpop<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.lPop(key);
      if (value === null) return null;
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Redis列表左弹出失败:', error);
      return null;
    }
  }

  /**
   * 列表右弹出
   */
  async rpop<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.rPop(key);
      if (value === null) return null;
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Redis列表右弹出失败:', error);
      return null;
    }
  }

  /**
   * 获取列表长度
   */
  async llen(key: string): Promise<number> {
    try {
      return await this.client.lLen(key);
    } catch (error) {
      console.error('Redis获取列表长度失败:', error);
      return 0;
    }
  }

  /**
   * 获取列表范围
   */
  async lrange<T>(key: string, start: number, stop: number): Promise<T[]> {
    try {
      const values = await this.client.lRange(key, start, stop);
      return values.map(value => {
        try {
          return JSON.parse(value) as T;
        } catch {
          return value as unknown as T;
        }
      });
    } catch (error) {
      console.error('Redis获取列表范围失败:', error);
      return [];
    }
  }

  /**
   * 集合添加成员
   */
  async sadd(key: string, ...members: any[]): Promise<number> {
    try {
      const serializedMembers = members.map(m => JSON.stringify(m));
      return await this.client.sAdd(key, serializedMembers);
    } catch (error) {
      console.error('Redis集合添加成员失败:', error);
      throw error;
    }
  }

  /**
   * 集合移除成员
   */
  async srem(key: string, ...members: any[]): Promise<number> {
    try {
      const serializedMembers = members.map(m => JSON.stringify(m));
      return await this.client.sRem(key, serializedMembers);
    } catch (error) {
      console.error('Redis集合移除成员失败:', error);
      throw error;
    }
  }

  /**
   * 获取集合所有成员
   */
  async smembers<T>(key: string): Promise<T[]> {
    try {
      const members = await this.client.sMembers(key);
      return members.map(member => {
        try {
          return JSON.parse(member) as T;
        } catch {
          return member as unknown as T;
        }
      });
    } catch (error) {
      console.error('Redis获取集合成员失败:', error);
      return [];
    }
  }

  /**
   * 检查是否为集合成员
   */
  async sismember(key: string, member: any): Promise<boolean> {
    try {
      const serializedMember = JSON.stringify(member);
      return await this.client.sIsMember(key, serializedMember);
    } catch (error) {
      console.error('Redis检查集合成员失败:', error);
      return false;
    }
  }

  /**
   * 获取集合大小
   */
  async scard(key: string): Promise<number> {
    try {
      return await this.client.sCard(key);
    } catch (error) {
      console.error('Redis获取集合大小失败:', error);
      return 0;
    }
  }
}

export const cacheService = new CacheService();
export default cacheService;
