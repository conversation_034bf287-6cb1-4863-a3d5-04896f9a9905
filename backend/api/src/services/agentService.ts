import bcrypt from 'bcryptjs';
import { pool } from '../config/database';
import { generateTokenPair } from '../config/jwt';
import { generateId } from '@games/shared';
import { AppError } from '../middleware/error';

export interface CreateAgentData {
  username: string;
  email: string;
  phone?: string;
  password: string;
  clubName: string;
  clubDescription?: string;
}

export interface AgentLoginData {
  username: string;
  password: string;
}

export class AgentService {
  /**
   * 创建新代理（同时创建俱乐部）
   */
  static async createAgent(agentData: CreateAgentData) {
    const { username, email, phone, password, clubName, clubDescription } = agentData;
    
    // 检查用户名是否已存在
    const [existingAgents] = await pool.execute(
      'SELECT id FROM agents WHERE username = ?',
      [username]
    );
    
    if (Array.isArray(existingAgents) && existingAgents.length > 0) {
      throw new AppError('Username already exists', 409);
    }
    
    // 检查邮箱是否已存在
    const [existingEmails] = await pool.execute(
      'SELECT id FROM agents WHERE email = ?',
      [email]
    );
    
    if (Array.isArray(existingEmails) && existingEmails.length > 0) {
      throw new AppError('Email already exists', 409);
    }
    
    // 检查俱乐部名称是否已存在
    const [existingClubs] = await pool.execute(
      'SELECT id FROM clubs WHERE name = ?',
      [clubName]
    );
    
    if (Array.isArray(existingClubs) && existingClubs.length > 0) {
      throw new AppError('Club name already exists', 409);
    }
    
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 加密密码
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
      const passwordHash = await bcrypt.hash(password, saltRounds);
      
      // 创建代理
      const agentId = generateId();
      await connection.execute(
        `INSERT INTO agents (id, username, email, phone, password_hash) 
         VALUES (?, ?, ?, ?, ?)`,
        [agentId, username, email, phone || null, passwordHash]
      );
      
      // 创建俱乐部（代理即俱乐部）
      const clubId = generateId();
      await connection.execute(
        `INSERT INTO clubs (id, agent_id, name, description) 
         VALUES (?, ?, ?, ?)`,
        [clubId, agentId, clubName, clubDescription || null]
      );
      
      await connection.commit();
      
      // 获取创建的代理信息
      const [agents] = await connection.execute(
        `SELECT a.id, a.username, a.email, a.phone, a.balance, a.commission, a.status, a.created_at,
                c.id as club_id, c.name as club_name, c.description as club_description
         FROM agents a 
         JOIN clubs c ON a.id = c.agent_id 
         WHERE a.id = ?`,
        [agentId]
      );
      
      return Array.isArray(agents) ? agents[0] : null;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
  
  /**
   * 代理登录
   */
  static async loginAgent(loginData: AgentLoginData, ipAddress?: string, userAgent?: string) {
    const { username, password } = loginData;

    // 查找代理
    const [agents] = await pool.execute(
      `SELECT a.id, a.username, a.password_hash, a.balance, a.commission, a.status,
              c.id as club_id, c.name as club_name, c.description as club_description
       FROM agents a
       JOIN clubs c ON a.id = c.agent_id
       WHERE a.username = ?`,
      [username]
    );

    if (!Array.isArray(agents) || agents.length === 0) {
      throw new AppError('用户名或密码错误', 401);
    }

    const agent = agents[0] as any;

    // 检查代理状态
    if (agent.status !== 'active') {
      throw new AppError('代理账户已被禁用或封禁', 403);
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, agent.password_hash);
    if (!isPasswordValid) {
      throw new AppError('用户名或密码错误', 401);
    }
    
    // 生成令牌
    const tokens = generateTokenPair(agent.id, 'agent', agent.username);
    
    // 计算过期时间
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30); // 30天后过期
    
    // 保存会话
    await pool.execute(
      `INSERT INTO user_sessions (id, user_id, user_type, token_hash, refresh_token_hash, ip_address, user_agent, expires_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        tokens.sessionId,
        agent.id,
        'agent',
        this.hashToken(tokens.accessToken),
        this.hashToken(tokens.refreshToken),
        ipAddress || null,
        userAgent || null,
        expiresAt
      ]
    );
    
    // 更新最后登录时间
    await pool.execute(
      'UPDATE agents SET last_login_at = NOW() WHERE id = ?',
      [agent.id]
    );
    
    return {
      agent: {
        id: agent.id,
        username: agent.username,
        email: agent.email,
        phone: agent.phone,
        balance: agent.balance,
        commission: agent.commission,
        club: {
          id: agent.club_id,
          name: agent.club_name,
          description: agent.club_description
        }
      },
      tokens
    };
  }
  
  /**
   * 获取代理信息
   */
  static async getAgentById(agentId: string) {
    const [agents] = await pool.execute(
      `SELECT a.id, a.username, a.email, a.phone, a.balance, a.commission, a.status, a.created_at, a.updated_at,
              c.id as club_id, c.name as club_name, c.description as club_description, c.current_members
       FROM agents a 
       JOIN clubs c ON a.id = c.agent_id 
       WHERE a.id = ?`,
      [agentId]
    );
    
    return Array.isArray(agents) && agents.length > 0 ? agents[0] : null;
  }
  
  /**
   * 代理给用户充值
   */
  static async depositToUser(agentId: string, userId: string, amount: number, description?: string) {
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 获取代理余额
      const [agents] = await connection.execute(
        'SELECT balance FROM agents WHERE id = ? FOR UPDATE',
        [agentId]
      );
      
      if (!Array.isArray(agents) || agents.length === 0) {
        throw new AppError('Agent not found', 404);
      }
      
      const agentBalance = (agents[0] as any).balance;
      if (agentBalance < amount) {
        throw new AppError('Insufficient agent balance', 400);
      }
      
      // 检查用户是否属于该代理
      const [users] = await connection.execute(
        'SELECT id, balance FROM users WHERE id = ? AND agent_id = ? FOR UPDATE',
        [userId, agentId]
      );
      
      if (!Array.isArray(users) || users.length === 0) {
        throw new AppError('User not found or not belongs to this agent', 404);
      }
      
      const userBalance = (users[0] as any).balance;
      
      // 扣除代理余额
      await connection.execute(
        'UPDATE agents SET balance = balance - ? WHERE id = ?',
        [amount, agentId]
      );
      
      // 增加用户余额
      await connection.execute(
        'UPDATE users SET balance = balance + ? WHERE id = ?',
        [amount, userId]
      );
      
      // 记录代理交易
      const agentTransactionId = generateId();
      await connection.execute(
        `INSERT INTO transactions (id, agent_id, type, amount, balance_before, balance_after, description, reference_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [agentTransactionId, agentId, 'transfer', -amount, agentBalance, agentBalance - amount, description || '给用户充值', userId]
      );
      
      // 记录用户交易
      const userTransactionId = generateId();
      await connection.execute(
        `INSERT INTO transactions (id, user_id, type, amount, balance_before, balance_after, description, reference_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [userTransactionId, userId, 'deposit', amount, userBalance, userBalance + amount, description || '代理充值', agentId]
      );
      
      await connection.commit();
      
      return {
        agentBalance: agentBalance - amount,
        userBalance: userBalance + amount,
        agentTransactionId,
        userTransactionId
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
  
  /**
   * 用户向代理提现
   */
  static async withdrawFromUser(agentId: string, userId: string, amount: number, description?: string) {
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 检查用户是否属于该代理并获取余额
      const [users] = await connection.execute(
        'SELECT id, balance FROM users WHERE id = ? AND agent_id = ? FOR UPDATE',
        [userId, agentId]
      );
      
      if (!Array.isArray(users) || users.length === 0) {
        throw new AppError('User not found or not belongs to this agent', 404);
      }
      
      const userBalance = (users[0] as any).balance;
      if (userBalance < amount) {
        throw new AppError('Insufficient user balance', 400);
      }
      
      // 获取代理余额
      const [agents] = await connection.execute(
        'SELECT balance FROM agents WHERE id = ? FOR UPDATE',
        [agentId]
      );
      
      const agentBalance = ((agents as any)[0] as any).balance;
      
      // 扣除用户余额
      await connection.execute(
        'UPDATE users SET balance = balance - ? WHERE id = ?',
        [amount, userId]
      );
      
      // 增加代理余额
      await connection.execute(
        'UPDATE agents SET balance = balance + ? WHERE id = ?',
        [amount, agentId]
      );
      
      // 记录用户交易
      const userTransactionId = generateId();
      await connection.execute(
        `INSERT INTO transactions (id, user_id, type, amount, balance_before, balance_after, description, reference_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [userTransactionId, userId, 'withdraw', -amount, userBalance, userBalance - amount, description || '向代理提现', agentId]
      );
      
      // 记录代理交易
      const agentTransactionId = generateId();
      await connection.execute(
        `INSERT INTO transactions (id, agent_id, type, amount, balance_before, balance_after, description, reference_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [agentTransactionId, agentId, 'transfer', amount, agentBalance, agentBalance + amount, description || '用户提现', userId]
      );
      
      await connection.commit();
      
      return {
        userBalance: userBalance - amount,
        agentBalance: agentBalance + amount,
        userTransactionId,
        agentTransactionId
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
  
  /**
   * 获取代理下的用户列表
   */
  static async getAgentUsers(agentId: string, page: number = 1, limit: number = 20) {
    const offset = (page - 1) * limit;
    
    // 获取用户列表
    const [users] = await pool.execute(
      `SELECT id, username, email, phone, balance, status, last_login_at, created_at
       FROM users 
       WHERE agent_id = ? 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [agentId, limit, offset]
    );
    
    // 获取总数
    const [countResult] = await pool.execute(
      'SELECT COUNT(*) as total FROM users WHERE agent_id = ?',
      [agentId]
    );
    
    const total = Array.isArray(countResult) ? (countResult[0] as any).total : 0;
    
    return {
      users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * 获取代理统计数据
   */
  static async getAgentStats(agentId: string) {
    // 获取代理基本信息
    const [agentInfo] = await pool.execute(
      `SELECT a.balance, c.name as club_name, c.id as club_id, c.current_members
       FROM agents a
       JOIN clubs c ON a.id = c.agent_id
       WHERE a.id = ?`,
      [agentId]
    );

    if (!Array.isArray(agentInfo) || agentInfo.length === 0) {
      throw new AppError('代理不存在', 404);
    }

    const agent = agentInfo[0] as any;

    // 获取用户统计
    const [userStats] = await pool.execute(
      `SELECT
         COUNT(*) as totalUsers,
         COUNT(CASE WHEN status = 'active' THEN 1 END) as activeUsers,
         COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as todayUsers
       FROM users
       WHERE agent_id = ?`,
      [agentId]
    );

    // 获取交易统计
    const [transactionStats] = await pool.execute(
      `SELECT
         COUNT(*) as totalTransactions,
         COALESCE(SUM(CASE WHEN type = 'commission' THEN amount ELSE 0 END), 0) as totalCommission,
         COALESCE(SUM(CASE WHEN type = 'commission' AND DATE(created_at) = CURDATE() THEN amount ELSE 0 END), 0) as todayCommission
       FROM transactions
       WHERE agent_id = ?`,
      [agentId]
    );

    // 获取游戏统计（简化版本，避免复杂查询）
    const gameStatsData = { totalGames: 0, todayGames: 0 };

    const userStatsData = Array.isArray(userStats) ? userStats[0] as any : {};
    const transactionStatsData = Array.isArray(transactionStats) ? transactionStats[0] as any : {};

    return {
      agentBalance: parseFloat(agent.balance || '0'),
      totalUsers: parseInt(userStatsData.totalUsers || '0'),
      activeUsers: parseInt(userStatsData.activeUsers || '0'),
      todayUsers: parseInt(userStatsData.todayUsers || '0'),
      totalGames: parseInt(String(gameStatsData.totalGames || '0')),
      todayGames: parseInt(String(gameStatsData.todayGames || '0')),
      totalCommission: parseFloat(transactionStatsData.totalCommission || '0'),
      todayCommission: parseFloat(transactionStatsData.todayCommission || '0'),
      totalBet: 0, // 需要从游戏记录中计算
      clubInfo: {
        id: agent.club_id,
        name: agent.club_name,
        currentMembers: parseInt(agent.current_members || '0')
      }
    };
  }

  /**
   * 获取代理交易记录
   */
  static async getAgentTransactions(agentId: string, options: {
    page: number;
    limit: number;
    type?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  }) {
    const { page, limit } = options;
    const offset = (page - 1) * limit;

    // 简化查询，避免复杂的条件
    const [transactions] = await pool.execute(
      `SELECT t.*, u.username
       FROM transactions t
       LEFT JOIN users u ON t.user_id = u.id
       WHERE t.agent_id = ?
       ORDER BY t.created_at DESC
       LIMIT ? OFFSET ?`,
      [agentId, limit, offset]
    );

    const [countResult] = await pool.execute(
      `SELECT COUNT(*) as total FROM transactions WHERE agent_id = ?`,
      [agentId]
    );

    const total = Array.isArray(countResult) ? (countResult[0] as any).total : 0;

    return {
      transactions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 获取代理游戏记录
   */
  static async getAgentGames(agentId: string, options: {
    page: number;
    limit: number;
    gameType?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  }) {
    const { page, limit } = options;

    // 返回空数据，因为游戏记录表还没有完全实现
    const games: any[] = [];
    const total = 0;

    return {
      games,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 获取代理财务报表
   */
  static async getAgentReports(agentId: string, options: {
    startDate?: string;
    endDate?: string;
    type: string;
  }) {
    // 获取汇总数据
    const [summaryResult] = await pool.execute(
      `SELECT
         COALESCE(SUM(CASE WHEN type = 'commission' THEN amount ELSE 0 END), 0) as totalCommission,
         COUNT(DISTINCT user_id) as totalUsers
       FROM transactions
       WHERE agent_id = ?`,
      [agentId]
    );

    const summary = Array.isArray(summaryResult) ? summaryResult[0] as any : {};
    const reports: any[] = [];

    return {
      summary: {
        totalRevenue: 0,
        totalCommission: parseFloat(summary.totalCommission || '0'),
        totalProfit: parseFloat(summary.totalCommission || '0'),
        totalUsers: parseInt(summary.totalUsers || '0'),
        totalGames: 0,
        avgDailyRevenue: 0
      },
      reports
    };
  }

  /**
   * 更新用户状态
   */
  static async updateUserStatus(agentId: string, userId: string, status: string) {
    // 验证用户是否属于该代理
    const [users] = await pool.execute(
      'SELECT id FROM users WHERE id = ? AND agent_id = ?',
      [userId, agentId]
    );

    if (!Array.isArray(users) || users.length === 0) {
      throw new AppError('用户不存在或不属于该代理', 404);
    }

    // 更新用户状态
    await pool.execute(
      'UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?',
      [status, userId]
    );

    return true;
  }

  /**
   * 代理登出
   */
  static async logoutAgent(sessionId: string) {
    await pool.execute(
      'DELETE FROM user_sessions WHERE id = ?',
      [sessionId]
    );
  }



  /**
   * 获取代理的俱乐部信息
   */
  static async getAgentClub(agentId: string) {
    const [clubs] = await pool.execute(
      `SELECT c.id, c.name, c.description, c.club_code, c.auto_join, c.join_approval_required,
              c.max_members, c.current_members, c.status, c.created_at, c.updated_at
       FROM clubs c
       WHERE c.agent_id = ?`,
      [agentId]
    );

    return Array.isArray(clubs) && clubs.length > 0 ? clubs[0] : null;
  }

  /**
   * 更新代理的俱乐部设置
   */
  static async updateAgentClub(agentId: string, clubData: {
    name?: string;
    description?: string;
    clubCode?: string;
    autoJoin?: boolean;
    joinApprovalRequired?: boolean;
    maxMembers?: number;
  }) {
    const { name, description, clubCode, autoJoin, joinApprovalRequired, maxMembers } = clubData;

    // 检查俱乐部代码是否已被其他俱乐部使用
    if (clubCode) {
      const [existingClubs] = await pool.execute(
        'SELECT id FROM clubs WHERE club_code = ? AND agent_id != ?',
        [clubCode, agentId]
      );

      if (Array.isArray(existingClubs) && existingClubs.length > 0) {
        throw new AppError('俱乐部代码已被使用', 409);
      }
    }

    // 构建更新字段
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }
    if (clubCode !== undefined) {
      updateFields.push('club_code = ?');
      updateValues.push(clubCode);
    }
    if (autoJoin !== undefined) {
      updateFields.push('auto_join = ?');
      updateValues.push(autoJoin);
    }
    if (joinApprovalRequired !== undefined) {
      updateFields.push('join_approval_required = ?');
      updateValues.push(joinApprovalRequired);
    }
    if (maxMembers !== undefined) {
      updateFields.push('max_members = ?');
      updateValues.push(maxMembers);
    }

    if (updateFields.length === 0) {
      throw new AppError('没有要更新的字段', 400);
    }

    updateValues.push(agentId);

    await pool.execute(
      `UPDATE clubs SET ${updateFields.join(', ')}, updated_at = NOW() WHERE agent_id = ?`,
      updateValues
    );

    // 返回更新后的俱乐部信息
    return this.getAgentClub(agentId);
  }

  /**
   * 获取俱乐部申请列表
   */
  static async getClubApplications(agentId: string, status?: string) {
    let query = `
      SELECT ca.id, ca.user_id, ca.club_code, ca.status, ca.applied_at, ca.processed_at,
             ca.rejection_reason, ca.notes, u.username
      FROM club_applications ca
      JOIN users u ON ca.user_id = u.id
      JOIN clubs c ON ca.club_id = c.id
      WHERE c.agent_id = ?
    `;

    const queryParams: any[] = [agentId];

    if (status) {
      query += ' AND ca.status = ?';
      queryParams.push(status);
    }

    query += ' ORDER BY ca.applied_at DESC';

    const [applications] = await pool.execute(query, queryParams);

    return Array.isArray(applications) ? applications : [];
  }

  /**
   * 处理俱乐部申请（批准或拒绝）
   */
  static async processClubApplication(agentId: string, applicationId: string, action: 'approve' | 'reject', rejectionReason?: string) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // 获取申请信息
      const [applications] = await connection.execute(
        `SELECT ca.id, ca.user_id, ca.club_id, ca.status, c.agent_id, c.current_members, c.max_members
         FROM club_applications ca
         JOIN clubs c ON ca.club_id = c.id
         WHERE ca.id = ? AND c.agent_id = ? AND ca.status = 'pending'`,
        [applicationId, agentId]
      );

      if (!Array.isArray(applications) || applications.length === 0) {
        throw new AppError('申请不存在或已处理', 404);
      }

      const application = applications[0] as any;

      if (action === 'approve') {
        // 检查俱乐部是否已满员
        if (application.current_members >= application.max_members) {
          throw new AppError('俱乐部已满员', 400);
        }

        // 检查用户是否已经在俱乐部中
        const [existingMembers] = await connection.execute(
          'SELECT id FROM user_clubs WHERE user_id = ? AND club_id = ? AND status = "active"',
          [application.user_id, application.club_id]
        );

        if (Array.isArray(existingMembers) && existingMembers.length > 0) {
          throw new AppError('用户已经是俱乐部成员', 400);
        }

        // 添加用户到俱乐部
        await connection.execute(
          `INSERT INTO user_clubs (user_id, club_id, joined_at, status, is_primary)
           VALUES (?, ?, NOW(), 'active', TRUE)
           ON DUPLICATE KEY UPDATE status = 'active', joined_at = NOW()`,
          [application.user_id, application.club_id]
        );

        // 更新用户的club_id（保持向后兼容）
        await connection.execute(
          'UPDATE users SET club_id = ? WHERE id = ?',
          [application.club_id, application.user_id]
        );
      }

      // 更新申请状态
      await connection.execute(
        `UPDATE club_applications
         SET status = ?, processed_at = NOW(), processed_by = ?, rejection_reason = ?
         WHERE id = ?`,
        [action === 'approve' ? 'approved' : 'rejected', agentId, rejectionReason || null, applicationId]
      );

      await connection.commit();

      return { success: true };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 哈希令牌（用于存储）
   */
  private static hashToken(token: string): string {
    return bcrypt.hashSync(token, 10);
  }
}
