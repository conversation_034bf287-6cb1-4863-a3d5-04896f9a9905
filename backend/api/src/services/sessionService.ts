import { cacheService } from './cacheService';

interface SessionData {
  userId: string;
  username: string;
  userType: 'user' | 'agent' | 'admin';
  loginTime: Date;
  lastActivity: Date;
  ipAddress: string;
  userAgent: string;
  permissions?: string[];
}

interface LoginSession {
  sessionId: string;
  refreshToken: string;
  expiresAt: Date;
}

class SessionService {
  private readonly SESSION_PREFIX = 'session:';
  private readonly USER_SESSIONS_PREFIX = 'user_sessions:';
  private readonly REFRESH_TOKEN_PREFIX = 'refresh_token:';
  private readonly SESSION_TTL = 24 * 60 * 60; // 24小时
  private readonly REFRESH_TOKEN_TTL = 7 * 24 * 60 * 60; // 7天

  /**
   * 创建会话
   */
  async createSession(
    userId: string,
    username: string,
    userType: 'user' | 'agent' | 'admin',
    ipAddress: string,
    userAgent: string,
    permissions?: string[]
  ): Promise<{ sessionId: string; refreshToken: string }> {
    const sessionId = this.generateSessionId();
    const refreshToken = this.generateRefreshToken();
    
    const sessionData: SessionData = {
      userId,
      username,
      userType,
      loginTime: new Date(),
      lastActivity: new Date(),
      ipAddress,
      userAgent,
      permissions
    };

    const loginSession: LoginSession = {
      sessionId,
      refreshToken,
      expiresAt: new Date(Date.now() + this.REFRESH_TOKEN_TTL * 1000)
    };

    // 存储会话数据
    await cacheService.set(
      `${this.SESSION_PREFIX}${sessionId}`,
      sessionData,
      this.SESSION_TTL
    );

    // 存储刷新令牌
    await cacheService.set(
      `${this.REFRESH_TOKEN_PREFIX}${refreshToken}`,
      { sessionId, userId },
      this.REFRESH_TOKEN_TTL
    );

    // 添加到用户会话列表
    await this.addUserSession(userId, loginSession);

    console.log(`✅ 创建会话: ${sessionId} for user ${userId}`);
    return { sessionId, refreshToken };
  }

  /**
   * 获取会话数据
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionData = await cacheService.get<SessionData>(
        `${this.SESSION_PREFIX}${sessionId}`
      );
      
      if (sessionData) {
        // 更新最后活动时间
        sessionData.lastActivity = new Date();
        await cacheService.set(
          `${this.SESSION_PREFIX}${sessionId}`,
          sessionData,
          this.SESSION_TTL
        );
      }
      
      return sessionData;
    } catch (error) {
      console.error('获取会话失败:', error);
      return null;
    }
  }

  /**
   * 更新会话
   */
  async updateSession(sessionId: string, updates: Partial<SessionData>): Promise<void> {
    try {
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) {
        throw new Error('会话不存在');
      }

      const updatedSession = {
        ...sessionData,
        ...updates,
        lastActivity: new Date()
      };

      await cacheService.set(
        `${this.SESSION_PREFIX}${sessionId}`,
        updatedSession,
        this.SESSION_TTL
      );
    } catch (error) {
      console.error('更新会话失败:', error);
      throw error;
    }
  }

  /**
   * 删除会话
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      const sessionData = await cacheService.get<SessionData>(
        `${this.SESSION_PREFIX}${sessionId}`
      );

      if (sessionData) {
        // 从用户会话列表中移除
        await this.removeUserSession(sessionData.userId, sessionId);
      }

      // 删除会话数据
      await cacheService.del(`${this.SESSION_PREFIX}${sessionId}`);
      
      console.log(`✅ 删除会话: ${sessionId}`);
    } catch (error) {
      console.error('删除会话失败:', error);
      throw error;
    }
  }

  /**
   * 通过刷新令牌刷新会话
   */
  async refreshSession(refreshToken: string): Promise<{ sessionId: string; newRefreshToken: string } | null> {
    try {
      const tokenData = await cacheService.get<{ sessionId: string; userId: string }>(
        `${this.REFRESH_TOKEN_PREFIX}${refreshToken}`
      );

      if (!tokenData) {
        return null;
      }

      const sessionData = await this.getSession(tokenData.sessionId);
      if (!sessionData) {
        return null;
      }

      // 生成新的刷新令牌
      const newRefreshToken = this.generateRefreshToken();

      // 删除旧的刷新令牌
      await cacheService.del(`${this.REFRESH_TOKEN_PREFIX}${refreshToken}`);

      // 存储新的刷新令牌
      await cacheService.set(
        `${this.REFRESH_TOKEN_PREFIX}${newRefreshToken}`,
        { sessionId: tokenData.sessionId, userId: tokenData.userId },
        this.REFRESH_TOKEN_TTL
      );

      // 更新用户会话列表中的刷新令牌
      await this.updateUserSessionRefreshToken(
        tokenData.userId,
        tokenData.sessionId,
        newRefreshToken
      );

      return { sessionId: tokenData.sessionId, newRefreshToken };
    } catch (error) {
      console.error('刷新会话失败:', error);
      return null;
    }
  }

  /**
   * 获取用户的所有会话
   */
  async getUserSessions(userId: string): Promise<LoginSession[]> {
    try {
      const sessions = await cacheService.get<LoginSession[]>(
        `${this.USER_SESSIONS_PREFIX}${userId}`
      );
      return sessions || [];
    } catch (error) {
      console.error('获取用户会话失败:', error);
      return [];
    }
  }

  /**
   * 删除用户的所有会话
   */
  async deleteAllUserSessions(userId: string): Promise<void> {
    try {
      const sessions = await this.getUserSessions(userId);
      
      // 删除所有会话数据
      await Promise.all(
        sessions.map(session => this.deleteSession(session.sessionId))
      );

      // 删除所有刷新令牌
      await Promise.all(
        sessions.map(session => 
          cacheService.del(`${this.REFRESH_TOKEN_PREFIX}${session.refreshToken}`)
        )
      );

      // 清空用户会话列表
      await cacheService.del(`${this.USER_SESSIONS_PREFIX}${userId}`);
      
      console.log(`✅ 删除用户所有会话: ${userId}`);
    } catch (error) {
      console.error('删除用户所有会话失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      console.log('开始清理过期会话...');
      // 这里需要实现扫描所有会话并清理过期的
      // 由于Redis没有直接的模式匹配删除，这里简化实现
      console.log('过期会话清理完成');
    } catch (error) {
      console.error('清理过期会话失败:', error);
    }
  }

  /**
   * 获取在线用户统计
   */
  async getOnlineStats(): Promise<{
    totalOnline: number;
    usersByType: Record<string, number>;
  }> {
    try {
      // 这里需要实现扫描所有活跃会话的统计
      // 简化实现，返回模拟数据
      return {
        totalOnline: 0,
        usersByType: {
          user: 0,
          agent: 0,
          admin: 0
        }
      };
    } catch (error) {
      console.error('获取在线统计失败:', error);
      return {
        totalOnline: 0,
        usersByType: {
          user: 0,
          agent: 0,
          admin: 0
        }
      };
    }
  }

  /**
   * 添加用户会话到列表
   */
  private async addUserSession(userId: string, session: LoginSession): Promise<void> {
    const sessions = await this.getUserSessions(userId);
    sessions.push(session);
    
    // 限制每个用户最多保持5个会话
    if (sessions.length > 5) {
      const oldestSession = sessions.shift();
      if (oldestSession) {
        await this.deleteSession(oldestSession.sessionId);
        await cacheService.del(`${this.REFRESH_TOKEN_PREFIX}${oldestSession.refreshToken}`);
      }
    }
    
    await cacheService.set(
      `${this.USER_SESSIONS_PREFIX}${userId}`,
      sessions,
      this.REFRESH_TOKEN_TTL
    );
  }

  /**
   * 从用户会话列表中移除会话
   */
  private async removeUserSession(userId: string, sessionId: string): Promise<void> {
    const sessions = await this.getUserSessions(userId);
    const filteredSessions = sessions.filter(s => s.sessionId !== sessionId);
    
    if (filteredSessions.length > 0) {
      await cacheService.set(
        `${this.USER_SESSIONS_PREFIX}${userId}`,
        filteredSessions,
        this.REFRESH_TOKEN_TTL
      );
    } else {
      await cacheService.del(`${this.USER_SESSIONS_PREFIX}${userId}`);
    }
  }

  /**
   * 更新用户会话中的刷新令牌
   */
  private async updateUserSessionRefreshToken(
    userId: string,
    sessionId: string,
    newRefreshToken: string
  ): Promise<void> {
    const sessions = await this.getUserSessions(userId);
    const sessionIndex = sessions.findIndex(s => s.sessionId === sessionId);
    
    if (sessionIndex !== -1) {
      sessions[sessionIndex].refreshToken = newRefreshToken;
      await cacheService.set(
        `${this.USER_SESSIONS_PREFIX}${userId}`,
        sessions,
        this.REFRESH_TOKEN_TTL
      );
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成刷新令牌
   */
  private generateRefreshToken(): string {
    return `ref_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
  }
}

export const sessionService = new SessionService();
export default sessionService;
