import bcrypt from 'bcryptjs';
import { pool } from '../config/database';
import { generateTokenPair, generateAccessToken, generateRefreshToken } from '../config/jwt';
import { sessionService } from './sessionService';
import { RowDataPacket } from 'mysql2';
import { generateId } from '@games/shared';

interface Admin {
  id: string;
  username: string;
  email: string;
  password_hash: string;
  role: string;
  permissions: any;
  status: string;
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export class AdminService {
  /**
   * 解析权限字段
   */
  private static parsePermissions(permissions: any): string[] {
    if (!permissions) {
      return ['all']; // 默认权限
    }

    try {
      if (typeof permissions === 'string') {
        return JSON.parse(permissions);
      }
      if (Array.isArray(permissions)) {
        return permissions;
      }
      return ['all'];
    } catch (error) {
      console.warn('权限字段解析失败，使用默认权限:', error instanceof Error ? error.message : String(error));
      return ['all'];
    }
  }

  /**
   * 管理员登录
   */
  static async loginAdmin(username: string, password: string, ipAddress: string, userAgent: string) {
    try {
      // 查找管理员
      const [rows] = await pool.execute<RowDataPacket[]>(
        'SELECT * FROM admins WHERE username = ? AND status = "active"',
        [username]
      );

      if (rows.length === 0) {
        throw new Error('管理员不存在或已被禁用');
      }

      const admin = rows[0] as Admin;

      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, admin.password_hash);
      if (!isPasswordValid) {
        throw new Error('密码错误');
      }

      // 生成会话ID和令牌
      const sessionId = generateId();
      const payload = {
        userId: admin.id,
        userType: 'admin' as const,
        username: admin.username,
        sessionId
      };

      const tokens = {
        accessToken: generateAccessToken(payload),
        refreshToken: generateRefreshToken(payload)
      };

      // 计算过期时间
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 30); // 30天后过期

      // 保存会话到MySQL
      await pool.execute(
        `INSERT INTO user_sessions (id, user_id, user_type, token_hash, refresh_token_hash, ip_address, user_agent, expires_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          sessionId,
          admin.id,
          'admin',
          this.hashToken(tokens.accessToken),
          this.hashToken(tokens.refreshToken),
          ipAddress || null,
          userAgent || null,
          expiresAt
        ]
      );

      // 更新最后登录时间
      await pool.execute(
        'UPDATE admins SET last_login_at = NOW() WHERE id = ?',
        [admin.id]
      );

      // 返回登录结果
      return {
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          role: admin.role,
          permissions: this.parsePermissions(admin.permissions),
          status: admin.status
        },
        tokens: {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken
        }
      };
    } catch (error) {
      console.error('管理员登录失败:', error);
      throw error;
    }
  }

  /**
   * 管理员登出
   */
  static async logoutAdmin(sessionId: string): Promise<void> {
    try {
      await sessionService.deleteSession(sessionId);
    } catch (error) {
      console.error('管理员登出失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取管理员信息
   */
  static async getAdminById(adminId: string): Promise<Admin | null> {
    try {
      const [rows] = await pool.execute<RowDataPacket[]>(
        'SELECT id, username, email, role, permissions, status, last_login_at, created_at, updated_at FROM admins WHERE id = ?',
        [adminId]
      );

      if (rows.length === 0) {
        return null;
      }

      const admin = rows[0] as Admin;
      return {
        ...admin,
        permissions: admin.permissions ? JSON.parse(admin.permissions) : []
      };
    } catch (error) {
      console.error('获取管理员信息失败:', error);
      throw error;
    }
  }

  /**
   * 根据用户名获取管理员信息
   */
  static async getAdminByUsername(username: string): Promise<Admin | null> {
    try {
      const [rows] = await pool.execute<RowDataPacket[]>(
        'SELECT * FROM admins WHERE username = ?',
        [username]
      );

      if (rows.length === 0) {
        return null;
      }

      return rows[0] as Admin;
    } catch (error) {
      console.error('根据用户名获取管理员失败:', error);
      throw error;
    }
  }

  /**
   * 获取仪表板统计数据
   */
  static async getDashboardStats() {
    try {
      // 获取总用户数
      const [totalUsersResult] = await pool.execute<RowDataPacket[]>(
        'SELECT COUNT(*) as count FROM users WHERE status != "banned"'
      );
      const totalUsers = totalUsersResult[0].count;

      // 获取总代理数
      const [totalAgentsResult] = await pool.execute<RowDataPacket[]>(
        'SELECT COUNT(*) as count FROM agents WHERE status = "active"'
      );
      const totalAgents = totalAgentsResult[0].count;

      // 获取总游戏局数
      const [totalGamesResult] = await pool.execute<RowDataPacket[]>(
        'SELECT COUNT(*) as count FROM game_records'
      );
      const totalGames = totalGamesResult[0].count;

      // 获取总收入（所有交易的总和，排除充值）
      const [totalRevenueResult] = await pool.execute<RowDataPacket[]>(
        'SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE type IN ("bet", "commission") AND status = "completed"'
      );
      const totalRevenue = parseFloat(totalRevenueResult[0].total) || 0;

      // 获取今日新增用户数
      const [todayUsersResult] = await pool.execute<RowDataPacket[]>(
        'SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()'
      );
      const todayUsers = todayUsersResult[0].count;

      // 获取今日游戏局数
      const [todayGamesResult] = await pool.execute<RowDataPacket[]>(
        'SELECT COUNT(*) as count FROM game_records WHERE DATE(started_at) = CURDATE()'
      );
      const todayGames = todayGamesResult[0].count;

      // 获取今日收入
      const [todayRevenueResult] = await pool.execute<RowDataPacket[]>(
        'SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE type IN ("bet", "commission") AND status = "completed" AND DATE(created_at) = CURDATE()'
      );
      const todayRevenue = parseFloat(todayRevenueResult[0].total) || 0;

      // 获取在线用户数（最近5分钟有活动的用户）
      const [onlineUsersResult] = await pool.execute<RowDataPacket[]>(
        'SELECT COUNT(*) as count FROM users WHERE last_login_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)'
      );
      const onlineUsers = onlineUsersResult[0].count;

      return {
        totalUsers,
        totalAgents,
        totalGames,
        totalRevenue,
        todayUsers,
        todayGames,
        todayRevenue,
        onlineUsers
      };
    } catch (error) {
      console.error('获取仪表板统计数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户列表
   */
  static async getUsers(page: number = 1, limit: number = 20, search?: string, status?: string) {
    try {
      const offset = (page - 1) * limit;
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (search) {
        whereClause += ' AND (u.username LIKE ? OR a.username LIKE ?)';
        const searchPattern = `%${search}%`;
        params.push(searchPattern, searchPattern);
      }

      if (status) {
        whereClause += ' AND u.status = ?';
        params.push(status);
      }

      // 获取总数
      const [countResult] = await pool.execute<RowDataPacket[]>(
        `SELECT COUNT(*) as total FROM users u
         LEFT JOIN agents a ON u.agent_id = a.id
         ${whereClause}`,
        params
      );
      const total = countResult[0].total;

      // 获取用户列表
      const [users] = await pool.execute<RowDataPacket[]>(
        `SELECT u.id, u.username, u.balance, u.status, u.last_login_at, u.created_at,
                a.username as agent_username, c.name as club_name
         FROM users u
         LEFT JOIN agents a ON u.agent_id = a.id
         LEFT JOIN clubs c ON a.id = c.agent_id
         ${whereClause}
         ORDER BY u.created_at DESC
         LIMIT ${limit} OFFSET ${offset}`,
        params
      );

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取用户详情
   */
  static async getUserById(userId: string) {
    try {
      const [rows] = await pool.execute<RowDataPacket[]>(
        `SELECT u.*, a.username as agent_username, c.name as club_name
         FROM users u
         LEFT JOIN agents a ON u.agent_id = a.id
         LEFT JOIN clubs c ON u.club_id = c.id
         WHERE u.id = ?`,
        [userId]
      );

      if (rows.length === 0) {
        return null;
      }

      return rows[0];
    } catch (error) {
      console.error('获取用户详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户详细信息（包含交易历史和游戏记录）
   */
  static async getUserDetailById(userId: string) {
    try {
      // 获取用户基本信息
      const [userRows] = await pool.execute<RowDataPacket[]>(
        `SELECT u.*, a.username as agent_username, c.name as club_name
         FROM users u
         LEFT JOIN agents a ON u.agent_id = a.id
         LEFT JOIN clubs c ON u.club_id = c.id
         WHERE u.id = ?`,
        [userId]
      );

      if (userRows.length === 0) {
        return null;
      }

      const user = userRows[0];

      // 获取最近的交易记录
      const [transactions] = await pool.execute<RowDataPacket[]>(
        `SELECT * FROM transactions
         WHERE user_id = ?
         ORDER BY created_at DESC
         LIMIT 10`,
        [userId]
      );

      // 获取最近的游戏记录
      const [gameParticipations] = await pool.execute<RowDataPacket[]>(
        `SELECT gp.*, gr.game_type, gr.started_at, gr.total_pot
         FROM game_participations gp
         LEFT JOIN game_records gr ON gp.game_record_id = gr.id
         WHERE gp.user_id = ?
         ORDER BY gr.started_at DESC
         LIMIT 10`,
        [userId]
      );

      // 获取统计数据
      const [statsRows] = await pool.execute<RowDataPacket[]>(
        `SELECT
           COUNT(DISTINCT gp.game_record_id) as total_games,
           COALESCE(SUM(gp.bet_amount), 0) as total_bet,
           COALESCE(SUM(gp.win_amount), 0) as total_win,
           COALESCE(SUM(CASE WHEN gp.is_winner = 1 THEN 1 ELSE 0 END), 0) as win_count
         FROM game_participations gp
         WHERE gp.user_id = ?`,
        [userId]
      );

      const stats = statsRows[0];

      return {
        user,
        transactions,
        gameParticipations,
        stats: {
          totalGames: stats.total_games,
          totalBet: parseFloat(stats.total_bet),
          totalWin: parseFloat(stats.total_win),
          winCount: stats.win_count,
          winRate: stats.total_games > 0 ? (stats.win_count / stats.total_games * 100).toFixed(2) : '0.00'
        }
      };
    } catch (error) {
      console.error('获取用户详细信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户状态
   */
  static async updateUserStatus(userId: string, status: string) {
    try {
      await pool.execute(
        'UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?',
        [status, userId]
      );
    } catch (error) {
      console.error('更新用户状态失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户余额
   */
  static async updateUserBalance(userId: string, amount: number, type: 'add' | 'subtract', description: string) {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // 获取当前余额
      const [userRows] = await connection.execute<RowDataPacket[]>(
        'SELECT balance FROM users WHERE id = ?',
        [userId]
      );

      if (userRows.length === 0) {
        throw new Error('用户不存在');
      }

      const currentBalance = parseFloat(userRows[0].balance);
      const newBalance = type === 'add' ? currentBalance + amount : currentBalance - amount;

      if (newBalance < 0) {
        throw new Error('余额不足');
      }

      // 更新用户余额
      await connection.execute(
        'UPDATE users SET balance = ?, updated_at = NOW() WHERE id = ?',
        [newBalance, userId]
      );

      // 记录交易
      await connection.execute(
        `INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, description, status)
         VALUES (?, ?, ?, ?, ?, ?, 'completed')`,
        [userId, type === 'add' ? 'deposit' : 'withdraw', Math.abs(amount), currentBalance, newBalance, description]
      );

      await connection.commit();
    } catch (error) {
      await connection.rollback();
      console.error('更新用户余额失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取代理列表
   */
  static async getAgents(page: number = 1, limit: number = 20, search?: string, status?: string) {
    try {
      const offset = (page - 1) * limit;
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (search) {
        whereClause += ' AND (a.username LIKE ? OR a.email LIKE ?)';
        const searchPattern = `%${search}%`;
        params.push(searchPattern, searchPattern);
      }

      if (status) {
        whereClause += ' AND a.status = ?';
        params.push(status);
      }

      // 获取总数
      const [countResult] = await pool.execute<RowDataPacket[]>(
        `SELECT COUNT(*) as total FROM agents a ${whereClause}`,
        params
      );
      const total = countResult[0].total;

      // 获取代理列表
      const [agents] = await pool.execute<RowDataPacket[]>(
        `SELECT a.id, a.username, a.email, a.phone, a.balance, a.commission, a.status, a.last_login_at, a.created_at,
                COUNT(DISTINCT u.id) as user_count, COUNT(DISTINCT c.id) as club_count
         FROM agents a
         LEFT JOIN users u ON a.id = u.agent_id
         LEFT JOIN clubs c ON a.id = c.agent_id
         ${whereClause}
         GROUP BY a.id, a.username, a.email, a.phone, a.balance, a.commission, a.status, a.last_login_at, a.created_at
         ORDER BY a.created_at DESC
         LIMIT ${limit} OFFSET ${offset}`,
        params
      );

      return {
        agents,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('获取代理列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取代理详情
   */
  static async getAgentById(agentId: string) {
    try {
      const [rows] = await pool.execute<RowDataPacket[]>(
        `SELECT a.*, COUNT(u.id) as user_count, COUNT(c.id) as club_count
         FROM agents a
         LEFT JOIN users u ON a.id = u.agent_id
         LEFT JOIN clubs c ON a.id = c.agent_id
         WHERE a.id = ?
         GROUP BY a.id`,
        [agentId]
      );

      if (rows.length === 0) {
        return null;
      }

      return rows[0];
    } catch (error) {
      console.error('获取代理详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取代理详细信息（包含用户列表和统计）
   */
  static async getAgentDetailById(agentId: string) {
    try {
      // 获取代理基本信息
      const [agentRows] = await pool.execute<RowDataPacket[]>(
        `SELECT a.*, COUNT(DISTINCT u.id) as user_count, COUNT(DISTINCT c.id) as club_count
         FROM agents a
         LEFT JOIN users u ON a.id = u.agent_id
         LEFT JOIN clubs c ON a.id = c.agent_id
         WHERE a.id = ?
         GROUP BY a.id`,
        [agentId]
      );

      if (agentRows.length === 0) {
        return null;
      }

      const agent = agentRows[0];

      // 获取代理下的用户列表（最近10个）
      const [userRows] = await pool.execute<RowDataPacket[]>(
        `SELECT u.id, u.username, u.email, u.balance, u.status, u.last_login_at, u.created_at
         FROM users u
         WHERE u.agent_id = ?
         ORDER BY u.created_at DESC
         LIMIT 10`,
        [agentId]
      );

      // 获取代理的交易统计
      const [transactionStats] = await pool.execute<RowDataPacket[]>(
        `SELECT
           COUNT(*) as total_transactions,
           COALESCE(SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END), 0) as total_income,
           COALESCE(SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END), 0) as total_expense
         FROM transactions
         WHERE agent_id = ?`,
        [agentId]
      );

      // 获取代理的俱乐部信息
      const [clubRows] = await pool.execute<RowDataPacket[]>(
        `SELECT c.id, c.name, c.description, c.current_members, c.max_members, c.status
         FROM clubs c
         WHERE c.agent_id = ?`,
        [agentId]
      );

      return {
        agent,
        users: userRows,
        clubs: clubRows,
        stats: transactionStats[0] || { total_transactions: 0, total_income: 0, total_expense: 0 }
      };
    } catch (error) {
      console.error('获取代理详细信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新代理信息
   */
  static async updateAgent(agentId: string, updateData: any) {
    try {
      const { username, email, phone, commission } = updateData;

      const updateFields = [];
      const updateValues = [];

      if (username) {
        updateFields.push('username = ?');
        updateValues.push(username);
      }
      if (email) {
        updateFields.push('email = ?');
        updateValues.push(email);
      }
      if (phone) {
        updateFields.push('phone = ?');
        updateValues.push(phone);
      }
      if (commission !== undefined) {
        updateFields.push('commission = ?');
        updateValues.push(commission);
      }

      if (updateFields.length === 0) {
        throw new Error('没有要更新的字段');
      }

      updateFields.push('updated_at = NOW()');
      updateValues.push(agentId);

      await pool.execute(
        `UPDATE agents SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );
    } catch (error) {
      console.error('更新代理信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新代理状态
   */
  static async updateAgentStatus(agentId: string, status: string) {
    try {
      await pool.execute(
        'UPDATE agents SET status = ?, updated_at = NOW() WHERE id = ?',
        [status, agentId]
      );
    } catch (error) {
      console.error('更新代理状态失败:', error);
      throw error;
    }
  }

  /**
   * 更新代理密码
   */
  static async updateAgentPassword(agentId: string, password: string) {
    try {
      const bcrypt = require('bcryptjs');
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      await pool.execute(
        'UPDATE agents SET password_hash = ?, updated_at = NOW() WHERE id = ?',
        [passwordHash, agentId]
      );
    } catch (error) {
      console.error('更新代理密码失败:', error);
      throw error;
    }
  }

  /**
   * 更新代理余额
   */
  static async updateAgentBalance(agentId: string, amount: number, type: string = 'adjustment', description: string = '') {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // 获取当前余额
      const [agentRows] = await connection.execute<RowDataPacket[]>(
        'SELECT balance FROM agents WHERE id = ?',
        [agentId]
      );

      if (agentRows.length === 0) {
        throw new Error('代理不存在');
      }

      const currentBalance = parseFloat(agentRows[0].balance);
      const newBalance = currentBalance + amount;

      if (newBalance < 0) {
        throw new Error('余额不足');
      }

      // 更新代理余额
      await connection.execute(
        'UPDATE agents SET balance = ?, updated_at = NOW() WHERE id = ?',
        [newBalance, agentId]
      );

      // 创建交易记录
      const transactionId = require('crypto').randomUUID();
      await connection.execute(
        `INSERT INTO transactions (id, agent_id, type, amount, balance_before, balance_after, description, status, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, 'completed', NOW())`,
        [transactionId, agentId, type, amount, currentBalance, newBalance, description || `管理员${amount > 0 ? '增加' : '减少'}代理余额`]
      );

      await connection.commit();
    } catch (error) {
      await connection.rollback();
      console.error('更新代理余额失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取代理下的用户列表
   */
  static async getAgentUsers(agentId: string, page: number = 1, limit: number = 20) {
    try {
      const offset = (page - 1) * limit;

      // 获取总数
      const [countRows] = await pool.execute<RowDataPacket[]>(
        'SELECT COUNT(*) as total FROM users WHERE agent_id = ?',
        [agentId]
      );
      const total = countRows[0].total;

      // 获取用户列表
      const [userRows] = await pool.execute<RowDataPacket[]>(
        `SELECT u.id, u.username, u.email, u.balance, u.status, u.last_login_at, u.created_at,
                c.name as club_name
         FROM users u
         LEFT JOIN clubs c ON u.club_id = c.id
         WHERE u.agent_id = ?
         ORDER BY u.created_at DESC
         LIMIT ${limit} OFFSET ${offset}`,
        [agentId]
      );

      return {
        users: userRows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('获取代理用户列表失败:', error);
      throw error;
    }
  }

  /**
   * 创建代理
   */
  static async createAgent(agentData: {
    username: string;
    password: string;
    commission: number;
    clubName: string;
    clubDescription?: string;
  }) {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      const { username, password, commission, clubName, clubDescription } = agentData;

      // 检查用户名是否已存在
      const [existingAgents] = await connection.execute<RowDataPacket[]>(
        'SELECT id FROM agents WHERE username = ?',
        [username]
      );

      if (existingAgents.length > 0) {
        throw new Error('用户名已存在');
      }

      // 加密密码
      const bcrypt = require('bcryptjs');
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // 生成代理ID
      const agentId = require('uuid').v4();

      // 创建代理 - 生成一个临时邮箱
      const tempEmail = `${username}@agent.local`;
      await connection.execute(
        `INSERT INTO agents (id, username, email, password_hash, commission, status)
         VALUES (?, ?, ?, ?, ?, 'active')`,
        [agentId, username, tempEmail, passwordHash, commission]
      );

      // 创建俱乐部
      const clubId = require('uuid').v4();
      await connection.execute(
        `INSERT INTO clubs (id, name, description, agent_id, status)
         VALUES (?, ?, ?, ?, 'active')`,
        [clubId, clubName, clubDescription || '', agentId]
      );

      await connection.commit();

      // 获取创建的代理信息
      const [agents] = await pool.execute<RowDataPacket[]>(
        `SELECT a.id, a.username, a.commission, a.balance, a.status, a.created_at,
                c.name as club_name, c.description as club_description
         FROM agents a
         LEFT JOIN clubs c ON a.id = c.agent_id
         WHERE a.id = ?`,
        [agentId]
      );

      return agents[0];
    } catch (error) {
      await connection.rollback();
      console.error('创建代理失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }



  /**
   * 根据ID获取游戏记录详情
   */
  static async getGameRecordById(gameId: string) {
    try {
      const [rows] = await pool.execute<RowDataPacket[]>(
        'SELECT * FROM game_records WHERE id = ?',
        [gameId]
      );

      if (rows.length === 0) {
        return null;
      }

      const gameRecord = rows[0];

      // 获取参与者详情
      const [participations] = await pool.execute<RowDataPacket[]>(
        `SELECT gp.*, u.username
         FROM game_participations gp
         LEFT JOIN users u ON gp.user_id = u.id
         WHERE gp.game_record_id = ?
         ORDER BY gp.seat_position`,
        [gameId]
      );

      // 获取旁注记录
      const [sideWagers] = await pool.execute<RowDataPacket[]>(
        `SELECT sw.*, u.username
         FROM side_wagers sw
         LEFT JOIN users u ON sw.user_id = u.id
         WHERE sw.game_record_id = ?`,
        [gameId]
      );

      return {
        ...gameRecord,
        participations,
        sideWagers
      };
    } catch (error) {
      console.error('获取游戏记录详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取交易记录列表
   */
  static async getTransactions(page: number = 1, limit: number = 20, type?: string, status?: string, startDate?: string, endDate?: string) {
    try {
      const offset = (page - 1) * limit;
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (type) {
        whereClause += ' AND type = ?';
        params.push(type);
      }

      if (status) {
        whereClause += ' AND status = ?';
        params.push(status);
      }

      if (startDate) {
        whereClause += ' AND DATE(created_at) >= ?';
        params.push(startDate);
      }

      if (endDate) {
        whereClause += ' AND DATE(created_at) <= ?';
        params.push(endDate);
      }

      // 获取总数
      const [countResult] = await pool.execute<RowDataPacket[]>(
        `SELECT COUNT(*) as total FROM transactions ${whereClause}`,
        params
      );
      const total = countResult[0].total;

      // 获取交易记录列表
      const [transactions] = await pool.execute<RowDataPacket[]>(
        `SELECT t.id, t.type, t.amount, t.balance_before, t.balance_after, t.description, t.status, t.created_at,
                u.username as user_username, a.username as agent_username
         FROM transactions t
         LEFT JOIN users u ON t.user_id = u.id
         LEFT JOIN agents a ON t.agent_id = a.id
         ${whereClause}
         ORDER BY t.created_at DESC
         LIMIT ${limit} OFFSET ${offset}`,
        params
      );

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('获取交易记录列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取交易记录详情
   */
  static async getTransactionById(transactionId: string) {
    try {
      const [rows] = await pool.execute<RowDataPacket[]>(
        `SELECT t.*, u.username as user_username, a.username as agent_username
         FROM transactions t
         LEFT JOIN users u ON t.user_id = u.id
         LEFT JOIN agents a ON t.agent_id = a.id
         WHERE t.id = ?`,
        [transactionId]
      );

      if (rows.length === 0) {
        return null;
      }

      return rows[0];
    } catch (error) {
      console.error('获取交易记录详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取游戏记录列表
   */
  static async getGameRecords(page: number = 1, limit: number = 20, gameType?: string, startDate?: string, endDate?: string) {
    try {
      const offset = (page - 1) * limit;
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (gameType) {
        whereClause += ' AND gr.game_type = ?';
        params.push(gameType);
      }

      if (startDate) {
        whereClause += ' AND DATE(gr.started_at) >= ?';
        params.push(startDate);
      }

      if (endDate) {
        whereClause += ' AND DATE(gr.started_at) <= ?';
        params.push(endDate);
      }

      // 获取总数
      const [countResult] = await pool.execute<RowDataPacket[]>(
        `SELECT COUNT(*) as total FROM game_records gr ${whereClause}`,
        params
      );
      const total = countResult[0].total;

      // 获取游戏记录列表
      const [games] = await pool.execute<RowDataPacket[]>(
        `SELECT gr.id, gr.game_type, gr.room_id, gr.round_number, gr.total_pot, gr.started_at, gr.ended_at,
                (SELECT COUNT(*) FROM game_participations gp WHERE gp.game_record_id = gr.id) as player_count
         FROM game_records gr
         ${whereClause}
         ORDER BY gr.started_at DESC
         LIMIT ${limit} OFFSET ${offset}`,
        params
      );

      return {
        gameRecords: games,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('获取游戏记录列表失败:', error);
      throw error;
    }
  }

  /**
   * 哈希令牌（用于存储）
   */
  private static hashToken(token: string): string {
    return bcrypt.hashSync(token, 10);
  }
}
