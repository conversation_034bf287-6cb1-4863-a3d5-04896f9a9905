import bcrypt from 'bcryptjs';
import { pool } from '../config/database';
import { generateTokenPair } from '../config/jwt';
import { generateId } from '@games/shared';
import { AppError } from '../middleware/error';

export interface CreateUserData {
  username: string;
  password: string;
  agentId?: string;
}

export interface LoginData {
  username: string;
  password: string;
}

export class UserService {
  /**
   * 创建新用户
   */
  static async createUser(userData: CreateUserData) {
    const { username, password, agentId } = userData;

    // 检查用户名是否已存在
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    if (Array.isArray(existingUsers) && existingUsers.length > 0) {
      throw new AppError('用户名已存在', 409);
    }

    // 验证代理ID是否存在
    if (agentId) {
      const [agents] = await pool.execute(
        'SELECT id FROM agents WHERE id = ? AND status = "active"',
        [agentId]
      );

      if (!Array.isArray(agents) || agents.length === 0) {
        throw new AppError('无效的代理ID', 400);
      }
    }

    // 加密密码
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const userId = generateId();
    await pool.execute(
      `INSERT INTO users (id, username, password_hash, agent_id)
       VALUES (?, ?, ?, ?)`,
      [userId, username, passwordHash, agentId || null]
    );

    // 获取创建的用户信息
    const [users] = await pool.execute(
      `SELECT id, username, balance, agent_id, status, created_at
       FROM users WHERE id = ?`,
      [userId]
    );

    return Array.isArray(users) ? users[0] : null;
  }
  
  /**
   * 用户登录
   */
  static async loginUser(loginData: LoginData, ipAddress?: string, userAgent?: string) {
    const { username, password } = loginData;

    // 查找用户
    const [users] = await pool.execute(
      `SELECT id, username, password_hash, balance, agent_id, status
       FROM users WHERE username = ?`,
      [username]
    );

    if (!Array.isArray(users) || users.length === 0) {
      throw new AppError('用户名或密码错误', 401);
    }

    const user = users[0] as any;

    // 检查用户状态
    if (user.status !== 'active') {
      throw new AppError('账户已被禁用或封禁', 403);
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      throw new AppError('用户名或密码错误', 401);
    }
    
    // 生成令牌
    const tokens = generateTokenPair(user.id, 'user', user.username);
    
    // 计算过期时间
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30); // 30天后过期
    
    // 保存会话
    await pool.execute(
      `INSERT INTO user_sessions (id, user_id, user_type, token_hash, refresh_token_hash, ip_address, user_agent, expires_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        tokens.sessionId,
        user.id,
        'user',
        this.hashToken(tokens.accessToken),
        this.hashToken(tokens.refreshToken),
        ipAddress || null,
        userAgent || null,
        expiresAt
      ]
    );
    
    // 更新最后登录时间
    await pool.execute(
      'UPDATE users SET last_login_at = NOW() WHERE id = ?',
      [user.id]
    );
    
    return {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        balance: user.balance,
        agentId: user.agent_id
      },
      tokens
    };
  }
  
  /**
   * 获取用户信息
   */
  static async getUserById(userId: string) {
    const [users] = await pool.execute(
      `SELECT id, username, email, phone, balance, agent_id, club_id, status, created_at, updated_at
       FROM users WHERE id = ?`,
      [userId]
    );
    
    return Array.isArray(users) && users.length > 0 ? users[0] : null;
  }
  
  /**
   * 更新用户余额
   */
  static async updateUserBalance(userId: string, amount: number, description?: string) {
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 获取当前余额
      const [users] = await connection.execute(
        'SELECT balance FROM users WHERE id = ? FOR UPDATE',
        [userId]
      );
      
      if (!Array.isArray(users) || users.length === 0) {
        throw new AppError('User not found', 404);
      }
      
      const currentBalance = (users[0] as any).balance;
      const newBalance = currentBalance + amount;
      
      if (newBalance < 0) {
        throw new AppError('Insufficient balance', 400);
      }
      
      // 更新余额
      await connection.execute(
        'UPDATE users SET balance = ? WHERE id = ?',
        [newBalance, userId]
      );
      
      // 记录交易
      const transactionId = generateId();
      const transactionType = amount > 0 ? 'deposit' : 'withdraw';
      
      await connection.execute(
        `INSERT INTO transactions (id, user_id, type, amount, balance_before, balance_after, description)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [transactionId, userId, transactionType, Math.abs(amount), currentBalance, newBalance, description || '']
      );
      
      await connection.commit();
      
      return {
        previousBalance: currentBalance,
        newBalance,
        transactionId
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
  
  /**
   * 用户登出
   */
  static async logoutUser(sessionId: string) {
    await pool.execute(
      'DELETE FROM user_sessions WHERE id = ?',
      [sessionId]
    );
  }
  
  /**
   * 用户加入俱乐部
   */
  static async joinClub(userId: string, agentId: string) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // 检查用户是否已经有代理
      const [users] = await connection.execute(
        'SELECT agent_id, club_id FROM users WHERE id = ?',
        [userId]
      );

      if (!Array.isArray(users) || users.length === 0) {
        throw new AppError('User not found', 404);
      }

      const user = users[0] as any;
      if (user.agent_id) {
        throw new AppError('User already belongs to a club', 400);
      }

      // 检查代理和俱乐部是否存在且活跃
      const [clubs] = await connection.execute(
        `SELECT c.id, c.current_members, c.max_members, c.status, a.status as agent_status
         FROM clubs c
         JOIN agents a ON c.agent_id = a.id
         WHERE c.agent_id = ?`,
        [agentId]
      );

      if (!Array.isArray(clubs) || clubs.length === 0) {
        throw new AppError('Club not found', 404);
      }

      const club = clubs[0] as any;
      if (club.status !== 'active' || club.agent_status !== 'active') {
        throw new AppError('Club or agent is not active', 400);
      }

      if (club.current_members >= club.max_members) {
        throw new AppError('Club is full', 400);
      }

      // 更新用户的代理和俱乐部信息
      await connection.execute(
        'UPDATE users SET agent_id = ?, club_id = ? WHERE id = ?',
        [agentId, club.id, userId]
      );

      // 更新俱乐部成员数量
      await connection.execute(
        'UPDATE clubs SET current_members = current_members + 1 WHERE id = ?',
        [club.id]
      );

      await connection.commit();

      return {
        clubId: club.id,
        agentId: agentId
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 用户离开俱乐部
   */
  static async leaveClub(userId: string) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // 获取用户当前的俱乐部信息
      const [users] = await connection.execute(
        'SELECT agent_id, club_id FROM users WHERE id = ?',
        [userId]
      );

      if (!Array.isArray(users) || users.length === 0) {
        throw new AppError('User not found', 404);
      }

      const user = users[0] as any;
      if (!user.club_id) {
        throw new AppError('User is not in any club', 400);
      }

      // 更新用户信息
      await connection.execute(
        'UPDATE users SET agent_id = NULL, club_id = NULL WHERE id = ?',
        [userId]
      );

      // 更新俱乐部成员数量
      await connection.execute(
        'UPDATE clubs SET current_members = current_members - 1 WHERE id = ?',
        [user.club_id]
      );

      await connection.commit();

      return true;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取可用的俱乐部列表
   */
  static async getAvailableClubs(page: number = 1, limit: number = 20) {
    const offset = (page - 1) * limit;

    // 获取俱乐部列表
    const [clubs] = await pool.execute(
      `SELECT c.id, c.name, c.description, c.current_members, c.max_members, c.created_at,
              a.username as agent_username
       FROM clubs c
       JOIN agents a ON c.agent_id = a.id
       WHERE c.status = 'active' AND a.status = 'active' AND c.current_members < c.max_members
       ORDER BY c.created_at DESC
       LIMIT ? OFFSET ?`,
      [limit, offset]
    );

    // 获取总数
    const [countResult] = await pool.execute(
      `SELECT COUNT(*) as total
       FROM clubs c
       JOIN agents a ON c.agent_id = a.id
       WHERE c.status = 'active' AND a.status = 'active' AND c.current_members < c.max_members`
    );

    const total = Array.isArray(countResult) ? (countResult[0] as any).total : 0;

    return {
      clubs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * 获取用户俱乐部状态
   */
  static async getUserClubStatus(userId: string) {
    // 检查用户是否已经是俱乐部成员
    const [userClubs] = await pool.execute(
      `SELECT uc.club_id, c.name as club_name, c.club_code
       FROM user_clubs uc
       JOIN clubs c ON uc.club_id = c.id
       WHERE uc.user_id = ? AND uc.status = 'active'`,
      [userId]
    );

    if (Array.isArray(userClubs) && userClubs.length > 0) {
      const clubInfo = userClubs[0] as any;
      return {
        isMember: true,
        hasPendingApplication: false,
        clubInfo: {
          id: clubInfo.club_id,
          name: clubInfo.club_name,
          clubCode: clubInfo.club_code
        }
      };
    }

    // 检查是否有待审核的申请
    const [pendingApplications] = await pool.execute(
      `SELECT id FROM club_applications
       WHERE user_id = ? AND status = 'pending'`,
      [userId]
    );

    return {
      isMember: false,
      hasPendingApplication: Array.isArray(pendingApplications) && pendingApplications.length > 0,
      clubInfo: null
    };
  }

  /**
   * 获取用户的俱乐部申请记录
   */
  static async getUserClubApplications(userId: string) {
    const [applications] = await pool.execute(
      `SELECT id, club_code, status, applied_at, processed_at, rejection_reason, notes
       FROM club_applications
       WHERE user_id = ?
       ORDER BY applied_at DESC`,
      [userId]
    );

    return Array.isArray(applications) ? applications : [];
  }

  /**
   * 申请加入俱乐部
   */
  static async applyToJoinClub(userId: string, clubCode: string) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // 查找俱乐部
      const [clubs] = await connection.execute(
        `SELECT id, agent_id, name, auto_join, join_approval_required, current_members, max_members
         FROM clubs WHERE club_code = ? AND status = 'active'`,
        [clubCode]
      );

      if (!Array.isArray(clubs) || clubs.length === 0) {
        throw new AppError('俱乐部代码不存在或已停用', 404);
      }

      const club = clubs[0] as any;

      // 检查俱乐部是否已满员
      if (club.current_members >= club.max_members) {
        throw new AppError('俱乐部已满员', 400);
      }

      // 检查用户是否已经是俱乐部成员
      const [existingMembers] = await connection.execute(
        'SELECT id FROM user_clubs WHERE user_id = ? AND club_id = ? AND status = "active"',
        [userId, club.id]
      );

      if (Array.isArray(existingMembers) && existingMembers.length > 0) {
        throw new AppError('您已经是该俱乐部的成员', 400);
      }

      // 检查是否有待审核的申请
      const [pendingApplications] = await connection.execute(
        'SELECT id FROM club_applications WHERE user_id = ? AND club_id = ? AND status = "pending"',
        [userId, club.id]
      );

      if (Array.isArray(pendingApplications) && pendingApplications.length > 0) {
        throw new AppError('您已有待审核的申请，请勿重复申请', 400);
      }

      // 创建申请记录
      const applicationId = generateId();
      await connection.execute(
        `INSERT INTO club_applications (id, user_id, club_id, club_code, status, applied_at)
         VALUES (?, ?, ?, ?, 'pending', NOW())`,
        [applicationId, userId, club.id, clubCode]
      );

      let autoJoined = false;

      // 如果俱乐部设置为自动加入
      if (club.auto_join && !club.join_approval_required) {
        // 自动批准申请
        await connection.execute(
          `UPDATE club_applications
           SET status = 'approved', processed_at = NOW(), processed_by = ?
           WHERE id = ?`,
          [club.agent_id, applicationId]
        );

        // 添加用户到俱乐部
        await connection.execute(
          `INSERT INTO user_clubs (user_id, club_id, joined_at, status, is_primary)
           VALUES (?, ?, NOW(), 'active', TRUE)`,
          [userId, club.id]
        );

        // 更新用户的club_id（保持向后兼容）
        await connection.execute(
          'UPDATE users SET club_id = ? WHERE id = ?',
          [club.id, userId]
        );

        autoJoined = true;
      }

      await connection.commit();

      return {
        autoJoined,
        applicationId
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 哈希令牌（用于存储）
   */
  private static hashToken(token: string): string {
    return bcrypt.hashSync(token, 10);
  }
}
