import { Request, Response } from 'express';
import { UserService } from '../services/userService';
import { ApiResponse } from '@games/shared';
import { asyncHandler } from '../middleware/error';

export class AuthController {
  /**
   * 用户注册
   */
  static register = asyncHandler(async (req: Request, res: Response) => {
    const { username, password, agentCode } = req.body;

    // 验证必填字段
    if (!username || !password) {
      const response: ApiResponse = {
        success: false,
        error: '用户名和密码不能为空'
      };
      return res.status(400).json(response);
    }

    // 如果提供了代理码，查找对应的代理ID
    let agentId: string | undefined;
    if (agentCode) {
      // 这里可以实现代理码到代理ID的转换逻辑
      // 暂时跳过，后续实现
    }

    const user = await UserService.createUser({
      username,
      password,
      agentId
    });

    // 注册成功后自动登录
    const loginResult = await UserService.loginUser(
      { username, password },
      req.ip,
      req.get('User-Agent')
    );

    const response: ApiResponse = {
      success: true,
      data: {
        user: {
          id: (user as any).id,
          username: (user as any).username,
          balance: (user as any).balance
        },
        tokens: {
          accessToken: loginResult.tokens.accessToken,
          refreshToken: loginResult.tokens.refreshToken
        }
      },
      message: '注册成功'
    };

    res.status(201).json(response);
  });
  
  /**
   * 用户登录
   */
  static login = asyncHandler(async (req: Request, res: Response) => {
    const { username, password } = req.body;
    const ipAddress = req.ip;
    const userAgent = req.get('User-Agent');

    // 验证必填字段
    if (!username || !password) {
      const response: ApiResponse = {
        success: false,
        error: '用户名和密码不能为空'
      };
      return res.status(400).json(response);
    }

    const result = await UserService.loginUser(
      { username, password },
      ipAddress,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      data: result,
      message: '登录成功'
    };

    res.json(response);
  });
  
  /**
   * 用户登出
   */
  static logout = asyncHandler(async (req: Request, res: Response) => {
    const sessionId = req.user?.sessionId;
    
    if (sessionId) {
      await UserService.logoutUser(sessionId);
    }
    
    const response: ApiResponse = {
      success: true,
      message: 'Logout successful'
    };
    
    res.json(response);
  });
  
  /**
   * 获取当前用户信息
   */
  static profile = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    
    if (!userId) {
      const response: ApiResponse = {
        success: false,
        error: 'User not authenticated'
      };
      return res.status(401).json(response);
    }
    
    const user = await UserService.getUserById(userId);
    
    if (!user) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      return res.status(404).json(response);
    }
    
    const response: ApiResponse = {
      success: true,
      data: { user }
    };
    
    res.json(response);
  });
  
  /**
   * 刷新令牌
   */
  static refresh = asyncHandler(async (req: Request, res: Response) => {
    // TODO: 实现刷新令牌逻辑
    const response: ApiResponse = {
      success: false,
      error: 'Refresh token not implemented yet'
    };
    
    res.status(501).json(response);
  });
}
