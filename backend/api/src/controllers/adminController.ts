import { Request, Response } from 'express';
import { AdminService } from '../services/adminService';
import { ApiResponse } from '@games/shared';

export class AdminController {
  /**
   * 管理员登录
   */
  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { username, password } = req.body;
      const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';

      const result = await AdminService.loginAdmin(username, password, clientIp, userAgent);

      const response: ApiResponse = {
        success: true,
        data: result,
        message: '登录成功'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '登录失败'
      };

      res.status(401).json(response);
    }
  }

  /**
   * 管理员登出
   */
  static async logout(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user;
      
      if (user && user.sessionId) {
        await AdminService.logoutAdmin(user.sessionId);
      }

      const response: ApiResponse = {
        success: true,
        message: '登出成功'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '登出失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 获取管理员信息
   */
  static async profile(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user;
      const adminInfo = await AdminService.getAdminById(user.userId);

      if (!adminInfo) {
        const response: ApiResponse = {
          success: false,
          error: '管理员不存在'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: {
          admin: adminInfo
        }
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取管理员信息失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 获取仪表板统计数据
   */
  static async getDashboardStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await AdminService.getDashboardStats();

      const response: ApiResponse = {
        success: true,
        data: stats
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取统计数据失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 获取用户列表
   */
  static async getUsers(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const search = req.query.search as string;
      const status = req.query.status as string;

      const result = await AdminService.getUsers(page, limit, search, status);

      const response: ApiResponse = {
        success: true,
        data: result
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取用户列表失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 根据ID获取用户详情
   */
  static async getUserById(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.params.id;
      const user = await AdminService.getUserById(userId);

      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: '用户不存在'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: { user }
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取用户详情失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 获取用户详细信息（包含交易历史和游戏记录）
   */
  static async getUserDetail(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.params.id;
      const userDetail = await AdminService.getUserDetailById(userId);

      if (!userDetail) {
        const response: ApiResponse = {
          success: false,
          error: '用户不存在'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: userDetail
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取用户详细信息失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 更新用户状态
   */
  static async updateUserStatus(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.params.id;
      const { status } = req.body;

      await AdminService.updateUserStatus(userId, status);

      const response: ApiResponse = {
        success: true,
        message: '用户状态更新成功'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '更新用户状态失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 更新用户余额
   */
  static async updateUserBalance(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.params.id;
      const { amount, type, description } = req.body;

      await AdminService.updateUserBalance(userId, amount, type, description);

      const response: ApiResponse = {
        success: true,
        message: '用户余额更新成功'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '更新用户余额失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 获取代理列表
   */
  static async getAgents(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const search = req.query.search as string;
      const status = req.query.status as string;

      const result = await AdminService.getAgents(page, limit, search, status);

      const response: ApiResponse = {
        success: true,
        data: result
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取代理列表失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 根据ID获取代理详情
   */
  static async getAgentById(req: Request, res: Response): Promise<void> {
    try {
      const agentId = req.params.id;
      const agent = await AdminService.getAgentById(agentId);

      if (!agent) {
        const response: ApiResponse = {
          success: false,
          error: '代理不存在'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: { agent }
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取代理详情失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 获取代理详细信息（包含用户列表和统计）
   */
  static async getAgentDetail(req: Request, res: Response): Promise<void> {
    try {
      const agentId = req.params.id;
      const agentDetail = await AdminService.getAgentDetailById(agentId);

      if (!agentDetail) {
        const response: ApiResponse = {
          success: false,
          error: '代理不存在'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: agentDetail
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取代理详细信息失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 更新代理信息
   */
  static async updateAgent(req: Request, res: Response): Promise<void> {
    try {
      const agentId = req.params.id;
      const updateData = req.body;

      await AdminService.updateAgent(agentId, updateData);

      const response: ApiResponse = {
        success: true,
        message: '代理信息更新成功'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '更新代理信息失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 更新代理状态
   */
  static async updateAgentStatus(req: Request, res: Response): Promise<void> {
    try {
      const agentId = req.params.id;
      const { status } = req.body;

      await AdminService.updateAgentStatus(agentId, status);

      const response: ApiResponse = {
        success: true,
        message: '代理状态更新成功'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '更新代理状态失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 更新代理密码
   */
  static async updateAgentPassword(req: Request, res: Response): Promise<void> {
    try {
      const agentId = req.params.id;
      const { password } = req.body;

      if (!password) {
        const response: ApiResponse = {
          success: false,
          error: '密码不能为空'
        };
        res.status(400).json(response);
        return;
      }

      await AdminService.updateAgentPassword(agentId, password);

      const response: ApiResponse = {
        success: true,
        message: '代理密码更新成功'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '更新代理密码失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 更新代理余额
   */
  static async updateAgentBalance(req: Request, res: Response): Promise<void> {
    try {
      const agentId = req.params.id;
      const { amount, type, description } = req.body;

      if (typeof amount !== 'number' || amount <= 0) {
        const response: ApiResponse = {
          success: false,
          error: '金额必须是大于0的数字'
        };
        res.status(400).json(response);
        return;
      }

      // 根据type确定实际的金额（正数或负数）
      const actualAmount = type === 'subtract' ? -amount : amount;
      const transactionType = type === 'subtract' ? 'deduct' : 'deposit';

      await AdminService.updateAgentBalance(agentId, actualAmount, transactionType, description);

      const response: ApiResponse = {
        success: true,
        message: '代理余额更新成功'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '更新代理余额失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 获取代理下的用户列表
   */
  static async getAgentUsers(req: Request, res: Response): Promise<void> {
    try {
      const agentId = req.params.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      const result = await AdminService.getAgentUsers(agentId, page, limit);

      const response: ApiResponse = {
        success: true,
        data: result
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取代理用户列表失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 创建代理
   */
  static async createAgent(req: Request, res: Response): Promise<void> {
    try {
      const { username, password, commission, clubName, clubDescription } = req.body;

      // 验证必填字段
      if (!username || !password || !clubName) {
        const response: ApiResponse = {
          success: false,
          error: '用户名、密码和俱乐部名称不能为空'
        };
        res.status(400).json(response);
        return;
      }

      const agent = await AdminService.createAgent({
        username,
        password,
        commission: commission || 0.1, // 默认10%佣金
        clubName,
        clubDescription
      });

      const response: ApiResponse = {
        success: true,
        data: { agent },
        message: '代理创建成功'
      };

      res.status(201).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '创建代理失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 获取游戏记录列表
   */
  static async getGameRecords(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const gameType = req.query.gameType as string;
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      const result = await AdminService.getGameRecords(page, limit, gameType, startDate, endDate);

      const response: ApiResponse = {
        success: true,
        data: result
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取游戏记录失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 根据ID获取游戏记录详情
   */
  static async getGameRecordById(req: Request, res: Response): Promise<void> {
    try {
      const gameId = req.params.id;
      const gameRecord = await AdminService.getGameRecordById(gameId);

      if (!gameRecord) {
        const response: ApiResponse = {
          success: false,
          error: '游戏记录不存在'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: { gameRecord }
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取游戏记录详情失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 获取交易记录列表
   */
  static async getTransactions(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const type = req.query.type as string;
      const status = req.query.status as string;
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      const result = await AdminService.getTransactions(page, limit, type, status, startDate, endDate);

      const response: ApiResponse = {
        success: true,
        data: result
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取交易记录失败'
      };

      res.status(500).json(response);
    }
  }

  /**
   * 根据ID获取交易记录详情
   */
  static async getTransactionById(req: Request, res: Response): Promise<void> {
    try {
      const transactionId = req.params.id;
      const transaction = await AdminService.getTransactionById(transactionId);

      if (!transaction) {
        const response: ApiResponse = {
          success: false,
          error: '交易记录不存在'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: { transaction }
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || '获取交易记录详情失败'
      };

      res.status(500).json(response);
    }
  }
}
