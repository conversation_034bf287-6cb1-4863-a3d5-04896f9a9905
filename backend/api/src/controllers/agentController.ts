import { Request, Response } from 'express';
import { AgentService } from '../services/agentService';
import { ApiResponse } from '@games/shared';
import { asyncHand<PERSON> } from '../middleware/error';

export class AgentController {
  /**
   * 代理注册
   */
  static register = asyncHandler(async (req: Request, res: Response) => {
    const { username, email, phone, password, clubName, clubDescription } = req.body;
    
    const agent = await AgentService.createAgent({
      username,
      email,
      phone,
      password,
      clubName,
      clubDescription
    });
    
    const response: ApiResponse = {
      success: true,
      data: {
        agent: {
          id: (agent as any).id,
          username: (agent as any).username,
          email: (agent as any).email,
          phone: (agent as any).phone,
          balance: (agent as any).balance,
          commission: (agent as any).commission,
          club: {
            id: (agent as any).club_id,
            name: (agent as any).club_name,
            description: (agent as any).club_description
          }
        }
      },
      message: 'Agent registered successfully'
    };
    
    res.status(201).json(response);
  });
  
  /**
   * 代理登录
   */
  static login = asyncHandler(async (req: Request, res: Response) => {
    const { username, password } = req.body;
    const ipAddress = req.ip;
    const userAgent = req.get('User-Agent');

    // 验证必填字段
    if (!username || !password) {
      const response: ApiResponse = {
        success: false,
        error: '用户名和密码不能为空'
      };
      return res.status(400).json(response);
    }

    const result = await AgentService.loginAgent(
      { username, password },
      ipAddress,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      data: result,
      message: '登录成功'
    };

    res.json(response);
  });
  
  /**
   * 代理登出
   */
  static logout = asyncHandler(async (req: Request, res: Response) => {
    const sessionId = req.user?.sessionId;
    
    if (sessionId) {
      await AgentService.logoutAgent(sessionId);
    }
    
    const response: ApiResponse = {
      success: true,
      message: 'Logout successful'
    };
    
    res.json(response);
  });
  
  /**
   * 获取代理信息
   */
  static profile = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    
    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: 'Agent not authenticated'
      };
      return res.status(401).json(response);
    }
    
    const agent = await AgentService.getAgentById(agentId);
    
    if (!agent) {
      const response: ApiResponse = {
        success: false,
        error: 'Agent not found'
      };
      return res.status(404).json(response);
    }
    
    const response: ApiResponse = {
      success: true,
      data: { agent }
    };
    
    res.json(response);
  });
  
  /**
   * 给用户充值
   */
  static depositToUser = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const { userId, amount, description } = req.body;
    
    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: 'Agent not authenticated'
      };
      return res.status(401).json(response);
    }
    
    const result = await AgentService.depositToUser(agentId, userId, amount, description);
    
    const response: ApiResponse = {
      success: true,
      data: result,
      message: 'Deposit successful'
    };
    
    res.json(response);
  });
  
  /**
   * 用户提现
   */
  static withdrawFromUser = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const { userId, amount, description } = req.body;
    
    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: 'Agent not authenticated'
      };
      return res.status(401).json(response);
    }
    
    const result = await AgentService.withdrawFromUser(agentId, userId, amount, description);
    
    const response: ApiResponse = {
      success: true,
      data: result,
      message: 'Withdrawal successful'
    };
    
    res.json(response);
  });
  
  /**
   * 获取代理下的用户列表
   */
  static getUsers = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: 'Agent not authenticated'
      };
      return res.status(401).json(response);
    }

    const result = await AgentService.getAgentUsers(agentId, page, limit);

    const response: ApiResponse = {
      success: true,
      data: result
    };

    res.json(response);
  });

  /**
   * 获取代理统计数据
   */
  static getStats = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: '代理未认证'
      };
      return res.status(401).json(response);
    }

    const stats = await AgentService.getAgentStats(agentId);

    const response: ApiResponse = {
      success: true,
      data: stats
    };

    res.json(response);
  });

  /**
   * 获取代理交易记录
   */
  static getTransactions = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const type = req.query.type as string;
    const status = req.query.status as string;
    const startDate = req.query.start_date as string;
    const endDate = req.query.end_date as string;

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: '代理未认证'
      };
      return res.status(401).json(response);
    }

    const result = await AgentService.getAgentTransactions(agentId, {
      page,
      limit,
      type,
      status,
      startDate,
      endDate
    });

    const response: ApiResponse = {
      success: true,
      data: result
    };

    res.json(response);
  });

  /**
   * 获取代理游戏记录
   */
  static getGames = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const gameType = req.query.game_type as string;
    const status = req.query.status as string;
    const startDate = req.query.start_date as string;
    const endDate = req.query.end_date as string;

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: '代理未认证'
      };
      return res.status(401).json(response);
    }

    const result = await AgentService.getAgentGames(agentId, {
      page,
      limit,
      gameType,
      status,
      startDate,
      endDate
    });

    const response: ApiResponse = {
      success: true,
      data: result
    };

    res.json(response);
  });

  /**
   * 获取代理财务报表
   */
  static getReports = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const startDate = req.query.start_date as string;
    const endDate = req.query.end_date as string;
    const type = req.query.type as string || 'daily';

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: '代理未认证'
      };
      return res.status(401).json(response);
    }

    const result = await AgentService.getAgentReports(agentId, {
      startDate,
      endDate,
      type
    });

    const response: ApiResponse = {
      success: true,
      data: result
    };

    res.json(response);
  });

  /**
   * 更新用户状态
   */
  static updateUserStatus = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const { userId } = req.params;
    const { status } = req.body;

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: '代理未认证'
      };
      return res.status(401).json(response);
    }

    if (!['active', 'inactive', 'banned'].includes(status)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的状态值'
      };
      return res.status(400).json(response);
    }

    await AgentService.updateUserStatus(agentId, userId, status);

    const response: ApiResponse = {
      success: true,
      message: '用户状态更新成功'
    };

    res.json(response);
  });

  /**
   * 获取代理的俱乐部信息
   */
  static getClub = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: '代理未认证'
      };
      return res.status(401).json(response);
    }

    const club = await AgentService.getAgentClub(agentId);

    if (!club) {
      const response: ApiResponse = {
        success: false,
        error: '俱乐部不存在'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: {
        id: (club as any).id,
        name: (club as any).name,
        description: (club as any).description,
        clubCode: (club as any).club_code,
        autoJoin: (club as any).auto_join,
        joinApprovalRequired: (club as any).join_approval_required,
        maxMembers: (club as any).max_members,
        currentMembers: (club as any).current_members,
        status: (club as any).status,
        createdAt: (club as any).created_at,
        updatedAt: (club as any).updated_at
      }
    };

    res.json(response);
  });

  /**
   * 更新代理的俱乐部设置
   */
  static updateClub = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const { name, description, clubCode, autoJoin, joinApprovalRequired, maxMembers } = req.body;

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: '代理未认证'
      };
      return res.status(401).json(response);
    }

    const updatedClub = await AgentService.updateAgentClub(agentId, {
      name,
      description,
      clubCode,
      autoJoin,
      joinApprovalRequired,
      maxMembers
    });

    const response: ApiResponse = {
      success: true,
      data: {
        id: (updatedClub as any).id,
        name: (updatedClub as any).name,
        description: (updatedClub as any).description,
        clubCode: (updatedClub as any).club_code,
        autoJoin: (updatedClub as any).auto_join,
        joinApprovalRequired: (updatedClub as any).join_approval_required,
        maxMembers: (updatedClub as any).max_members,
        currentMembers: (updatedClub as any).current_members,
        status: (updatedClub as any).status,
        createdAt: (updatedClub as any).created_at,
        updatedAt: (updatedClub as any).updated_at
      },
      message: '俱乐部设置更新成功'
    };

    res.json(response);
  });

  /**
   * 获取俱乐部申请列表
   */
  static getClubApplications = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const status = req.query.status as string;

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: '代理未认证'
      };
      return res.status(401).json(response);
    }

    const applications = await AgentService.getClubApplications(agentId, status);

    const response: ApiResponse = {
      success: true,
      data: applications.map((app: any) => ({
        id: app.id,
        userId: app.user_id,
        username: app.username,
        clubCode: app.club_code,
        status: app.status,
        appliedAt: app.applied_at,
        processedAt: app.processed_at,
        rejectionReason: app.rejection_reason,
        notes: app.notes
      }))
    };

    res.json(response);
  });

  /**
   * 处理俱乐部申请
   */
  static processClubApplication = asyncHandler(async (req: Request, res: Response) => {
    const agentId = req.user?.userId;
    const { applicationId } = req.params;
    const { action, rejectionReason } = req.body;

    if (!agentId) {
      const response: ApiResponse = {
        success: false,
        error: '代理未认证'
      };
      return res.status(401).json(response);
    }

    if (!['approve', 'reject'].includes(action)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的操作类型'
      };
      return res.status(400).json(response);
    }

    await AgentService.processClubApplication(agentId, applicationId, action, rejectionReason);

    const response: ApiResponse = {
      success: true,
      message: action === 'approve' ? '申请已批准' : '申请已拒绝'
    };

    res.json(response);
  });
}
