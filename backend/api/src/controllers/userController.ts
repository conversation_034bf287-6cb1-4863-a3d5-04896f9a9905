import { Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '../middleware/error';
import { UserService } from '../services/userService';
import { ApiResponse } from '../types/common';

export class UserController {
  /**
   * 获取用户俱乐部状态
   */
  static getClubStatus = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;

    if (!userId) {
      const response: ApiResponse = {
        success: false,
        error: '用户未认证'
      };
      return res.status(401).json(response);
    }

    const status = await UserService.getUserClubStatus(userId);

    const response: ApiResponse = {
      success: true,
      data: {
        status: status.isMember ? 'member' : (status.hasPendingApplication ? 'pending' : 'none'),
        clubInfo: status.clubInfo
      }
    };

    res.json(response);
  });

  /**
   * 获取用户的俱乐部申请记录
   */
  static getClubApplications = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;

    if (!userId) {
      const response: ApiResponse = {
        success: false,
        error: '用户未认证'
      };
      return res.status(401).json(response);
    }

    const applications = await UserService.getUserClubApplications(userId);

    const response: ApiResponse = {
      success: true,
      data: applications.map((app: any) => ({
        id: app.id,
        clubCode: app.club_code,
        status: app.status,
        appliedAt: app.applied_at,
        processedAt: app.processed_at,
        rejectionReason: app.rejection_reason,
        notes: app.notes
      }))
    };

    res.json(response);
  });

  /**
   * 申请加入俱乐部
   */
  static joinClub = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    const { clubCode } = req.body;

    if (!userId) {
      const response: ApiResponse = {
        success: false,
        error: '用户未认证'
      };
      return res.status(401).json(response);
    }

    if (!clubCode || !/^\d{6}$/.test(clubCode)) {
      const response: ApiResponse = {
        success: false,
        error: '请输入有效的6位数字俱乐部代码'
      };
      return res.status(400).json(response);
    }

    const result = await UserService.applyToJoinClub(userId, clubCode);

    const response: ApiResponse = {
      success: true,
      data: {
        autoJoined: result.autoJoined,
        applicationId: result.applicationId
      },
      message: result.autoJoined ? '成功加入俱乐部' : '申请已提交，等待审核'
    };

    res.json(response);
  });

  /**
   * 获取用户信息
   */
  static getProfile = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;

    if (!userId) {
      const response: ApiResponse = {
        success: false,
        error: '用户未认证'
      };
      return res.status(401).json(response);
    }

    const user = await UserService.getUserById(userId);

    if (!user) {
      const response: ApiResponse = {
        success: false,
        error: '用户不存在'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: {
        id: (user as any).id,
        username: (user as any).username,
        balance: (user as any).balance,
        status: (user as any).status,
        clubId: (user as any).club_id,
        createdAt: (user as any).created_at
      }
    };

    res.json(response);
  });
}
