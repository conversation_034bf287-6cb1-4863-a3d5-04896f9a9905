import jwt from 'jsonwebtoken';
import { generateId } from '@games/shared';

export interface JWTPayload {
  userId: string;
  userType: 'user' | 'agent' | 'admin';
  username: string;
  sessionId: string;
}

export const jwtConfig = {
  secret: process.env.JWT_SECRET || 'games-platform-super-secret-jwt-key-2024',
  expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d'
};

/**
 * 生成访问令牌
 */
export function generateAccessToken(payload: JWTPayload): string {
  return (jwt as any).sign(payload, jwtConfig.secret, {
    expiresIn: jwtConfig.expiresIn,
    issuer: 'games-platform',
    audience: 'games-users'
  });
}

/**
 * 生成刷新令牌
 */
export function generateRefreshToken(payload: JWTPayload): string {
  return (jwt as any).sign(
    { ...payload, type: 'refresh' },
    jwtConfig.secret,
    {
      expiresIn: jwtConfig.refreshExpiresIn,
      issuer: 'games-platform',
      audience: 'games-users'
    }
  );
}

/**
 * 验证访问令牌
 */
export function verifyAccessToken(token: string): JWTPayload {
  try {
    const decoded = (jwt as any).verify(token, jwtConfig.secret, {
      issuer: 'games-platform',
      audience: 'games-users'
    }) as any;
    
    if (decoded.type === 'refresh') {
      throw new Error('Invalid token type');
    }
    
    return {
      userId: decoded.userId,
      userType: decoded.userType,
      username: decoded.username,
      sessionId: decoded.sessionId
    };
  } catch (error) {
    throw new Error('Invalid access token');
  }
}

/**
 * 验证刷新令牌
 */
export function verifyRefreshToken(token: string): JWTPayload {
  try {
    const decoded = (jwt as any).verify(token, jwtConfig.secret, {
      issuer: 'games-platform',
      audience: 'games-users'
    }) as any;
    
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type');
    }
    
    return {
      userId: decoded.userId,
      userType: decoded.userType,
      username: decoded.username,
      sessionId: decoded.sessionId
    };
  } catch (error) {
    throw new Error('Invalid refresh token');
  }
}

/**
 * 生成令牌对
 */
export function generateTokenPair(userId: string, userType: 'user' | 'agent' | 'admin', username: string) {
  const sessionId = generateId();
  const payload: JWTPayload = {
    userId,
    userType,
    username,
    sessionId
  };
  
  return {
    accessToken: generateAccessToken(payload),
    refreshToken: generateRefreshToken(payload),
    sessionId,
    expiresIn: jwtConfig.expiresIn
  };
}
