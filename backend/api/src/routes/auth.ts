import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { validate, commonSchemas } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';
import { userCache } from '../middleware/cache';

const router = Router();

// 用户注册
router.post('/register', validate(commonSchemas.userRegister), AuthController.register);

// 用户登录
router.post('/login', validate(commonSchemas.userLogin), AuthController.login);

// 用户登出 (需要认证)
router.post('/logout', authenticateToken, AuthController.logout);

// 获取用户信息 (需要认证，使用缓存)
router.get('/profile', authenticateToken, userCache(300), AuthController.profile);

// 刷新令牌
router.post('/refresh', AuthController.refresh);

export default router;
