import { Router } from 'express';
import { AgentController } from '../controllers/agentController';
import { validate, validateQuery, commonSchemas } from '../middleware/validation';
import { authenticateToken, requireAgent } from '../middleware/auth';
import { userCache } from '../middleware/cache';
import Joi from 'joi';

const router = Router();

// 代理登录验证模式
const agentLoginSchema = Joi.object({
  username: Joi.string().required(),
  password: Joi.string().required()
});

// 充值验证模式
const depositSchema = Joi.object({
  userId: Joi.string().uuid().required(),
  amount: Joi.number().positive().precision(2).required(),
  description: Joi.string().max(200).optional()
});

// 提现验证模式
const withdrawSchema = Joi.object({
  userId: Joi.string().uuid().required(),
  amount: Joi.number().positive().precision(2).required(),
  description: Joi.string().max(200).optional()
});

// 代理登录
router.post('/login', validate(agentLoginSchema), AgentController.login);

// 代理登出 (需要认证)
router.post('/logout', authenticateToken, requireAgent, AgentController.logout);

// 获取代理信息 (需要认证，使用缓存)
router.get('/profile', authenticateToken, requireAgent, userCache(300), AgentController.profile);

// 给用户充值 (需要代理权限)
router.post('/deposit', authenticateToken, requireAgent, validate(depositSchema), AgentController.depositToUser);

// 用户提现 (需要代理权限)
router.post('/withdraw', authenticateToken, requireAgent, validate(withdrawSchema), AgentController.withdrawFromUser);

// 获取代理下的用户列表 (需要代理权限，使用缓存)
router.get('/users', authenticateToken, requireAgent, validateQuery(commonSchemas.pagination), userCache(120), AgentController.getUsers);

// 获取代理统计数据 (需要代理权限)
router.get('/stats', authenticateToken, requireAgent, AgentController.getStats);

// 获取代理交易记录 (需要代理权限)
router.get('/transactions', authenticateToken, requireAgent, validateQuery(commonSchemas.pagination), AgentController.getTransactions);

// 获取代理游戏记录 (需要代理权限)
router.get('/games', authenticateToken, requireAgent, validateQuery(commonSchemas.pagination), AgentController.getGames);

// 获取代理财务报表 (需要代理权限)
router.get('/reports', authenticateToken, requireAgent, AgentController.getReports);

// 更新用户状态 (需要代理权限)
router.put('/users/:userId/status', authenticateToken, requireAgent, AgentController.updateUserStatus);

// 俱乐部管理相关路由
// 获取俱乐部信息
router.get('/club', authenticateToken, requireAgent, AgentController.getClub);

// 更新俱乐部设置
router.put('/club', authenticateToken, requireAgent, AgentController.updateClub);

// 获取俱乐部申请列表
router.get('/club/applications', authenticateToken, requireAgent, AgentController.getClubApplications);

// 处理俱乐部申请
router.put('/club/applications/:applicationId', authenticateToken, requireAgent, AgentController.processClubApplication);

export default router;
