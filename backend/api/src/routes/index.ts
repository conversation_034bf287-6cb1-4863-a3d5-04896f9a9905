import { Router } from 'express';
import authRoutes from './auth';
import agentRoutes from './agent';
import adminRoutes from './admin';
import userRoutes from './users';

const router = Router();

// 认证相关路由
router.use('/auth', authRoutes);

// 用户相关路由
router.use('/users', userRoutes);

// 代理相关路由
router.use('/agent', agentRoutes);

// 管理员相关路由
router.use('/admin', adminRoutes);

// 健康检查
router.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    }
  });
});

export default router;
