import { Router } from 'express';
import { UserController } from '../controllers/userController';
import { authenticateToken, requireUser } from '../middleware/auth';

const router = Router();

// 获取用户信息 (需要用户权限)
router.get('/profile', authenticateToken, requireUser, UserController.getProfile);

// 获取用户俱乐部状态 (需要用户权限)
router.get('/club-status', authenticateToken, requireUser, UserController.getClubStatus);

// 获取用户俱乐部申请记录 (需要用户权限)
router.get('/club-applications', authenticateToken, requireUser, UserController.getClubApplications);

// 申请加入俱乐部 (需要用户权限)
router.post('/join-club', authenticateToken, requireUser, UserController.joinClub);

export default router;
