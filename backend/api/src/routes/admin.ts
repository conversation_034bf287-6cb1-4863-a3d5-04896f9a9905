import { Router } from 'express';
import { AdminController } from '../controllers/adminController';
import { validate, commonSchemas } from '../middleware/validation';
import { authenticateToken, requireAdmin } from '../middleware/auth';
import <PERSON><PERSON> from 'joi';

const router = Router();

// 登录验证模式
const loginSchema = Joi.object({
  username: Joi.string().required(),
  password: Joi.string().required()
});

// 管理员登录 (不需要认证)
router.post('/login', validate(loginSchema), AdminController.login);

// 管理员登出 (需要认证)
router.post('/logout', authenticateToken, requireAdmin, AdminController.logout);

// 获取管理员信息 (需要认证)
router.get('/profile', authenticateToken, requireAdmin, AdminController.profile);

// 获取仪表板统计数据 (需要认证)
router.get('/dashboard/stats', authenticateToken, requireAdmin, AdminController.getDashboardStats);

// 用户管理相关路由
router.get('/users', authenticateToken, requireAdmin, AdminController.getUsers);
router.get('/users/:id', authenticateToken, requireAdmin, AdminController.getUserById);
router.get('/users/:id/detail', authenticateToken, requireAdmin, AdminController.getUserDetail);
router.put('/users/:id/status', authenticateToken, requireAdmin, AdminController.updateUserStatus);
router.put('/users/:id/balance', authenticateToken, requireAdmin, AdminController.updateUserBalance);

// 代理管理相关路由
router.get('/agents', authenticateToken, requireAdmin, AdminController.getAgents);
router.post('/agents', authenticateToken, requireAdmin, AdminController.createAgent);
router.get('/agents/:id', authenticateToken, requireAdmin, AdminController.getAgentById);
router.put('/agents/:id/status', authenticateToken, requireAdmin, AdminController.updateAgentStatus);

// 游戏记录管理相关路由
router.get('/games', authenticateToken, requireAdmin, AdminController.getGameRecords);
router.get('/games/:id', authenticateToken, requireAdmin, AdminController.getGameRecordById);

// 交易记录管理相关路由
router.get('/transactions', authenticateToken, requireAdmin, AdminController.getTransactions);
router.get('/transactions/:id', authenticateToken, requireAdmin, AdminController.getTransactionById);

export default router;
