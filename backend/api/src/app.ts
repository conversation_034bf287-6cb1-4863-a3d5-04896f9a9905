import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

import routes from './routes';
import { errorHandler, notFoundHandler } from './middleware/error';
import { testDatabaseConnection, initRedis } from './config/database';
import { cacheService } from './services/cacheService';

// 加载环境变量
dotenv.config();

const app = express();

// 安全中间件
app.use(helmet());

// CORS配置
const corsOptions = {
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// 日志中间件
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined'));
}

// 解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 信任代理 (用于获取真实IP)
app.set('trust proxy', true);

// API路由
app.use('/api', routes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '🎮 游戏平台API服务',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// 404处理
app.use(notFoundHandler);

// 错误处理
app.use(errorHandler);

// 初始化数据库连接
export async function initializeApp() {
  try {
    console.log('🔄 初始化应用...');
    
    // 测试数据库连接
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      throw new Error('数据库连接失败');
    }
    
    // 初始化Redis
    await initRedis();

    // 连接缓存服务
    await cacheService.connect();

    console.log('✅ 应用初始化完成');
    return true;
  } catch (error) {
    console.error('❌ 应用初始化失败:', error);
    return false;
  }
}

export default app;
