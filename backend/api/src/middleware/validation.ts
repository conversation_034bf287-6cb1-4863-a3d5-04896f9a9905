import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { ApiResponse } from '@games/shared';

/**
 * 验证中间件生成器
 */
export function validate(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }
    
    next();
  };
}

/**
 * 查询参数验证中间件生成器
 */
export function validateQuery(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.query);
    
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }
    
    next();
  };
}

/**
 * 路径参数验证中间件生成器
 */
export function validateParams(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.params);
    
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }
    
    next();
  };
}

// 常用验证模式
export const commonSchemas = {
  // ID验证
  id: Joi.string().uuid().required(),
  
  // 分页验证
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  }),
  
  // 用户注册验证
  userRegister: Joi.object({
    username: Joi.string().alphanum().min(3).max(20).required(),
    email: Joi.string().email().optional(),
    phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional(),
    password: Joi.string().min(6).max(50).required(),
    agentCode: Joi.string().optional()
  }).or('email', 'phone'),
  
  // 用户登录验证
  userLogin: Joi.object({
    username: Joi.string().optional(),
    email: Joi.string().email().optional(),
    phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional(),
    password: Joi.string().required()
  }).or('username', 'email', 'phone'),
  
  // 代理注册验证
  agentRegister: Joi.object({
    username: Joi.string().alphanum().min(3).max(20).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional(),
    password: Joi.string().min(6).max(50).required()
  }),
  
  // 俱乐部创建验证
  clubCreate: Joi.object({
    name: Joi.string().min(2).max(50).required(),
    description: Joi.string().max(500).optional(),
    maxMembers: Joi.number().integer().min(10).max(10000).default(1000)
  }),
  
  // 下注验证
  placeBet: Joi.object({
    amount: Joi.number().positive().precision(2).required(),
    seatNumber: Joi.number().integer().min(1).max(10).optional()
  }),
  
  // 游戏房间创建验证
  createRoom: Joi.object({
    gameType: Joi.string().valid('sangong', 'niuniu').required(),
    minBet: Joi.number().positive().precision(2).default(10),
    maxBet: Joi.number().positive().precision(2).default(1000)
  }),
  
  // 充值验证
  deposit: Joi.object({
    amount: Joi.number().positive().precision(2).required(),
    userId: Joi.string().uuid().optional()
  }),
  
  // 提现验证
  withdraw: Joi.object({
    amount: Joi.number().positive().precision(2).required()
  })
};
