import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@games/shared';

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 全局错误处理中间件
 */
export function errorHandler(
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
) {
  let statusCode = 500;
  let message = 'Internal server error';

  // 如果是自定义错误
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
  }
  // MySQL错误处理
  else if (error.message.includes('ER_DUP_ENTRY')) {
    statusCode = 409;
    message = 'Duplicate entry';
  }
  // JWT错误处理
  else if (error.message.includes('jwt')) {
    statusCode = 401;
    message = 'Invalid token';
  }
  // 验证错误处理
  else if (error.message.includes('ValidationError')) {
    statusCode = 400;
    message = error.message;
  }

  // 开发环境下显示详细错误信息
  if (process.env.NODE_ENV === 'development') {
    console.error('Error:', error);
  }

  const response: ApiResponse = {
    success: false,
    error: message
  };

  res.status(statusCode).json(response);
}

/**
 * 404错误处理中间件
 */
export function notFoundHandler(req: Request, res: Response) {
  const response: ApiResponse = {
    success: false,
    error: `Route ${req.originalUrl} not found`
  };

  res.status(404).json(response);
}

/**
 * 异步错误捕获包装器
 */
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
