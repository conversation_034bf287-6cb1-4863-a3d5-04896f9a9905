import { Request, Response, NextFunction } from 'express';
import { cacheService } from '../services/cacheService';

/**
 * 缓存中间件配置
 */
interface CacheOptions {
  ttl?: number; // 缓存时间（秒）
  keyGenerator?: (req: Request) => string; // 自定义键生成器
  condition?: (req: Request, res: Response) => boolean; // 缓存条件
  skipCache?: (req: Request) => boolean; // 跳过缓存条件
}

/**
 * 生成默认缓存键
 */
function generateDefaultKey(req: Request): string {
  const { method, originalUrl, query, body } = req;
  const userId = (req as any).user?.id || 'anonymous';
  
  // 对于GET请求，使用URL和查询参数
  if (method === 'GET') {
    const queryString = Object.keys(query).length > 0 ? JSON.stringify(query) : '';
    return `cache:${method}:${originalUrl}:${queryString}:${userId}`;
  }
  
  // 对于其他请求，包含请求体
  const bodyString = Object.keys(body || {}).length > 0 ? JSON.stringify(body) : '';
  return `cache:${method}:${originalUrl}:${bodyString}:${userId}`;
}

/**
 * 缓存中间件
 */
export function cache(options: CacheOptions = {}) {
  const {
    ttl = 300, // 默认5分钟
    keyGenerator = generateDefaultKey,
    condition = () => true,
    skipCache = () => false
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // 检查是否跳过缓存
    if (skipCache(req) || !cacheService.isConnected()) {
      return next();
    }

    // 只缓存GET请求
    if (req.method !== 'GET') {
      return next();
    }

    try {
      const cacheKey = keyGenerator(req);
      
      // 尝试从缓存获取数据
      const cachedData = await cacheService.get(cacheKey);
      
      if (cachedData) {
        console.log(`✅ 缓存命中: ${cacheKey}`);
        return res.json(cachedData);
      }

      // 缓存未命中，继续执行
      console.log(`❌ 缓存未命中: ${cacheKey}`);
      
      // 拦截响应
      const originalJson = res.json;
      res.json = function(data: any) {
        // 检查是否满足缓存条件
        if (condition(req, res) && res.statusCode === 200) {
          // 异步缓存数据，不阻塞响应
          cacheService.set(cacheKey, data, ttl).catch(err => {
            console.error('缓存设置失败:', err);
          });
        }
        
        // 调用原始的json方法
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error('缓存中间件错误:', error);
      next(); // 缓存失败不影响正常流程
    }
  };
}

/**
 * 用户数据缓存中间件
 */
export function userCache(ttl: number = 600) {
  return cache({
    ttl,
    keyGenerator: (req) => {
      const userId = (req as any).user?.id || 'anonymous';
      return `user:${userId}:${req.originalUrl}:${JSON.stringify(req.query)}`;
    },
    condition: (req, res) => res.statusCode === 200 && (req as any).user?.id
  });
}

/**
 * 游戏数据缓存中间件
 */
export function gameCache(ttl: number = 60) {
  return cache({
    ttl,
    keyGenerator: (req) => {
      return `game:${req.originalUrl}:${JSON.stringify(req.query)}`;
    },
    condition: (req, res) => res.statusCode === 200
  });
}

/**
 * 统计数据缓存中间件
 */
export function statsCache(ttl: number = 300) {
  return cache({
    ttl,
    keyGenerator: (req) => {
      const userId = (req as any).user?.id;
      const userPart = userId ? `:${userId}` : '';
      return `stats${userPart}:${req.originalUrl}:${JSON.stringify(req.query)}`;
    },
    condition: (req, res) => res.statusCode === 200
  });
}

/**
 * 清除缓存工具函数
 */
export class CacheManager {
  /**
   * 清除用户相关缓存
   */
  static async clearUserCache(userId: string): Promise<void> {
    try {
      // 这里需要实现模式匹配删除，Redis不直接支持，需要先获取所有匹配的键
      // 简化实现，实际项目中可能需要使用Redis的SCAN命令
      const patterns = [
        `user:${userId}:*`,
        `cache:*:*:*:${userId}`
      ];
      
      console.log(`清除用户缓存: ${userId}`);
      // 实际实现需要扫描并删除匹配的键
    } catch (error) {
      console.error('清除用户缓存失败:', error);
    }
  }

  /**
   * 清除游戏相关缓存
   */
  static async clearGameCache(gameType?: string): Promise<void> {
    try {
      const pattern = gameType ? `game:*${gameType}*` : 'game:*';
      console.log(`清除游戏缓存: ${pattern}`);
      // 实际实现需要扫描并删除匹配的键
    } catch (error) {
      console.error('清除游戏缓存失败:', error);
    }
  }

  /**
   * 清除统计数据缓存
   */
  static async clearStatsCache(): Promise<void> {
    try {
      console.log('清除统计缓存');
      // 实际实现需要扫描并删除匹配的键
    } catch (error) {
      console.error('清除统计缓存失败:', error);
    }
  }

  /**
   * 清除所有缓存
   */
  static async clearAllCache(): Promise<void> {
    try {
      console.log('清除所有缓存');
      // 注意：这个操作很危险，实际项目中需要谨慎使用
      // await cacheService.flushAll();
    } catch (error) {
      console.error('清除所有缓存失败:', error);
    }
  }
}

/**
 * 缓存预热工具
 */
export class CacheWarmer {
  /**
   * 预热用户数据
   */
  static async warmUserData(userId: string): Promise<void> {
    try {
      console.log(`预热用户数据: ${userId}`);
      // 这里可以预加载用户的常用数据到缓存
    } catch (error) {
      console.error('预热用户数据失败:', error);
    }
  }

  /**
   * 预热游戏数据
   */
  static async warmGameData(): Promise<void> {
    try {
      console.log('预热游戏数据');
      // 这里可以预加载游戏的常用数据到缓存
    } catch (error) {
      console.error('预热游戏数据失败:', error);
    }
  }
}
