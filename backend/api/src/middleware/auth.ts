import { Request, Response, NextFunction } from 'express';
import { verifyAccessToken, JWTPayload } from '../config/jwt';
import { pool } from '../config/database';
import { ApiResponse } from '@games/shared';

// 扩展Request接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
    }
  }
}

/**
 * JWT认证中间件
 */
export async function authenticateToken(req: Request, res: Response, next: NextFunction) {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access token required'
      } as ApiResponse);
    }

    // 验证JWT令牌
    const payload = verifyAccessToken(token);
    
    // 检查会话是否有效
    const [sessions] = await pool.execute(
      'SELECT id, expires_at FROM user_sessions WHERE id = ? AND user_id = ? AND expires_at > NOW()',
      [payload.sessionId, payload.userId]
    );

    if (!Array.isArray(sessions) || sessions.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'Session expired or invalid'
      } as ApiResponse);
    }

    req.user = payload;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: 'Invalid token'
    } as ApiResponse);
  }
}

/**
 * 用户类型验证中间件
 */
export function requireUserType(...allowedTypes: Array<'user' | 'agent' | 'admin'>) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse);
    }

    if (!allowedTypes.includes(req.user.userType)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse);
    }

    next();
  };
}

/**
 * 管理员权限验证中间件
 */
export function requireAdmin(req: Request, res: Response, next: NextFunction) {
  return requireUserType('admin')(req, res, next);
}

/**
 * 代理权限验证中间件
 */
export function requireAgent(req: Request, res: Response, next: NextFunction) {
  return requireUserType('agent', 'admin')(req, res, next);
}

/**
 * 用户权限验证中间件
 */
export function requireUser(req: Request, res: Response, next: NextFunction) {
  return requireUserType('user', 'agent', 'admin')(req, res, next);
}
