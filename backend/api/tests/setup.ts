import dotenv from 'dotenv';
import { pool } from '../src/config/database';
import { cacheService } from '../src/services/cacheService';

// 加载测试环境变量
dotenv.config({ path: '.env.test' });

// 全局测试设置
beforeAll(async () => {
  console.log('🔄 初始化测试环境...');
  
  try {
    // 连接Redis
    await cacheService.connect();
    console.log('✅ Redis测试连接成功');
  } catch (error) {
    console.warn('⚠️ Redis连接失败，跳过缓存测试');
  }
});

// 每个测试后清理
afterEach(async () => {
  // 清理测试数据
  if (cacheService.isConnected()) {
    // 清理测试缓存数据
    try {
      // 这里可以添加清理测试缓存的逻辑
    } catch (error) {
      console.warn('清理缓存失败:', error);
    }
  }
});

// 全局测试清理
afterAll(async () => {
  console.log('🔄 清理测试环境...');
  
  try {
    // 关闭数据库连接
    await pool.end();
    console.log('✅ 数据库连接已关闭');
  } catch (error) {
    console.warn('关闭数据库连接失败:', error);
  }
  
  try {
    // 关闭Redis连接
    await cacheService.disconnect();
    console.log('✅ Redis连接已关闭');
  } catch (error) {
    console.warn('关闭Redis连接失败:', error);
  }
});

// 测试工具函数
export const testUtils = {
  /**
   * 创建测试用户
   */
  async createTestUser(userData: any = {}) {
    const defaultUser = {
      username: `test_user_${Date.now()}`,
      email: `test${Date.now()}@example.com`,
      password: 'test123456',
      ...userData
    };
    
    // 这里应该调用实际的用户创建逻辑
    return defaultUser;
  },

  /**
   * 创建测试代理
   */
  async createTestAgent(agentData: any = {}) {
    const defaultAgent = {
      username: `test_agent_${Date.now()}`,
      email: `agent${Date.now()}@example.com`,
      password: 'test123456',
      clubName: `测试俱乐部_${Date.now()}`,
      ...agentData
    };
    
    // 这里应该调用实际的代理创建逻辑
    return defaultAgent;
  },

  /**
   * 生成测试JWT令牌
   */
  generateTestToken(payload: any = {}) {
    const defaultPayload = {
      userId: 'test-user-id',
      username: 'testuser',
      userType: 'user',
      sessionId: 'test-session-id',
      ...payload
    };
    
    // 这里应该调用实际的JWT生成逻辑
    return 'test-jwt-token';
  },

  /**
   * 清理测试数据
   */
  async cleanupTestData() {
    try {
      // 清理测试用户
      await pool.execute('DELETE FROM users WHERE username LIKE "test_user_%"');
      
      // 清理测试代理
      await pool.execute('DELETE FROM agents WHERE username LIKE "test_agent_%"');
      
      // 清理测试俱乐部
      await pool.execute('DELETE FROM clubs WHERE name LIKE "测试俱乐部_%"');
      
      console.log('✅ 测试数据清理完成');
    } catch (error) {
      console.warn('清理测试数据失败:', error);
    }
  }
};

// 模拟数据
export const mockData = {
  user: {
    id: 'mock-user-id',
    username: 'mockuser',
    email: '<EMAIL>',
    balance: 1000,
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  
  agent: {
    id: 'mock-agent-id',
    username: 'mockagent',
    email: '<EMAIL>',
    balance: 5000,
    commission: 0.05,
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  
  club: {
    id: 'mock-club-id',
    name: '模拟俱乐部',
    agentId: 'mock-agent-id',
    maxMembers: 1000,
    currentMembers: 10,
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  
  transaction: {
    id: 'mock-transaction-id',
    userId: 'mock-user-id',
    type: 'deposit',
    amount: 100,
    balanceBefore: 900,
    balanceAfter: 1000,
    status: 'completed',
    createdAt: new Date()
  }
};
