const mysql = require('mysql2/promise');

async function debugSeatStatus() {
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    user: 'root',
    password: 'root',
    database: 'games'
  });

  try {
    console.log('🔍 检查当前房间座位状态...\n');
    
    // 查询房间状态
    const [rooms] = await connection.execute(
      'SELECT * FROM game_rooms WHERE game_type = ?',
      ['sangong']
    );

    if (rooms.length === 0) {
      console.log('❌ 未找到三公房间');
      return;
    }

    const room = rooms[0];
    console.log(`📊 房间基本信息:`);
    console.log(`   ID: ${room.id}`);
    console.log(`   状态: ${room.status}`);
    console.log(`   当前轮次: ${room.current_round}`);
    console.log(`   最小下注: ${room.min_bet}`);
    console.log(`   最大下注: ${room.max_bet}`);
    console.log(`   创建时间: ${room.created_at}`);
    console.log(`   更新时间: ${room.updated_at}\n`);

    // 解析座位信息
    let seats;
    try {
      seats = typeof room.seats === 'string' ? JSON.parse(room.seats) : room.seats;
    } catch (error) {
      console.log(`❌ 座位数据解析失败: ${error.message}`);
      console.log(`原始座位数据: ${room.seats}`);
      return;
    }

    console.log(`🪑 座位详情 (共${seats.length}个座位):`);

    let occupiedSeats = 0;
    seats.forEach((seat, index) => {
      const status = seat.userId ? '🔴 已占用' : '🟢 空闲';
      console.log(`   座位 ${seat.position}: ${status}`);

      if (seat.userId) {
        occupiedSeats++;
        console.log(`      用户ID: ${seat.userId}`);
        console.log(`      用户名: ${seat.username}`);
        console.log(`      余额: ${seat.balance}`);
        console.log(`      下注: ${seat.bet}`);
        console.log(`      准备状态: ${seat.isReady}`);
        console.log(`      牌数: ${seat.cards.length}`);
      }
    });

    console.log(`\n📈 统计信息:`);
    console.log(`   已占用座位: ${occupiedSeats}/${seats.length}`);
    console.log(`   空闲座位: ${seats.length - occupiedSeats}/${seats.length}`);

    // 解析旁观者信息
    let spectators;
    try {
      spectators = typeof room.spectators === 'string' ? JSON.parse(room.spectators) : room.spectators;
    } catch (error) {
      console.log(`❌ 旁观者数据解析失败: ${error.message}`);
      spectators = [];
    }
    console.log(`   旁观者数量: ${spectators.length}`);
    if (spectators.length > 0) {
      console.log(`   旁观者列表: ${spectators.join(', ')}`);
    }

    // 查询用户信息
    console.log(`\n👥 用户信息:`);
    const [users] = await connection.execute(
      'SELECT id, username, balance FROM users WHERE username IN (?, ?)',
      ['testuser', 'testuser2']
    );

    users.forEach(user => {
      console.log(`   ${user.username} (${user.id}): 余额 ${user.balance}`);
    });

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await connection.end();
  }
}

// 清理特定用户的座位
async function clearUserSeat(username) {
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    user: 'root',
    password: 'root',
    database: 'games'
  });

  try {
    console.log(`🧹 清理用户 ${username} 的座位...\n`);
    
    // 获取用户ID
    const [users] = await connection.execute(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    if (users.length === 0) {
      console.log(`❌ 未找到用户: ${username}`);
      return;
    }

    const userId = users[0].id;
    console.log(`👤 用户ID: ${userId}`);

    // 获取房间信息
    const [rooms] = await connection.execute(
      'SELECT * FROM game_rooms WHERE game_type = ?',
      ['sangong']
    );

    if (rooms.length === 0) {
      console.log('❌ 未找到三公房间');
      return;
    }

    const room = rooms[0];
    const seats = typeof room.seats === 'string' ? JSON.parse(room.seats) : room.seats;
    let spectators = typeof room.spectators === 'string' ? JSON.parse(room.spectators) : room.spectators;

    // 查找并清理用户座位
    let foundSeat = false;
    seats.forEach((seat, index) => {
      if (seat.userId === userId) {
        console.log(`🪑 找到用户在座位 ${seat.position}`);
        seats[index] = {
          position: seat.position,
          userId: null,
          username: null,
          balance: 0,
          bet: 0,
          cards: [],
          isReady: false,
          sideWagers: []
        };
        foundSeat = true;
      }
    });

    // 从旁观者列表中移除
    const originalSpectators = typeof room.spectators === 'string' ? JSON.parse(room.spectators) : room.spectators;
    spectators = spectators.filter(id => id !== userId);

    if (foundSeat || spectators.length !== originalSpectators.length) {
      // 更新数据库
      await connection.execute(
        'UPDATE game_rooms SET seats = ?, spectators = ?, updated_at = NOW() WHERE game_type = ?',
        [JSON.stringify(seats), JSON.stringify(spectators), 'sangong']
      );

      console.log(`✅ 用户 ${username} 的座位已清理`);
    } else {
      console.log(`ℹ️ 用户 ${username} 没有占用任何座位`);
    }

  } catch (error) {
    console.error('❌ 清理失败:', error);
  } finally {
    await connection.end();
  }
}

// 根据命令行参数执行不同操作
const args = process.argv.slice(2);
if (args.length > 0 && args[0] === 'clear') {
  const username = args[1] || 'testuser';
  clearUserSeat(username);
} else {
  debugSeatStatus();
}
