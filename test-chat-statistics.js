const io = require('socket.io-client');

// 测试配置
const SERVER_URL = 'http://localhost:3002';
const TEST_TOKENS = {
  user1: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5NWM0ZjkxYy01MTFlLTExZjAtYjVhNS0wMjQyYWMxNDAwMDIiLCJ1c2VybmFtZSI6InRlc3R1c2VyIiwidXNlclR5cGUiOiJ1c2VyIiwic2Vzc2lvbklkIjoidGVzdC1zZXNzaW9uLTEiLCJpYXQiOjE3NTIxNTA4MzcsImV4cCI6MTc1NDc0MjgzN30.Ganx91CMCvFiZCk1V-JjfOs0auX2xLgK7WENkvqD54I',
  user2: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5NWM0ZjkxYy01MTFlLTExZjAtYjVhNS0wMjQyYWMxNDAwMDMiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInVzZXJUeXBlIjoidXNlciIsInNlc3Npb25JZCI6InRlc3Qtc2Vzc2lvbi0yIiwiaWF0IjoxNzUyMTUwODM3LCJleHAiOjE3NTQ3NDI4Mzd9.7RXlfuYrnCwtMl7JFi0JI8Wpa1mzfWdC0sipc9wDkmo'
};

let user1Socket, user2Socket;
let chatMessages = [];

async function connectUsers() {
  console.log('🧪 开始测试聊天统计功能...\n');

  // 连接用户1
  user1Socket = io(SERVER_URL, {
    auth: { token: TEST_TOKENS.user1 },
    transports: ['websocket']
  });

  // 连接用户2
  user2Socket = io(SERVER_URL, {
    auth: { token: TEST_TOKENS.user2 },
    transports: ['websocket']
  });

  // 设置事件监听器
  setupEventListeners();

  return new Promise((resolve) => {
    let connectedCount = 0;

    const checkConnected = () => {
      connectedCount++;
      if (connectedCount === 2) {
        console.log('✅ 两个用户已连接\n');
        resolve();
      }
    };

    user1Socket.on('connect', checkConnected);
    user2Socket.on('connect', checkConnected);
  });
}

function setupEventListeners() {
  // 监听连接事件
  user1Socket.on('connect', () => {
    console.log('🔌 User1 已连接');
  });

  user2Socket.on('connect', () => {
    console.log('🔌 User2 已连接');
  });

  user1Socket.on('disconnect', () => {
    console.log('🔌 User1 已断开连接');
  });

  user2Socket.on('disconnect', () => {
    console.log('🔌 User2 已断开连接');
  });

  // 监听聊天消息
  user1Socket.on('chat_message', (message) => {
    console.log('💬 User1 收到聊天消息:', {
      username: message.username,
      message: message.message,
      isSystem: message.isSystem || false
    });
    chatMessages.push(message);
  });

  user2Socket.on('chat_message', (message) => {
    console.log('💬 User2 收到聊天消息:', {
      username: message.username,
      message: message.message,
      isSystem: message.isSystem || false
    });
    chatMessages.push(message);
  });

  // 监听游戏结束事件
  user1Socket.on('game_end', (data) => {
    console.log('🏁 User1 收到游戏结束事件:', {
      hasResult: !!data.result,
      totalPot: data.result?.totalPot,
      winnersCount: data.result?.winners?.length
    });
  });

  user2Socket.on('game_end', (data) => {
    console.log('🏁 User2 收到游戏结束事件:', {
      hasResult: !!data.result,
      totalPot: data.result?.totalPot,
      winnersCount: data.result?.winners?.length
    });
  });

  // 监听其他事件
  user1Socket.on('notification', (notification) => {
    console.log('📢 User1 通知:', notification.title, '-', notification.message);
  });

  user2Socket.on('notification', (notification) => {
    console.log('📢 User2 通知:', notification.title, '-', notification.message);
  });

  user1Socket.on('room_update', (room) => {
    console.log('👤 User1 收到房间更新:', {
      status: room.status,
      seatedPlayers: room.seatedPlayers?.length || 0,
      gameStartCountdown: room.gameStartCountdown
    });
  });

  user2Socket.on('room_update', (room) => {
    console.log('👤 User2 收到房间更新:', {
      status: room.status,
      seatedPlayers: room.seatedPlayers?.length || 0,
      gameStartCountdown: room.gameStartCountdown
    });
  });

  user1Socket.on('deal_cards', (data) => {
    console.log('🃏 User1 收到发牌:', data.cards.length, '张牌');
  });

  user2Socket.on('deal_cards', (data) => {
    console.log('🃏 User2 收到发牌:', data.cards.length, '张牌');
  });

  user1Socket.on('player_show_cards', (data) => {
    console.log('🎴 User1 收到开牌事件:', {
      username: data.username,
      position: data.position,
      cards: data.cards
    });
  });

  user2Socket.on('player_show_cards', (data) => {
    console.log('🎴 User2 收到开牌事件:', {
      username: data.username,
      position: data.position,
      cards: data.cards
    });
  });
}

async function runTest() {
  try {
    await connectUsers();

    console.log('📍 步骤1: 加入房间');
    console.log('发送 join_room 事件...');
    user1Socket.emit('join_room', { gameType: 'sangong' });
    user2Socket.emit('join_room', { gameType: 'sangong' });
    await sleep(1000);

    console.log('\n📍 步骤2: 用户坐下');
    console.log('发送 sit_down 事件...');
    user1Socket.emit('sit_down', { seatNumber: 1 });
    await sleep(1000);
    user2Socket.emit('sit_down', { seatNumber: 2 });
    await sleep(2000);

    console.log('\n📍 步骤3: 等待自动开始游戏 (5秒倒计时)');
    await sleep(6000);

    console.log('\n📍 步骤4: 用户下注');
    user1Socket.emit('place_bet', { amount: 50 });
    user2Socket.emit('place_bet', { amount: 100 });
    await sleep(3000);

    console.log('\n📍 步骤5: 等待发牌阶段');
    await sleep(3000);

    console.log('\n📍 步骤6: 测试开牌功能');
    console.log('🎴 User1 开牌...');
    user1Socket.emit('show_cards');
    await sleep(3000);

    console.log('🎴 User2 开牌...');
    user2Socket.emit('show_cards');
    await sleep(5000);

    console.log('\n📍 步骤7: 等待游戏结算');
    await sleep(5000);

    console.log('\n✅ 聊天统计功能测试完成！');
    console.log('\n📊 聊天消息统计:');
    console.log(`总消息数: ${chatMessages.length}`);
    
    const systemMessages = chatMessages.filter(msg => msg.isSystem);
    console.log(`系统消息数: ${systemMessages.length}`);
    
    if (systemMessages.length > 0) {
      console.log('\n系统消息列表:');
      systemMessages.forEach((msg, index) => {
        console.log(`${index + 1}. ${msg.message}`);
      });
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    console.log('\n🔌 准备断开连接');
    await sleep(2000); // 等待2秒确保所有事件处理完成
    console.log('🔌 连接已断开');
    user1Socket?.disconnect();
    user2Socket?.disconnect();
    await sleep(1000); // 等待断开连接完成
    process.exit(0);
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行测试
runTest();
