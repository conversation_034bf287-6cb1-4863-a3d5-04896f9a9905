const mysql = require('mysql2/promise');

async function resetRoom() {
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    user: 'root',
    password: 'root',
    database: 'games'
  });

  try {
    // 重置房间状态
    await connection.execute(`
      UPDATE game_rooms 
      SET status = 'waiting', 
          seats = JSON_ARRAY(
            JSON_OBJECT('position', 1, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY()),
            JSON_OBJECT('position', 2, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY()),
            JSON_OBJECT('position', 3, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY()),
            JSON_OBJECT('position', 4, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY()),
            JSON_OBJECT('position', 5, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY()),
            JSON_OBJECT('position', 6, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY()),
            JSON_OBJECT('position', 7, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY()),
            JSON_OBJECT('position', 8, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY()),
            JSON_OBJECT('position', 9, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY()),
            JSON_OBJECT('position', 10, 'userId', NULL, 'username', NULL, 'balance', 0, 'bet', 0, 'isReady', false, 'cards', JSON_ARRAY(), 'sideWagers', JSON_ARRAY())
          )
      WHERE game_type = 'sangong'
    `);

    // 检查结果
    const [rows] = await connection.execute(
      'SELECT game_type, status FROM game_rooms WHERE game_type = ?',
      ['sangong']
    );

    console.log('✅ Room reset successfully');
    console.log(`🎮 Status: ${rows[0].status}`);
    console.log('🪑 All seats cleared');

  } catch (error) {
    console.error('❌ Reset failed:', error);
  } finally {
    await connection.end();
  }
}

resetRoom();
