# 依赖目录
node_modules/
/dist
/.pnp
.pnp.js

# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 环境变量和配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 编辑器目录和文件
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# 前端构建输出
/frontend/build/
/frontend/dist/
/frontend/.next/
/frontend/*/build/
/frontend/*/.next/
/frontend/*/out/
/frontend/*/.nuxt/
/frontend/out/
/frontend/.nuxt/
/frontend/storybook-static/
/frontend/coverage/

# Next.js 特定文件
.next/
**/.next/
**/.next/**/
out/
**/out/
.vercel
.env*.local

# 后端构建输出
/backend/dist/
/backend/build/
/backend/coverage/
/backend/__pycache__/
/backend/**/__pycache__/
/backend/**/*.py[cod]
/backend/**/*.so
/backend/.pytest_cache/
/backend/.coverage

# Docker相关
.dockerignore
docker-compose.override.yml

# 临时文件
/tmp/
*.tmp
*.bak
*.swp
*~.nib

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 包管理器锁文件（根据需要取消注释）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# 本地数据库文件
*.sqlite
*.sqlite3
*.db

# 缓存目录
.cache/
.parcel-cache/
.npm/
.eslintcache
.stylelintcache

# 测试覆盖率报告
coverage/
.nyc_output/

# 构建工具配置（根据需要取消注释）
# .grunt/
# .webpack/
# bower_components/

# 其他常见忽略项
*.pid
*.seed
*.pid.lock
.lock-wscript
.yarn-integrity
.env.test
.env.production
bfg.jar
*.icloud