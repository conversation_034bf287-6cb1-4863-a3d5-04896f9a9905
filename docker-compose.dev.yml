version: '3.8'

services:
  # 开发环境API服务
  api:
    build:
      context: ./backend/api
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
      DEBUG: "games:*"
    volumes:
      - ./backend/api/src:/app/src
      - ./backend/api/logs:/app/logs
    command: npm run dev

  # 开发环境WebSocket服务
  websocket:
    build:
      context: ./backend/websocket
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
      DEBUG: "games:*"
    volumes:
      - ./backend/websocket/src:/app/src
      - ./backend/websocket/logs:/app/logs
    command: npm run dev

  # 开发环境管理后台
  admin:
    build:
      context: ./frontend/admin
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
    volumes:
      - ./frontend/admin/src:/app/src
      - ./frontend/admin/public:/app/public
    command: npm run dev

  # 开发环境代理端
  agent:
    build:
      context: ./frontend/agent
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
    volumes:
      - ./frontend/agent/src:/app/src
      - ./frontend/agent/public:/app/public
    command: npm run dev

  # 开发环境玩家端
  player:
    build:
      context: ./frontend/player
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
    volumes:
      - ./frontend/player/src:/app/src
      - ./frontend/player/public:/app/public
    command: npm run dev

  # MySQL开发配置
  mysql:
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: games_dev
    ports:
      - "3306:3306"

  # Redis开发配置
  redis:
    ports:
      - "6379:6379"
