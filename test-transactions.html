<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易记录测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .positive { color: green; }
        .negative { color: red; }
        .loading { text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <h1>交易记录测试页面</h1>
    <div id="loading" class="loading">正在加载交易记录...</div>
    <div id="content" style="display: none;">
        <h2>交易记录列表</h2>
        <table id="transactionsTable">
            <thead>
                <tr>
                    <th>交易ID</th>
                    <th>用户/代理</th>
                    <th>类型</th>
                    <th>金额</th>
                    <th>余额变化</th>
                    <th>状态</th>
                    <th>描述</th>
                    <th>时间</th>
                </tr>
            </thead>
            <tbody id="transactionsBody">
            </tbody>
        </table>
        <div id="pagination"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJhZG1pbi1zdXBlci0wMDEiLCJ1c2VyVHlwZSI6ImFkbWluIiwidXNlcm5hbWUiOiJzdXBlcmFkbWluIiwic2Vzc2lvbklkIjoiZjBiNDJmMDMtOTY4Mi00MGUzLWFmNGMtOWU5M2Y4ZDVlYzJiIiwiaWF0IjoxNzUyMzMwODIxLCJleHAiOjE3NTI5MzU2MjEsImF1ZCI6ImdhbWVzLXVzZXJzIiwiaXNzIjoiZ2FtZXMtcGxhdGZvcm0ifQ.1AJMvlE20sAzjkPNQiAaeqpe0CyjO2yJcnXL7os3kfc';

        const typeNames = {
            deposit: '充值',
            withdraw: '提现',
            bet: '下注',
            win: '赢取',
            commission: '佣金',
            transfer: '转账'
        };

        async function loadTransactions() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/transactions?limit=10`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success) {
                    displayTransactions(data.data.transactions);
                    displayPagination(data.data.pagination);
                } else {
                    throw new Error(data.error || '获取数据失败');
                }
            } catch (error) {
                console.error('加载交易记录失败:', error);
                document.getElementById('loading').innerHTML = `<p style="color: red;">加载失败: ${error.message}</p>`;
            }
        }

        function displayTransactions(transactions) {
            const tbody = document.getElementById('transactionsBody');
            tbody.innerHTML = '';

            transactions.forEach(transaction => {
                const row = document.createElement('tr');
                const amount = parseFloat(transaction.amount);
                const amountClass = amount >= 0 ? 'positive' : 'negative';
                const amountText = amount >= 0 ? `+¥${amount.toLocaleString()}` : `¥${amount.toLocaleString()}`;

                row.innerHTML = `
                    <td>${transaction.id.slice(0, 8)}...</td>
                    <td>
                        ${transaction.user_username || transaction.agent_username || '-'}
                        <br><small>${transaction.user_username ? '用户' : transaction.agent_username ? '代理' : '-'}</small>
                    </td>
                    <td>${typeNames[transaction.type] || transaction.type}</td>
                    <td class="${amountClass}">${amountText}</td>
                    <td>
                        前: ¥${parseFloat(transaction.balance_before).toLocaleString()}<br>
                        后: ¥${parseFloat(transaction.balance_after).toLocaleString()}
                    </td>
                    <td>${transaction.status}</td>
                    <td>${transaction.description || '-'}</td>
                    <td>${new Date(transaction.created_at).toLocaleString()}</td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
        }

        function displayPagination(pagination) {
            const paginationDiv = document.getElementById('pagination');
            paginationDiv.innerHTML = `
                <p>显示第 ${pagination.page} 页，共 ${pagination.totalPages} 页 (总计 ${pagination.total} 条记录)</p>
            `;
        }

        // 页面加载时执行
        window.onload = loadTransactions;
    </script>
</body>
</html>
