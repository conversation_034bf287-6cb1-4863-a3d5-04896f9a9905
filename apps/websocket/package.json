{"name": "@card-game/websocket", "version": "1.0.0", "description": "游戏平台WebSocket服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"@card-game/shared": "^1.0.0", "@card-game/game-engine": "^1.0.0", "socket.io": "^4.7.4", "express": "^4.18.2", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "jest": "^29.7.0", "@types/jest": "^29.5.8", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}}