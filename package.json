{"name": "games-platform", "version": "1.0.0", "description": "多端游戏平台 - 三公和牛牛", "private": true, "workspaces": ["backend/api", "backend/websocket", "backend/shared", "frontend/admin", "frontend/agent", "frontend/player"], "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:ws\" \"npm run dev:admin\" \"npm run dev:agent\" \"npm run dev:player\"", "dev:api": "cd backend/api && npm run dev", "dev:ws": "cd backend/websocket && npm run dev", "dev:admin": "cd frontend/admin && npm run dev", "dev:agent": "cd frontend/agent && npm run dev", "dev:player": "cd frontend/player && npm run dev", "build": "npm run build:shared && npm run build:api && npm run build:ws && npm run build:admin && npm run build:agent && npm run build:player", "build:shared": "cd backend/shared && npm run build", "build:api": "cd backend/api && npm run build", "build:ws": "cd backend/websocket && npm run build", "build:admin": "cd frontend/admin && npm run build", "build:agent": "cd frontend/agent && npm run build", "build:player": "cd frontend/player && npm run build", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend/api && npm install && cd ../websocket && npm install && cd ../shared && npm install", "install:frontend": "cd frontend/admin && npm install && cd ../agent && npm install && cd ../player && npm install", "db:init": "cd backend/database && node init.js", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend/api && npm test && cd ../websocket && npm test", "test:frontend": "cd frontend/admin && npm test && cd ../agent && npm test && cd ../player && npm test"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}