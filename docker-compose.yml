version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: games_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: games
      MYSQL_USER: games_user
      MYSQL_PASSWORD: games_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - games_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: games_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - games_network

  # API服务
  api:
    build:
      context: ./backend/api
      dockerfile: Dockerfile
    container_name: games_api
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: games_user
      DB_PASSWORD: games_password
      DB_NAME: games
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-in-production
    ports:
      - "3001:3001"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./backend/api/logs:/app/logs
    networks:
      - games_network

  # WebSocket游戏服务
  websocket:
    build:
      context: ./backend/websocket
      dockerfile: Dockerfile
    container_name: games_websocket
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3002
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: games_user
      DB_PASSWORD: games_password
      DB_NAME: games
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
    ports:
      - "3002:3002"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./backend/websocket/logs:/app/logs
    networks:
      - games_network

  # 管理后台
  admin:
    build:
      context: ./frontend/admin
      dockerfile: Dockerfile
    container_name: games_admin
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://localhost:3001/api
      NEXT_PUBLIC_WS_URL: http://localhost:3002
    ports:
      - "3000:3000"
    depends_on:
      - api
      - websocket
    networks:
      - games_network

  # 代理端
  agent:
    build:
      context: ./frontend/agent
      dockerfile: Dockerfile
    container_name: games_agent
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://localhost:3001/api
      NEXT_PUBLIC_WS_URL: http://localhost:3002
    ports:
      - "3003:3003"
    depends_on:
      - api
      - websocket
    networks:
      - games_network

  # 玩家端
  player:
    build:
      context: ./frontend/player
      dockerfile: Dockerfile
    container_name: games_player
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://localhost:3001/api
      NEXT_PUBLIC_WS_URL: http://localhost:3002
    ports:
      - "3004:3004"
    depends_on:
      - api
      - websocket
    networks:
      - games_network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: games_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - admin
      - agent
      - player
      - api
      - websocket
    networks:
      - games_network

volumes:
  mysql_data:
  redis_data:

networks:
  games_network:
    driver: bridge
