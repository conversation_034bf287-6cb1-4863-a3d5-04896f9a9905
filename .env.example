# 环境配置
NODE_ENV=development

# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=games

# MySQL生产环境配置
MYSQL_ROOT_PASSWORD=your-super-secure-root-password
MYSQL_USER=games_user
MYSQL_PASSWORD=your-super-secure-user-password

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# 服务端口
API_PORT=3001
WEBSOCKET_PORT=3002
ADMIN_PORT=3000
AGENT_PORT=3003
PLAYER_PORT=3004

# CORS配置
CORS_ORIGIN=http://localhost:3000,http://localhost:3003,http://localhost:3004

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 游戏配置
GAME_MIN_BET=10
GAME_MAX_BET=10000
GAME_MAX_SEATS=10
GAME_BETTING_TIME=30
GAME_DEALING_TIME=5
GAME_SETTLING_TIME=10

# 邮件配置（可选）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM=<EMAIL>

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif

# 安全配置
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# 监控配置（可选）
SENTRY_DSN=your-sentry-dsn
PROMETHEUS_PORT=9090

# SSL配置（生产环境）
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
